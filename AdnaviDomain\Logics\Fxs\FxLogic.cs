using System.Globalization;
using Adnavi.Domain.DTOs.Fxs;
using Adnavi.Domain.Logics.Common.Images;
using Adnavi.Domain.Models;
using Adnavi.Domain.Models.Fxs;
using Adnavi.Utils;
using Adnavi.Utils.Exceptions;
using Adnavi.Utils.Images;
using CsvHelper.Configuration;
using Microsoft.EntityFrameworkCore;
using NUlid;

namespace Adnavi.Domain.Logics.Fxs;

public class FxLogic
{
    private readonly AdnaviDomainContext _context;
    private readonly ModelUtils _model;
    private readonly ImageService _imageService;

    public FxLogic(
        AdnaviDomainContext context,
        ModelUtils model,
        ImageService imageService
    )
    {
        _context = context;
        _context.ChangeTracker.LazyLoadingEnabled = false;
        _model = model;
        _imageService = imageService;
    }

    private IQueryable<Fx> GetQuery(FxFilterRequest request)
    {
        var query = _context.Fxs
            .Include(fx => fx.FxRecommendedPoints)
            .Include(fx => fx.FxPromotions)
            .AsNoTracking()
            .AsQueryable();
        if (request.Name != null)
        {
            query = query.Where(fx => fx.Name.Contains(request.Name));
        }

        return query;
    }

    private IQueryable<Fx> GetQuery(FxSearchRequest request)
    {
        var query = _context.Fxs
            .Include(fx => fx.FxRecommendedPoints)
            .Include(fx => fx.FxPromotions)
            .AsNoTracking()
            .AsQueryable();

        if (request.MinOrderUnits != null)
        {
            switch (request.MinOrderUnits)
            {
                case MinOrderUnits.OneOrLess:
                    query = query.Where(fx => fx.MinTradeUnit <= 1);
                    break;
                case MinOrderUnits.ThousandOrLess:
                    query = query.Where(fx => fx.MinTradeUnit <= 10000);
                    break;
            }
        }

        if (request.MaxCashbackAmount != null)
        {
            switch (request.MaxCashbackAmount)
            {
                case MaxCashbackAmounts.TenOrMore:
                    query = query.Where(fx => fx.MaxCashBackAmount >= 100000);
                    break;
                case MaxCashbackAmounts.HundredOrMore:
                    query = query.Where(fx => fx.MaxCashBackAmount >= 1000000);
                    break;
            }
        }

        if (request.AppUsability == true)
        {
            query = query.Where(
                fx => fx.AppComfortableTextAssessmentPoint >= 3
            );
        }

        if (request.DemoAvailability == true)
        {
            query = query.Where(
                fx =>
                    fx.DemoTradePeriodDays != null && fx.DemoTradePeriodDays > 0
            );
        }
        if (request.TwentyFourHoursSupport == true)
        {
            query = query.Where(
                fx =>
                    fx.TwentyFourHoursSupport.HasValue
                    && fx.TwentyFourHoursSupport.Value
            );
        }

        if (request.NewsEnriched == true)
        {
            query = query.Where(
                fx => fx.NewsEnriched.HasValue && fx.NewsEnriched.Value
            );
        }

        if (request.AutomatedTrade == true)
        {
            query = query.Where(
                fx => fx.AutomatedTrade.HasValue && fx.AutomatedTrade.Value
            );
        }

        if (request.Promotions == true)
        {
            query = query.Where(
                fx =>
                    fx.FxPromotions.Any(
                        p =>
                            (
                                p.StartDate != null
                                && p.EndDate != null
                                && p.StartDate
                                    <= DateOnly.FromDateTime(DateTime.UtcNow)
                                && p.EndDate
                                    >= DateOnly.FromDateTime(DateTime.UtcNow)
                            ) || (p.StartDate == null && p.EndDate == null)
                    )
            );
        }

        return query;
    }

    private IQueryable<Fx> GetPaginatedQuery(FxSearchRequest request)
    {
        var query = GetQuery(request);

        if (request.Offset.HasValue)
        {
            query = query.Skip((int)request.Offset.Value);
        }

        if (request.Count.HasValue)
        {
            query = query.Take((int)request.Count.Value);
        }

        return query;
    }

    public IQueryable<Fx> SearchFxs(FxSearchRequest request)
    {
        var query = GetPaginatedQuery(request);
        if (request.OrderColumn != null)
        {
            switch (request.OrderColumn)
            {
                case FxOrderColumns.Id:
                    query =
                        request.Descendence == true
                            ? query.OrderByDescending(fx => fx.Id)
                            : query.OrderBy(fx => fx.Id);
                    break;
                case FxOrderColumns.Name:
                    query =
                        request.Descendence == true
                            ? query.OrderByDescending(fx => fx.Name)
                            : query.OrderBy(fx => fx.Name);
                    break;
                case FxOrderColumns.ModifiedTime:
                    query =
                        request.Descendence == true
                            ? query.OrderByDescending(
                                fx => fx.ModifiedTime ?? fx.CreatedTime
                            )
                            : query.OrderBy(
                                fx => fx.ModifiedTime ?? fx.CreatedTime
                            );
                    break;

                case FxOrderColumns.Recommendation:
                    query = OrderByRecommendation(query, request);
                    break;

                case FxOrderColumns.MinTradeUnit:
                    query =
                        request.Descendence == true
                            ? query.OrderByDescending(
                                fx =>
                                    fx.MinTradeUnit != null
                                        ? fx.MinTradeUnit
                                        : 0
                            )
                            : query.OrderBy(
                                fx =>
                                    fx.MinTradeUnit != null
                                        ? fx.MinTradeUnit
                                        : int.MaxValue
                            );
                    break;
                case FxOrderColumns.AppComfortableText:
                    query =
                        request.Descendence == true
                            ? query.OrderByDescending(
                                fx =>
                                    fx.AppComfortableTextAssessmentPoint != null
                                        ? fx.AppComfortableTextAssessmentPoint
                                        : 0
                            )
                            : query.OrderBy(
                                fx =>
                                    fx.AppComfortableTextAssessmentPoint != null
                                        ? fx.AppComfortableTextAssessmentPoint
                                        : int.MaxValue
                            );
                    break;
                case FxOrderColumns.DemoTradePeriodDays:
                    query =
                        request.Descendence == true
                            ? query.OrderByDescending(
                                fx => fx.DemoTradePeriodDays ?? 0
                            )
                            : query.OrderBy(
                                fx => fx.DemoTradePeriodDays ?? int.MaxValue
                            );
                    break;
                case FxOrderColumns.MaxCashBackAmount:
                    query =
                        request.Descendence == true
                            ? query.OrderByDescending(
                                fx =>
                                    fx.MaxCashBackAmount != null
                                        ? fx.MaxCashBackAmount
                                        : 0
                            )
                            : query.OrderBy(
                                fx =>
                                    fx.MaxCashBackAmount != null
                                        ? fx.MaxCashBackAmount
                                        : int.MaxValue
                            );
                    break;
                case FxOrderColumns.UsdJpySpread:
                    query =
                        request.Descendence == true
                            ? query.OrderByDescending(
                                fx =>
                                    fx.UsdJpySpread != null
                                        ? fx.UsdJpySpread
                                        : 0
                            )
                            : query.OrderBy(
                                fx =>
                                    fx.UsdJpySpread != null
                                        ? fx.UsdJpySpread
                                        : decimal.MaxValue
                            );
                    break;
                case FxOrderColumns.EurJpySpread:
                    query =
                        request.Descendence == true
                            ? query.OrderByDescending(
                                fx =>
                                    fx.EurJpySpread != null
                                        ? fx.EurJpySpread
                                        : 0
                            )
                            : query.OrderBy(
                                fx =>
                                    fx.EurJpySpread != null
                                        ? fx.EurJpySpread
                                        : decimal.MaxValue
                            );
                    break;
                case FxOrderColumns.GbpJpySpread:
                    query =
                        request.Descendence == true
                            ? query.OrderByDescending(
                                fx =>
                                    fx.GbpJpySpread != null
                                        ? fx.GbpJpySpread
                                        : 0
                            )
                            : query.OrderBy(
                                fx =>
                                    fx.GbpJpySpread != null
                                        ? fx.GbpJpySpread
                                        : decimal.MaxValue
                            );
                    break;
                case FxOrderColumns.AudJpySpread:
                    query =
                        request.Descendence == true
                            ? query.OrderByDescending(
                                fx =>
                                    fx.AudJpySpread != null
                                        ? fx.AudJpySpread
                                        : 0
                            )
                            : query.OrderBy(
                                fx =>
                                    fx.AudJpySpread != null
                                        ? fx.AudJpySpread
                                        : decimal.MaxValue
                            );
                    break;
            }
        }
        return query;
    }

    public async Task<int> GetCount(FxFilterRequest request)
    {
        return await GetQuery(request).CountAsync();
    }

    public async Task<Fx> CreateFx(StaffFxRequest request)
    {
        var fx = request.To(_model.Create(new Fx()), _model);
        await _context.SaveChangesAsync();
        return fx;
    }

    public async Task<StaffFxResponse> GetFx(Ulid id)
    {
        var fx = await _context.Fxs
            .Include(fx => fx.FxRecommendedPoints)
            .SingleOrDefaultAsync(f => f.Id == id);
        if (fx == null)
        {
            throw new NotFoundException($"FX ID {id} not found");
        }
        return StaffFxResponse.Create(fx, _context);
    }

    public async Task<Fx> UpdateFx(Ulid id, StaffFxRequest request)
    {
        var fx = await _context.Fxs
            .Include(fx => fx.FxRecommendedPoints)
            .SingleOrDefaultAsync(f => f.Id == id);
        if (fx == null)
        {
            throw new NotFoundException($"FX ID {id} not found");
        }

        fx = _model.Update(request.To(fx, _model));
        await _context.SaveChangesAsync();
        return fx;
    }

    public async Task<UploadData> GeneratePhotoUploadUrl(
        ImageFileTypes type,
        Ulid fxId
    )
    {
        return await GeneratePhotoUploadUrlHelper(
            type,
            fxId,
            fx => fx.OfficialImageFileKey,
            (fx, key) => fx.OfficialImageFileKey = key
        );
    }

    public async Task<UploadData> GenerateTopPhotoUploadUrl(
        ImageFileTypes type,
        Ulid fxId
    )
    {
        return await GeneratePhotoUploadUrlHelper(
            type,
            fxId,
            fx => fx.TopImageFileKey,
            (fx, key) => fx.TopImageFileKey = key
        );
    }

    public async Task<UploadData> GenerateCampaignPhotoUploadUrl(
        ImageFileTypes type,
        Ulid fxId
    )
    {
        return await GeneratePhotoUploadUrlHelper(
            type,
            fxId,
            fx => fx.CampaignImageFileKey,
            (fx, key) => fx.CampaignImageFileKey = key
        );
    }

    private async Task<UploadData> GeneratePhotoUploadUrlHelper(
        ImageFileTypes type,
        Ulid fxId,
        Func<Fx, Ulid?> getImageKey,
        Action<Fx, Ulid?> setImageKey
    )
    {
        var fx = await _context.Fxs.SingleOrDefaultAsync(f => f.Id == fxId);
        if (fx == null)
        {
            throw new NotFoundException($"FX ID {fxId} not found");
        }

        var existingImageKey = getImageKey(fx);
        if (existingImageKey != null)
        {
            await _imageService.DeleteImage(existingImageKey.Value);
        }

        UploadData imageData = _imageService.GenerateUploadPreSignedUrl(type);
        setImageKey(fx, imageData.ContentId);
        await _context.SaveChangesAsync();
        return imageData;
    }

    public async Task<IEnumerable<ImageSpecAndUrl>> GetPhotoUrls(
        Ulid? ImageFileKey
    )
    {
        if (ImageFileKey == null)
            return new List<ImageSpecAndUrl>();

        return await _imageService.GetConvertedImageUrls(
            ImageFileKey.Value,
            new List<ImageFileSpec>
            {
                new ImageFileSpec(ImageFileTypes.Png, 600, 600, false),
                new ImageFileSpec(ImageFileTypes.Png, 300, 300, false),
                new ImageFileSpec(ImageFileTypes.Png, 150, 150, false),
                new ImageFileSpec(ImageFileTypes.Webp, 600, 600, false),
                new ImageFileSpec(ImageFileTypes.Webp, 300, 300, false),
                new ImageFileSpec(ImageFileTypes.Webp, 150, 150, false),
            }
        );
    }

    public async Task<string> ExportToCsvFile()
    {
        var config = new CsvConfiguration(CultureInfo.InvariantCulture)
        {
            HasHeaderRecord = true
        };

        var dataQuery = FxCsvRecord.FromQuery(
            _context.Fxs.IgnoreQueryFilters().AsNoTracking()
        );

        return await CsvExportUtils.ExportToCsvFile(dataQuery.Result, config);
    }

    public IQueryable<Fx> GetTopTenPopular()
    {
        var query = _context.Fxs
            .Include(fx => fx.FxRecommendedPoints)
            .AsNoTracking()
            .OrderByDescending(fx => fx.BiasedScore)
            .Take(10)
            .AsQueryable();

        return query;
    }

    private static IOrderedQueryable<Fx> OrderByRecommendation(
        IQueryable<Fx> query,
        FxSearchRequest request
    )
    {
        if (request.Descendence == true)
        {
            return query
                .OrderByDescending(fx => fx.BiasedScore)
                .ThenByDescending(fx => fx.AppComfortableTextAssessmentPoint)
                .ThenByDescending(fx => fx.FxRecommendedPoints.Count)
                .ThenByDescending(fx => fx.CreatedTime)
                .ThenByDescending(fx => fx.TwentyFourHoursSupport)
                .ThenByDescending(fx => fx.NewsEnriched)
                .ThenByDescending(fx => fx.AutomatedTrade)
                .ThenByDescending(fx => fx.FxPromotions.Count);
        }
        else
        {
            return query
                .OrderBy(fx => fx.BiasedScore)
                .ThenBy(fx => fx.AppComfortableTextAssessmentPoint)
                .ThenBy(fx => fx.FxRecommendedPoints.Count)
                .ThenBy(fx => fx.CreatedTime)
                .ThenBy(fx => fx.TwentyFourHoursSupport)
                .ThenBy(fx => fx.NewsEnriched)
                .ThenBy(fx => fx.AutomatedTrade)
                .ThenBy(fx => fx.FxPromotions.Count);
        }
    }

    public IQueryable<Fx> GetPickUp()
    {
        var query = _context.Fxs
            .Include(fx => fx.FxRecommendedPoints)
            .AsNoTracking()
            .OrderByDescending(fx => fx.BiasedScore)
            .Where(fx => fx.PickUp);

        return query;
    }
}
