using Microsoft.AspNetCore.DataProtection;

namespace Adnavi.Domain.Logics.Profiles.AdviserProfiles;

public class TokenProtectLogic
{
    private readonly IDataProtector _protector;

    public TokenProtectLogic(IDataProtectionProvider protectionProvider)
    {
        _protector = protectionProvider.CreateProtector(
            "AdnaviDomain:TokenProtector"
        );
    }

    public string Protect(string plainText)
    {
        return string.IsNullOrEmpty(plainText)
            ? ""
            : _protector.Protect(plainText);
    }

    public string Unprotect(string protectedText)
    {
        return string.IsNullOrEmpty(protectedText)
            ? ""
            : _protector.Unprotect(protectedText);
    }
}
