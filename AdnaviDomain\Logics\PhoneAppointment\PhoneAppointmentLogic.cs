using Adnavi.Domain.Models;
using NUlid;
using Microsoft.EntityFrameworkCore;
using Adnavi.Domain.Models.PhoneAppointment;
using Adnavi.Domain.DTOs.PhoneAppointment;
using Adnavi.Utils.Exceptions;
using CsvHelper.Configuration;
using CsvHelper;
using System.Globalization;
using System.Text;
using Adnavi.Utils;
using Microsoft.Extensions.Options;
using Adnavi.Domain.Common;

namespace Adnavi.Domain.Logics.PhoneAppointment
{
    public class PhoneAppointmentLogic
    {
        private readonly ChatWorkUtils _chatWork;
        private readonly AdnaviDomainContext _context;
        private readonly ModelUtils _model;
        private readonly IOptions<AdnaviDomainSettings> _settings;

        public PhoneAppointmentLogic(
            ChatWorkUtils chatWork,
            AdnaviDomainContext context,
            ModelUtils model,
            IOptions<AdnaviDomainSettings> settings
        )
        {
            _chatWork = chatWork;
            _context = context;
            _context.ChangeTracker.LazyLoadingEnabled = false;
            _model = model;
            _settings = settings;
        }

        public async Task<Ulid> Create(PublicPhoneAppointmentSurveyRequest data)
        {
            var survey = data.To(new PhoneAppointmentSurvey());
            _model.Create(survey);
            await _context.SaveChangesAsync();

            var assetRange = _context.AssetRanges
                .Where(x => x.Id == survey.AssetRange)
                .Select(x => x.Name)
                .Single();

            string surveyTypeMessage =
                survey.PhoneAppointmentSurveyType
                == PhoneAppointmentSurveyTypes.RealEstate
                    ? "不動産に関するアンケートが送信されました"
                    : "資産運用に関するアンケートが送信されました";

            await _chatWork.TrySend(
                _settings.Value.Investors.AssetSurveyChatWorkRoomId,
                $@"{surveyTypeMessage}

回答ID: {survey.Id}
氏名: {data.FamilyName} {data.FirstName} ({data.FamilyNameKana} {data.FirstNameKana})
年齢： {data.Age}
電話番号: {data.TelephoneNumber}
メールアドレス: {data.EMail}
金融資産: {assetRange}
モチベーション: {data.ConsultationInterests}
"
            );

            return survey.Id;
        }

        public async Task Update(
            IObjectUpdater<PhoneAppointmentSurvey> data,
            Ulid surveyId
        )
        {
            var surveyData =
                await _context.PhoneAppointmentSurveys.SingleOrDefaultAsync(
                    x => x.Id == surveyId
                );
            if (surveyData != null)
            {
                var survey = data.To(surveyData);
                _model.Update(survey);
                await _context.SaveChangesAsync();
            }
        }

        // 一つのアンケート情報を取得
        public async Task<StaffPhoneAppointmentSurveyResponse> Search(Ulid id)
        {
            var survey = await StaffPhoneAppointmentSurveyResponse
                .FromQuery(
                    _context.PhoneAppointmentSurveys
                        .AsNoTracking()
                        .Where(x => x.Id == id),
                    _context
                )
                .SingleOrDefaultAsync();

            if (survey == null)
            {
                throw new NotFoundException(
                    typeof(PhoneAppointmentSurvey).Name,
                    id
                );
            }

            return survey;
        }

        // 全てのアンケート情報を取得
        public async Task<
            List<StaffPhoneAppointmentSurveyResponse>
        > SearchSurveys()
        {
            var surveys = await StaffPhoneAppointmentSurveyResponse
                .FromQuery(
                    _context.PhoneAppointmentSurveys.AsNoTracking(),
                    _context
                )
                .ToListAsync();

            return surveys;
        }

        private IQueryable<PhoneAppointmentSurvey> GetQuery(
            PhoneAppointmentFilterRequest request
        )
        {
            var query = _context.PhoneAppointmentSurveys.AsNoTracking();

            switch (request.OrderColumn)
            {
                case PhoneAppointmentOrderColumn.Status:
                    query =
                        request.Descendence == true
                            ? query.OrderByDescending(x => x.Status)
                            : query.OrderBy(x => x.Status);
                    break;
                case PhoneAppointmentOrderColumn.Age:
                    query =
                        request.Descendence == true
                            ? query.OrderByDescending(x => x.Age)
                            : query.OrderBy(x => x.Age);
                    break;
                case PhoneAppointmentOrderColumn.AssetRange:
                    query =
                        request.Descendence == true
                            ? query.OrderByDescending(x => x.AssetRange)
                            : query.OrderBy(x => x.AssetRange);
                    break;
                case PhoneAppointmentOrderColumn.ConsultationInterest:
                    query =
                        request.Descendence == true
                            ? query.OrderByDescending(
                                x => x.ConsultationInterests
                            )
                            : query.OrderBy(x => x.ConsultationInterests);
                    break;
                default:
                    query = query.OrderByDescending(x => x.CreatedTime);
                    break;
            }

            if (request.Status != null && request.Status.Any())
            {
                query = query.Where(x => request.Status.Contains(x.Status));
            }

            if (request.AssetRange != null && request.AssetRange.Any())
            {
                query = query.Where(
                    x => request.AssetRange.Contains(x.AssetRange)
                );
            }

            if (
                request.ConsultationInterests != null
                && request.ConsultationInterests.Any()
            )
            {
                query = query.Where(
                    x =>
                        request.ConsultationInterests.Any(
                            ci => ci == x.ConsultationInterests
                        )
                );
            }

            if (request.searchText != null)
            {
                query = query.Where(
                    x =>
                        x.FamilyName.Contains(request.searchText)
                        || x.FirstName.Contains(request.searchText)
                        || x.FamilyNameKana.Contains(request.searchText)
                        || x.FirstNameKana.Contains(request.searchText)
                );
            }

            if (
                request.IsPhoneNumberVerified != null
                && request.IsPhoneNumberVerified == true
            )
            {
                var verifiedPhoneNumbers = _context.PhoneNumberVerifications
                    .AsNoTracking()
                    .Where(x => x.IsVerified)
                    .Select(x => x.PhoneNumber)
                    .ToList();
                query = query.Where(
                    x => verifiedPhoneNumbers.Contains(x.TelephoneNumber)
                );
            }

            return query;
        }

        public async Task<
            List<StaffPhoneAppointmentSurveyResponse>
        > SearchSurveysSummary(PhoneAppointmentFilterRequest request)
        {
            var query = this.GetQuery(request);
            var offset = request.Offset ?? 0;
            var count = request.Count ?? 10;
            query = query.Skip((int)offset).Take((int)count);

            var surveys = await StaffPhoneAppointmentSurveyResponse
                .FromQuery(query, _context)
                .ToListAsync();

            return surveys;
        }

        public async Task<int> GetCount(
            PhoneAppointmentFilterRequest request
        ) => await GetQuery(request).AsNoTracking().CountAsync();

        public async Task<StaffPhoneAppointmentSurveyResponse> UnderTake(
            Ulid id,
            Ulid assignedStaffId
        )
        {
            var survey =
                await _context.PhoneAppointmentSurveys.SingleOrDefaultAsync(
                    x => x.Id == id
                );
            if (survey == null)
            {
                throw new NotFoundException(
                    typeof(PhoneAppointmentSurvey).Name,
                    id
                );
            }

            survey.AssignedStaffId = assignedStaffId;
            survey.Status = PhoneAppointmentStatuses.InProgress;
            _model.Update(survey);
            await _context.SaveChangesAsync();

            var investor = await Search(id);
            return investor;
        }

        public async Task UpdateStatus(
            Ulid id,
            PhoneAppointmentStatuses newStatus
        )
        {
            var survey = await _context.PhoneAppointmentSurveys.SingleAsync(
                x => x.Id == id
            );

            survey.Status = newStatus;
            _model.Update(survey);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateCallCount(Ulid id)
        {
            var survey = await _context.PhoneAppointmentSurveys.SingleAsync(
                x => x.Id == id
            );
            if (survey == null)
            {
                throw new NotFoundException(
                    typeof(PhoneAppointmentSurvey).Name,
                    id
                );
            }

            survey.CallCount++;
            _model.Update(survey);
            await _context.SaveChangesAsync();
        }

        public async Task<MemoryStream> StaffOutputCsvOnStatus()
        {
            var config = new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                HasHeaderRecord = true
            };

            var dataExport = PhoneAppointmentCsvRecord.FromQuery(_context);

            var stream = new MemoryStream();
            using (
                var writeFile = new StreamWriter(
                    stream,
                    Encoding.UTF8,
                    -1,
                    leaveOpen: true
                )
            )
            {
                var csv = new CsvWriter(writeFile, config);
                csv.WriteHeader<PhoneAppointmentCsvRecord>();
                await csv.NextRecordAsync();
                await foreach (var record in dataExport)
                {
                    csv.WriteRecord(record);
                    await csv.NextRecordAsync();
                }
            }
            stream.Position = 0; // reset stream

            return stream;
        }

        public async Task<StaffPhoneAppointmentSurveyResponse> SearchSurvey(
            Ulid id
        )
        {
            return await StaffPhoneAppointmentSurveyResponse
                .FromQuery(
                    _context.PhoneAppointmentSurveys.Where(x => x.Id == id),
                    _context
                )
                .SingleAsync();
        }
    }
}
