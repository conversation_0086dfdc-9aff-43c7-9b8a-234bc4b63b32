using Adnavi.Utils.Models;
using NUlid;

namespace Adnavi.Domain.Models.Profiles.AdviserProfiles;

public class AdviserPhotoCollection : IHasId
{
    /// <summary>写真ID</summary>
    public Ulid Id { get; set; }

    /// <summary>アドバイザーID</summary>
    public Ulid AdviserId { get; set; }

    /// <summary>写真ファイル名</summary>
    public string? PhotoFileKey { get; set; }

    /// <summary>新しい写真ファイルID</summary>
    public Ulid? NewPhotoFileKey { get; set; }
}
