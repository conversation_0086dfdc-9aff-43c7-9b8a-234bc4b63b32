﻿using System.ComponentModel.DataAnnotations;
using Adnavi.Utils.Models;
using NUlid;

namespace Adnavi.Domain.Models.Invoices.RecruitInvoices;

/// <summary>
/// EmployeeProfit
/// /// </summary>
/// <seealso cref="UpdateEmployeeProfitRequest" />
public class EmployeeProfit : IHasId
{
    /// <summary>ID</summary>
    [Required]
    public Ulid Id { get; set; }

    /// <summary>バージョン</summary>
    [Timestamp]
    public byte[]? RowVersion { get; set; }

    /// <summary>請求書ID</summary>
    [Required]
    public Ulid RecruiterInvoiceId { get; set; }

    /// <summary>請求書</summary>
    public virtual RecruiterInvoice RecruiterInvoice { get; set; }

    /// <summary>雇用ID</summary>
    [Required]
    public Ulid EmploymentId { get; set; }

    /// <summary>雇用</summary>
    public virtual Employment Employment { get; set; }

    /// <summary>料率(%)</summary>
    [Required]
    public byte FeeRatePercent { get; set; }

    /// <summary>金額</summary>
    public uint? Amount { get; set; }
}
