using Adnavi.Domain.Models.Commons;
using Adnavi.Domain.Models.Consultations.BatchIntroductions;
using Adnavi.Domain.Models.Profiles.AdviserProfiles;
using Adnavi.Domain.Models.Profiles.IfaCompanyProfiles;
using NUlid;

namespace Adnavi.Domain.Logics.Consultations.BatchIntroductions;

public class AdviserRequirementsCheck
{
    public Ulid AdviserId { get; set; }
    public ICollection<Day>? WorkDays { get; set; }
    public GenderTypes? Gender { get; set; }
    public CustomerStatics? CustomerStatics { get; set; }
    public ICollection<SecuritiesCompany>? CooperativeSecurities { get; set; }
    public Ulid? IfaCompanyId { get; set; }
    public int ClosingHour { get; set; }
    public BatchIntroductionSetting? BatchIntroductionSetting { get; set; }

    public AdviserRequirementsCheck() { }

    public static IQueryable<AdviserRequirementsCheck> FromQuery(
        IQueryable<Adviser> advisers
    )
    {
        return advisers.Select(
            a =>
                new AdviserRequirementsCheck
                {
                    AdviserId = a.Id,
                    WorkDays =
                        a.AdditionalProfileItems == null
                            ? null
                            : a.AdditionalProfileItems.WorkDays,
                    Gender =
                        a.AdditionalProfileItems == null
                            ? null
                            : a.AdditionalProfileItems.Gender,
                    CustomerStatics = a.CustomerStatics,
                    CooperativeSecurities =
                        a.IfaCompany == null
                            ? null
                            : a.IfaCompany.CooperativeSecurities,
                    IfaCompanyId = a.IfaCompany == null ? null : a.IfaCompanyId,
                    ClosingHour = a.ClosingHour,
                    BatchIntroductionSetting = a.BatchIntroductionSetting
                }
        );
    }
}
