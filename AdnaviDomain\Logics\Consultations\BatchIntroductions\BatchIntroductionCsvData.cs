﻿using Adnavi.Domain.Models.Commons;
using Adnavi.Domain.Models.Consultations.BatchIntroductions;
using Adnavi.Domain.Models.Consultations.ConsultationRequests;
using Adnavi.Domain.Models.Profiles.InvestorProfiles;
using CsvHelper.Configuration.Attributes;

namespace Adnavi.Domain.Logics.Consultations.BatchIntroductions;

public class BatchIntroductionCsvData
{
    [Name("#")]
    public string Id { get; set; }

    [Name("リード獲得日")]
    public DateTime CreatedTime { get; set; }

    [Name("氏名")]
    public string Name { get; set; }

    [Name("年齢")]
    public int Age { get; set; }

    [Name("年収")]
    public AnnualIncomes? AnnualIncome { get; set; }

    [Name("電話番号")]
    public string TelephoneNumber { get; set; }

    [Name("メールアドレス")]
    public string EMail { get; set; }

    [Name("区分")]
    public string OriginName { get; set; }

    [Name("流入詳細（記入：URL貼り付け）")]
    public string OriginUrl { get; set; }
    public string? Advsn { get; set; }
    public string? UtmSource { get; set; }
    public string? UtmMedium { get; set; }
    public string? UtmCampaign { get; set; }
    public string? UtmContent { get; set; }

    [Name("IFA申し込み")]
    public BatchIntroductionStatus Status { get; set; }

    [Name("金融資産範囲")]
    public string AssetRange { get; set; }
    public ConsultationRequestTypes ConsultationRequestType { get; set; }
    public string Furigana { get; set; }
    public GenderTypes? Gender { get; set; }
    public Prefectures Prefecture { get; set; }
    public string PostalCode { get; set; }
    public string Address { get; set; }
    public string AdviserWorkTypes { get; set; }
    public InvestmentPurposes? InvestmentPurpose { get; set; }
    public string ContactMethods { get; set; }
    public string Remarks { get; set; }

    [Name("職業")]
    public string InvestorOccupationType { get; set; }

    public string ConsultationTypes { get; set; }
    public bool AcceptPrivacy { get; set; }
    public bool AcceptMailMagazine { get; set; }
    public string Advisers { get; set; }
    public string RemoteHost { get; set; }

    [Name("IFA申し込み人数")]
    public int AdvisersCount { get; set; }

    [Name("送客IFA①")]
    public string Adviser0Name { get; set; }

    [Name("IFA所属会社①")]
    public string Adviser0IfaCompanyName { get; set; }

    [Name("送客IFA②")]
    public string Adviser1Name { get; set; }

    [Name("IFA所属会社②")]
    public string Adviser1IfaCompanyName { get; set; }

    [Name("送客IFA③")]
    public string Adviser2Name { get; set; }

    [Name("IFA所属会社③")]
    public string Adviser2IfaCompanyName { get; set; }

    [Name("送客IFA④")]
    public string Adviser3Name { get; set; }

    [Name("IFA所属会社④")]
    public string Adviser3IfaCompanyName { get; set; }

    public string? UtmTerm { get; set; }

    public string? RegisterEmail { get; set; }
}
