using Adnavi.Domain.Common;
using AngleSharp.Common;
using System.Collections.Specialized;

namespace Adnavi.Domain.Models.PhoneAppointment;

public class PhoneAppointmentStatusesDefs : IDefinitionExport
{
    public string Name => GetType().Name.Replace("Defs", "");
    public string ValueType => nameof(PhoneAppointmentStatuses);

    public IEnumerable<string> DefinitionNames =>
        new List<string> { $"dict/ja-jp/{Name}", $"list/{Name}" };

    public bool DictMatched(string name) => name == $"dict/ja-jp/{Name}";

    public bool ListMatched(string name) => name == $"list/{Name}";

    public List<(string, string)> GetDefList()
    {
        return new List<(string, string)>
        {
            (nameof(PhoneAppointmentStatuses.NotSupported), "未対応"),
            (nameof(PhoneAppointmentStatuses.InProgress), "対応中"),
            (nameof(PhoneAppointmentStatuses.NotAnswering), "電話出ない"),
            (nameof(PhoneAppointmentStatuses.CallLater), "後で電話"),
            (nameof(PhoneAppointmentStatuses.MailSent), "メール送信済"),
            (nameof(PhoneAppointmentStatuses.UnderConsideration), "検討中"),
            (nameof(PhoneAppointmentStatuses.CompletedRefused), "完了（お断り）"),
            (nameof(PhoneAppointmentStatuses.CompletedAppointment), "完了（アポ完了）"),
        };
    }

    public object? GetDict() =>
        GetDefList().ToDictionary(i => i.Item1, i => i.Item2);

    public object? GetList() => GetDefList().Select(i => i.Item1).ToList();
}
