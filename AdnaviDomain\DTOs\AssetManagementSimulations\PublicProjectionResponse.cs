using System.ComponentModel.DataAnnotations;
using Adnavi.Utils.Models;

namespace Adnavi.Domain.DTOs.AssetManagementSimulations;

public class PublicProjectionResponse : IDataTransferObject
{
    [Required]
    public int Year { get; set; }

    [Required]
    public double Expected { get; set; }

    [Required]
    public double Minus2Sigma { get; set; }

    [Required]
    public double Minus1Sigma { get; set; }

    [Required]
    public double Plus1Sigma { get; set; }

    [Required]
    public double Plus2Sigma { get; set; }
}
