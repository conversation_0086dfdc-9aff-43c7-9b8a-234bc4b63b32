using Adnavi.Utils.Exceptions;
using Adnavi.Domain.Models.Commons.PhoneNumbers;

namespace Adnavi.Domain.Logics.Common.PhoneNumbers;

public class PhoneNumberVerificationExceededError : BadRequestException
{
    public string PhoneNumber { get; }
    public VerificationSourceTypes? VerificationSourceType { get; }

    public PhoneNumberVerificationExceededError(
        string phoneNumber,
        VerificationSourceTypes? verificationSourceType
    )
    {
        PhoneNumber = phoneNumber;
        VerificationSourceType = verificationSourceType;
    }

    public override object AdditionalInformation =>
        new
        {
            Message,
            PhoneNumber,
            VerificationSourceType
        };
}
