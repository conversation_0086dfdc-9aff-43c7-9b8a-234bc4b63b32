using Adnavi.Domain.Models.Profiles.AdviserProfiles;
using Adnavi.Domain.Models.Profiles.IfaCompanyProfiles;
using Adnavi.Domain.Models.Profiles.InvestorProfiles;
using Adnavi.Utils.Models;
using Microsoft.EntityFrameworkCore;
using NUlid;

namespace Adnavi.Domain.Models.Consultations.BatchIntroductions;

/// <summary>
/// BatchIntroductionIfaCompanySetting
/// /// </summary>
/// <seealso cref="BatchIntroductionIfaCompanySettingData" />
public class BatchIntroductionIfaCompanySetting : IHasId, IInvestorFilterFields
{
    public Ulid Id { get; set; }

    public Ulid IfaCompanyId { get; set; }

    public virtual IfaCompany IfaCompany { get; set; }

    public virtual ICollection<AssetRange> AcceptAssetRanges { get; set; } =
        new List<AssetRange>();

    public bool NotCompanySettingRange { get; set; }

    /// <summary>一括紹介の有効化</summary>
    public bool Enabled { get; set; }

    public uint? OlderLimit { get; set; }

    public uint? YoungerLimit { get; set; }

    public virtual ICollection<InvestorOccupationType> ExcludeOccupationTypes { get; set; } =
        new List<InvestorOccupationType>();

    public virtual ICollection<AdviserWorkType> ExcludeWorkTypes { get; set; } =
        new List<AdviserWorkType>();

    public bool ExcludeSecondOpinion { get; set; }

    public static BatchIntroductionIfaCompanySetting GetDefault(
        IfaCompany ifaCompany
    )
    {
        return new BatchIntroductionIfaCompanySetting
        {
            Id = Ulid.NewUlid(),
            IfaCompany = ifaCompany,
            IfaCompanyId = ifaCompany.Id,
            ExcludeSecondOpinion = false,
            Enabled = false,
            NotCompanySettingRange = false,
        };
    }

    public static void SetupModel(ModelBuilder modelBuilder)
    {
        modelBuilder
            .Entity<BatchIntroductionIfaCompanySetting>()
            .HasMany(e => e.AcceptAssetRanges)
            .WithMany(c => c.BatchIntroductionIfaCompanySetting)
            .UsingEntity(
                j =>
                    j.ToTable(
                        "BatchIntroductionIfaCompanySettingAcceptAssetRanges"
                    )
            );
        modelBuilder
            .Entity<BatchIntroductionIfaCompanySetting>()
            .HasMany(e => e.ExcludeOccupationTypes)
            .WithMany(c => c.BatchIntroductionIfaCompanySetting)
            .UsingEntity(
                j =>
                    j.ToTable(
                        "BatchIntroductionIfaCompanySettingExcludeOccupationTypes"
                    )
            );
        modelBuilder
            .Entity<BatchIntroductionIfaCompanySetting>()
            .HasMany(e => e.ExcludeWorkTypes)
            .WithMany(c => c.BatchIntroductionIfaCompanySetting)
            .UsingEntity(
                j =>
                    j.ToTable(
                        "BatchIntroductionIfaCompanySettingExcludeWorkTypes"
                    )
            );
    }
}
