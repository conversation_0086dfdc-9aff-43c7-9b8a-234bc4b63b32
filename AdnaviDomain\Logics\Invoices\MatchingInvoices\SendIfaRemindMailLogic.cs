using Adnavi.Domain.DTOs.Invoices.MatchingInvoices;
using Adnavi.Utils;
using Adnavi.Domain.Logics.Common.EMails;
using MimeKit;
using Microsoft.Extensions.Options;

namespace Adnavi.Domain.Logics.Invoices.MatchingInvoices;

public partial class SendIfaRemindMailLogic
{
    private readonly AdnaviDomainSettings _settings;
    private readonly AdnaviDomainContext _context;
    private readonly EMailService _emailService;
    private readonly MatchingInvoicesLogic _invoice;

    public SendIfaRemindMailLogic(
        AdnaviDomainContext context,
        IOptions<AdnaviDomainSettings> settings,
        EMailService emailService,
        MatchingInvoicesLogic invoice
    )
    {
        _settings = settings.Value;
        _context = context;
        _emailService = emailService;
        _invoice = invoice;
    }

    static bool IsWeekday(DateTime date)
    {
        // 平日（月曜日から金曜日）かどうかを判定
        return date.DayOfWeek >= DayOfWeek.Monday
            && date.DayOfWeek <= DayOfWeek.Friday;
    }

    static DateTime GetLastWeekdayOfMonth(int year, int month)
    {
        // 指定した年と月の最終日を取得
        DateTime lastDayOfMonth = new DateTime(
            year,
            month,
            DateTime.DaysInMonth(year, month)
        );

        // 最終日から最後の営業日（平日）を取得
        DateTime lastWeekday = lastDayOfMonth;
        while (!IsWeekday(lastWeekday))
        {
            lastWeekday = lastWeekday.AddDays(-1);
        }

        return lastWeekday;
    }

    public bool IsWeekdayBeforeLastWeekday(DateTime date)
    {
        if (
            date.DayOfWeek == DayOfWeek.Saturday
            || date.DayOfWeek == DayOfWeek.Sunday
        )
        {
            // 土曜日または日曜日の場合はfalseを返す
            return false;
        }
        else if (date.DayOfWeek == DayOfWeek.Friday)
        {
            // 金曜の場合は次の月曜日が最終営業日かどうかを判定
            return date.AddDays(3)
                == GetLastWeekdayOfMonth(date.Year, date.Month);
        }
        return date.AddDays(1) == GetLastWeekdayOfMonth(date.Year, date.Month);
    }

    public async Task SendIfaRemindMail()
    {
        // 今日の日付を取得
        DateTime JstToday = DateUtils.UtcToJst(DateTime.UtcNow);
        // 指定した日付が次の最終営業日の1日前かどうかを判定
        if (!IsWeekdayBeforeLastWeekday(JstToday))
            return;
        // リマインドメールを送信するユーザのデータを取得
        var users = _invoice.GetUsersWithNoProgressSet();
        if (users == null || users.Any())
            return;
        var errorData = new List<MatchingInvoiceManagementSummaryData>();
        var mailFrom = new MailboxAddress(
            _settings.Investors.ConsultationRequestMail.FromName,
            _settings.Investors.ConsultationRequestMail.FromAddress
        );
        foreach (var user in users)
        {
            if (
                user.ToName == null
                || user.ToName == ""
                || user.ToMail == null
                || user.ToMail == ""
            )
            {
                errorData.AddRange(user.Invoices);
                continue;
            }
            // メールテンプレート
            var mailTemplate = new SendIfaRemindMailTemplate(user, _context);
            // 次の最終営業日の1日前の場合
            // ここにメール送信処理を記述
            var e = !await _emailService.TrySendMail(
                mailTemplate.GetRemindMailSubject(),
                mailFrom,
                //new MailboxAddress("", user.ToMail),
                //mailTemplate.GetContentsForIfa(),

                // ADV-870 For testing
                "<EMAIL>",
                mailTemplate.GetContentsForIfa() + $"\nTo: ${user.ToMail}",
                ""
            );
            if (e)
            {
                errorData.AddRange(user.Invoices);
            }
        }
    }
}
