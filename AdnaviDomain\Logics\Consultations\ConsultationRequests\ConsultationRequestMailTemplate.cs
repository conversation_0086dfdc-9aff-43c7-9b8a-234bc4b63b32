using Adnavi.Domain.Models;
using Adnavi.Domain.DTOs.Consultations.ConsultationRequests;
using Adnavi.Domain.Common;
using MimeKit;

namespace Adnavi.Domain.Logics.Consultations.ConsultationRequests;

public class ConsultationRequestMailTemplate : IConsultationRequestMailTemplate
{
    protected readonly ConsultationRequestData _data;
    protected readonly string _timerexUrl;
    protected readonly AdnaviDomainContext _context;
    protected readonly string _consultations;
    protected readonly string _contactMethod;
    protected readonly string _assetRange;
    protected readonly string _prefecture;
    protected readonly MailNotification _notifications;

    public ConsultationRequestMailTemplate(
        ConsultationRequestData data,
        string timerexUrl,
        AdnaviDomainContext context,
        InvestorsSettings settings
    )
    {
        _data = data;
        _timerexUrl = timerexUrl;
        _context = context;
        _consultations = string.Join(
            "\r\n",
            _data.ConsultationTypes.Select(
                c =>
                    $"・ {ModelUtils.GetDisplayName(_context.ConsultationRequestTypes, c)}"
            )
        );
        _contactMethod = ModelUtils.GetDisplayName(
            _context.ContactMethods,
            _data.ContactMethod
        );
        _assetRange = ModelUtils.GetDisplayName(
            _context.AssetRanges,
            data.AssetRange
        );
        _prefecture = ModelUtils.GetDisplayName(
            _context.Prefectures,
            data.Prefecture
        );

        _notifications = settings.ConsultationRequestMail2;
    }

    public MailNotification GetNotifications() => _notifications;

    public MailboxAddress GetFromMailAddress() =>
        new("資産運用マッチング by ADVISER navi", _notifications.FromAddress);

    public string GetInvestorSubject() =>
        "お問合せありがとうございました【資産運用の相談サイト「資産運用マッチング」】";

    public string GetNotificationSubject(string originName) =>
        $"【フォーム:{originName}】投資家からお問い合わせがありました【資産運用の相談サイト「資産運用マッチング」】";

    public virtual string GetContentsForInvestor()
    {
        var informations = string.Empty;
        if (!string.IsNullOrEmpty(_data.Name))
        {
            informations += $"氏名:　　　　　　　　　　{_data.Name}\n";
        }
        if (!string.IsNullOrEmpty(_data.MailAddress))
        {
            informations += $"メールアドレス:　　　　　{_data.MailAddress}\n";
        }
        if (!string.IsNullOrEmpty(_data.Age.ToString()))
        {
            informations += $"年齢:　　　　　　　　　　{_data.Age}\n";
        }
        if (!string.IsNullOrEmpty(_data.TelephoneNumber))
        {
            informations += $"電話番号:　　　　　　　　{_data.TelephoneNumber}\n";
        }
        if (!string.IsNullOrEmpty(_prefecture))
        {
            informations += $"お住まい:　　　　　　　　{_prefecture}\n";
        }
        if (!string.IsNullOrEmpty(_contactMethod))
        {
            informations += $"面談方法:　　　　　　　　{_contactMethod}\n";
        }
        if (!string.IsNullOrEmpty(_assetRange))
        {
            informations += $"金融資産:　　　　　　　　{_assetRange}\n";
        }
        if (_data.DepositAssets != null)
        {
            informations += $"預貯金:　　　　　　　　　{_data.DepositAssets}\n";
        }
        if (_data.StockAssets != null)
        {
            informations += $"株・投資信託・債券:　　　{_data.StockAssets}\n";
        }
        if (_data.InsuranceAssets != null)
        {
            informations += $"保険:　　　　　　　　　　{_data.InsuranceAssets}\n";
        }
        if (_data.OtherAssets != null)
        {
            informations += $"その他:　　　　　　　　　{_data.OtherAssets}\n";
        }
        if (!string.IsNullOrEmpty(_data.RelatedFinancialInstitutions))
        {
            informations +=
                $"付き合いのある金融機関:　{_data.RelatedFinancialInstitutions}\n";
        }
        if (!string.IsNullOrEmpty(_consultations))
        {
            informations += $"ご用件:\n{_consultations}\n";
        }
        if (!string.IsNullOrEmpty(_data.Contents))
        {
            informations += $"お問い合わせ内容:\n　{_data.Contents}";
        }

        var contents =
            $@"{_data.Name} 様

お世話になります。
アドバイザーナビ株式会社「資産運用マッチング」カスタマーサポートチームでございます。
資産運用の相談サイト「資産運用マッチング」にお申し込みいただきましてありがとうございます。

早速ではございますが、資産運用に関しますご意向をヒアリングさせていただくため、担当者と面談をセッティングさせていただきたく存じます。
お手数ではございますが、下記のURLからご都合のよろしい日程を選択いただけますでしょうか。
(すでに日程調整いただいている方は重複のご案内となってしまい申し訳ございません。)
 
【ご面談日程調整URL】
{_timerexUrl}

よろしくお願い致します。

━━━━━━━━━━━━━━━━━━━━━━━━━━
　問い合わせ情報
━━━━━━━━━━━━━━━━━━━━━━━━━━
{informations}

━━━━━━━━━━━━━━━━━━━━━━━━━━

本自動配信メールは、ご登録いただいたメールアドレス宛てに、資産運用の相談サイト「資産運用マッチング」事務局から送られたものです。
お心当たりのない方は、お問合せ窓口（<EMAIL>）までご連絡くださいませ。

日程調整ツールを使用してのご調整をお願いいたします。※個別での日程調整メールへの返信は承っておりません

【お問合せ窓口】
アドバイザーナビ株式会社
カスタマーサポートチーム
<EMAIL>

資産運用の相談サイト「資産運用マッチング」
URL https://adviser-navi.co.jp/watashi-ifa/
";
        return contents;
    }

    public string GetContentsForNotification(
        NUlid.Ulid requestId,
        string remoteHost
    )
    {
        return GetContentsForInvestor()
            + $@"
特記事項： {_data.ConfirmText ?? "特記事項なし"}
お問い合わせタイプ：{_data.OriginName}
お問い合わせ元のページ：{_data.OriginUrl}
お問い合わせ元端末：{remoteHost}
リクエストID：{requestId.ToString()}
";
    }
}
