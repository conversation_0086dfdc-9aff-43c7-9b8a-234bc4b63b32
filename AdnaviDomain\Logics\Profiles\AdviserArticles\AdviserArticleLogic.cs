using Adnavi.Domain.Models;
using Adnavi.Utils;
using Adnavi.Utils.Images;
using NUlid;
using Adnavi.Utils.Converters;
using Adnavi.Domain.Models.Profiles.AdviserArticles;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Adnavi.Domain.DTOs.Profiles.AdviserArticles;
using Adnavi.Domain.DTOs.Profiles.AdviserColleagueEvaluations;
using Adnavi.Domain.Logics.Profiles.AdviserProfiles;

namespace Adnavi.Domain.Logics.Profiles.AdviserArticles;

public class AdviserArticleLogic
{
    private readonly S3Utils _s3;
    private readonly string _mediaUrl;
    private readonly AdnaviDomainContext _context;
    private readonly ModelUtils _model;
    private readonly AdviserProfileCommonLogic _commonLogic;

    private readonly ImageFileSpec _imageFileSpec =
        new(ImageFileTypes.Jpeg, 600, 600, true);

    public AdviserArticleLogic(
        IOptions<AdnaviS3Settings> settings,
        S3Utils s3,
        AdnaviDomainContext context,
        AdviserProfileCommonLogic commonLogic,
        ModelUtils model
    )
    {
        _context = context;
        _model = model;
        _s3 = s3;
        _mediaUrl = settings.Value.AdviserNaviMediaUrl;
        _commonLogic = commonLogic;
    }

    private IQueryable<Article> GetQuery(
        Ulid currentAdviserId,
        string? text,
        ICollection<ArticleCategories> categories
    )
    {
        var query = _context.Articles.Where(
            a => a.AdviserId == currentAdviserId
        );

        if (!string.IsNullOrEmpty(text))
            query = query.Where(a => a.Text.Contains(text));

        if (categories.Any())
            query = query.Where(
                a => a.Categories.Any(c => categories.Contains(c.Id))
            );

        return query;
    }

    public Task<int> GetCount(
        Ulid currentAdviserId,
        string? text,
        ICollection<ArticleCategories> categories
    ) => GetQuery(currentAdviserId, text, categories).CountAsync();

    public ICollection<Article> Search(
        Ulid currentAdviserId,
        string? text,
        ICollection<ArticleCategories> categories,
        int offset,
        int count
    )
    {
        return GetQuery(currentAdviserId, text, categories)
            .Include(a => a.Categories)
            .Skip(offset)
            .Take(count)
            .ToArray();
    }

    public async Task<Article> Get(Ulid articleId)
    {
        return await _model.SingleAsync(
            _context.Articles
                .Include(a => a.Categories)
                .Where(a => a.Id == articleId)
        );
    }

    private IQueryable<Article> GetPublished()
    {
        return _context.Articles
            .Where(a => a.LastPublishedDate != null)
            .OrderByDescending(a => a.LastPublishedDate);
    }

    private IQueryable<Article> GetQuery(
        Ulid? adviserId,
        string? text,
        ICollection<ArticleCategories> categories
    )
    {
        var query = GetPublished();

        if (adviserId != null)
            query = query.Where(a => a.AdviserId == adviserId);

        if (!string.IsNullOrEmpty(text))
            query = query.Where(a => a.Text.Contains(text));

        if (categories.Any())
            query = query.Where(
                a => a.Categories.Any(c => categories.Contains(c.Id))
            );

        return query;
    }

    public async Task<int> GetCount(
        Ulid? adviserId,
        string? text,
        ICollection<ArticleCategories> categories
    )
    {
        var query = GetQuery(adviserId, text, categories);
        return await query.CountAsync();
    }

    public IEnumerable<Article> Search(
        ICollection<ArticleCategories> categories,
        Ulid? adviserId,
        string? text,
        int offset = 0,
        int count = 20
    )
    {
        return GetQuery(adviserId, text, categories)
            .Include(a => a.Categories)
            .Skip(offset)
            .Take(count)
            .ToArray();
    }

    public Article GetPublished(Ulid articleId)
    {
        return GetPublished().Where(a => a.Id == articleId).Single();
    }

    public async Task<string> UploadImage(Ulid articleId, Stream input)
    {
        var image = _model.Create(
            new ArticleImage
            {
                ArticleId = articleId,
                CreatedTime = DateTime.UtcNow,
                Url = "",
            }
        );

        var s3Image = new S3Image(_s3);
        var key = $"articles/{image.ArticleId}/images/{image.Id}";
        var byteArray = await S3Image.ToBinaryArray(input);
        await s3Image.Upload(byteArray, key, _imageFileSpec);

        await _context.SaveChangesAsync();
        return $"{_mediaUrl}/{key}-{_imageFileSpec.FileName}";
    }

    public async Task DeleteImage(ArticleImage image)
    {
        var s3Image = new S3Image(_s3);
        var key = GetImageKey(image);
        await s3Image.Delete(key, _imageFileSpec);

        _context.Remove(image);
        await _context.SaveChangesAsync();
    }

    private static string GetImageKey(ArticleImage image)
    {
        return $"articles/{image.ArticleId}/images/{image.Id}";
    }

    public ICollection<ImageSpecAndUrl> GetTopImage(Article article)
    {
        var images = CreateResponsiveTopImage(article.Id);
        return images.GetUrls(article.TopImagePrefix).ToArray();
    }

    public async Task UploadTopImage(Article article, Stream input)
    {
        var images = CreateResponsiveTopImage(article.Id);
        article.TopImagePrefix = await images.Upload(input);
        await _context.SaveChangesAsync();
    }

    public async Task DeleteTopImage(Article article)
    {
        if (article.TopImagePrefix == null)
            return;

        var images = CreateResponsiveTopImage(article.Id);
        await images.Delete(article.TopImagePrefix);
        article.TopImagePrefix = null;
        await _context.SaveChangesAsync();
    }

    private ResponsiveS3Image CreateResponsiveTopImage(Ulid articleId)
    {
        return new ResponsiveS3Image(
            _s3,
            $"articles/{articleId}/top-image",
            _mediaUrl,
            600,
            600
        );
    }

    public static Article SanitizeHtmlDraft(Article data)
    {
        data.Draft = HtmlSanitizerConverter.SanitizeHtml(data.Draft);
        return data;
    }

    async public IAsyncEnumerable<PublicCustomerEvaluationSummaryData> GetCustomerEvaluationSummaries(
        Ulid adviserId,
        int offset,
        int count
    )
    {
        var query = _context.CustomerEvaluations;

        var customerEvaluationSummaries =
            PublicCustomerEvaluationSummaryData.FromQuery(
                query
                    .Where(e => e.AdviserId == adviserId && e.All >= 4)
                    .Where(e => e.Recommendation == 1)
                    .Where(e => e.Description.Length >= 60)
                    .Skip(offset)
                    .Take(count)
                    .OrderByDescending(c => c.PublishDate),
                _context
            );

        await foreach (
            var customerEvaluationSummary in customerEvaluationSummaries
        )
        {
            yield return customerEvaluationSummary;
        }
    }

    async public Task<int> CountCustomerEvaluationSummaries(Ulid adviserId)
    {
        var query = _context.CustomerEvaluations;
        return await query
            .Where(e => e.AdviserId == adviserId && e.All >= 4)
            .Where(e => e.Recommendation == 1)
            .Where(e => e.Description.Length >= 60)
            .CountAsync();
    }

    async public IAsyncEnumerable<AdviserColleagueEvaluationSummaryResponse> GetColleagueEvaluationSummaries(
        Ulid adviserId,
        int offset,
        int count
    )
    {
        var query = _context.AdviserColleagueEvaluations;

        var colleagueEvaluationSummaries =
            AdviserColleagueEvaluationSummaryResponse.FromQuery(
                query
                    .Where(e => e.EvaluatedAdviserId == adviserId)
                    .Skip(offset)
                    .Take(count)
                    .OrderByDescending(c => c.Id),
                _context,
                _commonLogic
            );

        await foreach (
            var colleagueEvaluationSummary in colleagueEvaluationSummaries
        )
        {
            yield return colleagueEvaluationSummary;
        }
    }

    async public IAsyncEnumerable<PublicCustomerEvaluationSummaryData> GetManyCustomerEvaluationSummaries(
        PublicArticleSummaryRequest request
    )
    {
        var query = _context.CustomerEvaluations;

        var customerEvaluationSummaries =
            PublicCustomerEvaluationSummaryData.FromQuery(
                query
                    .Where(
                        e =>
                            request.AdviserIds.Contains(e.AdviserId)
                            && e.All >= 4
                    )
                    .Skip(request.Offset)
                    .Take(request.Count)
                    .OrderByDescending(c => c.Id),
                _context
            );

        await foreach (
            var customerEvaluationSummary in customerEvaluationSummaries
        )
        {
            yield return customerEvaluationSummary;
        }
    }
}
