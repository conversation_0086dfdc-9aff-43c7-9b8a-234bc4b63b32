using Adnavi.Domain.Models.CardLoans;
using Adnavi.Domain.Models.Commons;
using Adnavi.Domain.Models.Profiles.InvestorProfiles;
using Adnavi.Utils.Models;

namespace Adnavi.Domain.DTOs.CardLoans
{
    public class PublicCardLoanSearchRequest : IDataTransferObject
    {
        public uint Offset { get; set; }
        public uint Count { get; set; }
        public CardLoanOrders Order { get; set; }

        public bool? OtherLoanExists { get; set; }

        public int? Age { get; set; }

        public AnnualIncomes? AnnualIncome { get; set; }

        public InvestorOccupationTypes? OccupationTypes { get; set; }

        public int? MaximumFinancingSeconds { get; set; }
    }
}
