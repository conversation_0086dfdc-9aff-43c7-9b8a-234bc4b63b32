using System.Globalization;
using System.Text;
using Adnavi.Domain.Models;
using Adnavi.Domain.Models.Accounts;
using Adnavi.Domain.Models.Commons;
using Adnavi.Domain.Models.Profiles;
using Adnavi.Domain.Models.Profiles.AdviserProfiles;
using CsvHelper;
using CsvHelper.Configuration;
using Microsoft.EntityFrameworkCore;
using NUlid;
using Adnavi.Domain.Logics.Common.SequenceNumber;
using Adnavi.Domain.Models.Commons.SequenceNumber;
using Adnavi.Domain.Logics.Accounts.OrganizationManagement;
using Adnavi.Domain.Models.Profiles.IfaCompanyProfiles;
using Adnavi.Domain.Logics.Consultations.BatchIntroductions;
using Adnavi.Domain.Models.Profiles.InvestorProfiles;
using Adnavi.Domain.Models.Consultations.BatchIntroductions;
using Adnavi.Domain.DTOs.Profiles.AdviserProfiles;
using Adnavi.Domain.DTOs.Profiles.CustomerEvaluations;
using Adnavi.Domain.DTOs.Consultations.BatchIntroductions;
using Adnavi.Domain.Logics.Profiles.IfaCompanyProfiles;
using Adnavi.Utils;

namespace Adnavi.Domain.Logics.Profiles.AdviserProfiles;

public class AdviserProfileLogic
{
    private readonly SequenceNumberService _seq;
    private readonly AdnaviDomainContext _context;
    private readonly ModelUtils _model;
    private readonly AdviserProfileCommonLogic _commonLogic;

    public AdviserProfileLogic(
        ModelUtils model,
        SequenceNumberService seq,
        AdnaviDomainContext context,
        AdviserProfileCommonLogic commonLogic
    )
    {
        _context = context;
        _model = model;
        _seq = seq;
        _commonLogic = commonLogic;
    }

    private IQueryable<Adviser> PublishableAdviserQuery() =>
        _context.Advisers.Where(
            adviser =>
                adviser.Published
                && adviser.AgreementsAccepted
                && adviser.FamilyName != ""
        );

    private IQueryable<Adviser> AdviserQuery() =>
        _context.Advisers.Where(
            adviser => adviser.AgreementsAccepted && adviser.FamilyName != ""
        );

    private IQueryable<Adviser> AcceptedAdviserQueryForCalendar =>
        _context.Advisers.Where(
            a =>
                a.CompanyByCompanyIntroduction != null
                && a.IfaCompany != null
                // IFA法人に所属のアドバイザーのみを対象とする
                && a.IfaCompany.CompanyType == CompanyTypes.IfaCompany
                && a.IfaCompany.EnableCalendarIntroduction == true
                && a.BatchIntroductionSetting != null
                && a.BatchIntroductionSetting.Enabled
        );

    // public Task<Adviser?> GetAdviserWithEMail(string eMail)
    // {
    //     return _context.Advisers
    //         .Where(a => a.EMail == eMail)
    //         .SingleOrDefaultAsync();
    // }

    private IQueryable<Adviser> GetQuery(
        List<Prefectures>? areas = null,
        List<Prefectures>? visitPrefectures = null,
        List<Prefectures>? webMeetingPrefectures = null,
        List<ConsultationTypes>? consultations = null,
        List<CustomerTypes>? mainCustomerTypes = null,
        List<AssetRanges>? assetRanges = null,
        List<AdviserWorkTypes>? adviserWorkTypes = null,
        List<Days>? workDays = null,
        List<ContactMethods>? availableContactMethods = null,
        string? text = null,
        bool publishedOnly = true,
        List<int>? cooperativeSecurityIds = null,
        bool? isBatchIntroductionSetting = false,
        bool isStaffIntroductionOnly = false,
        Ulid? ifaCompanyId = null,
        bool nightAvailable = false,
        bool fromSecurities = false,
        bool fromBank = false
    )
    {
        areas ??= new();
        visitPrefectures ??= new();
        webMeetingPrefectures ??= new();
        consultations ??= new();
        mainCustomerTypes ??= new();
        adviserWorkTypes ??= new();
        workDays ??= new();
        availableContactMethods ??= new();
        cooperativeSecurityIds ??= new();

        var stringAreas = areas.Select(p => p.ToString());
        var stringWorkDays = workDays.Select(p => p.ToString());
        var stringAvailableContactMethods = availableContactMethods.Select(
            p => p.ToString()
        );

        var query = publishedOnly
            ? PublishableAdviserQuery()
            : _context.Advisers;

        query = query.Include(a => a.CustomerStatics);
        if (isBatchIntroductionSetting == true)
            query = BatchIntroductionLogic.AddWhereAccepted(query);

        if (areas.Any())
        {
            query = query.Where(
                a => a.VisitPrefectures.Any(p => stringAreas.Contains(p.Name))
            );
        }

        if (visitPrefectures.Any())
        {
            query = query.Where(
                a =>
                    a.VisitPrefectures.Any(p => visitPrefectures.Contains(p.Id))
            );
        }

        if (webMeetingPrefectures.Any())
        {
            query = query.Where(
                a =>
                    a.WebMeetingPrefectures.Any(
                        p => webMeetingPrefectures.Contains(p.Id)
                    )
            );
        }

        if (consultations.Any())
        {
            foreach (var c in consultations)
            {
                query = Adviser.HasConsultationTypes(query, c);
            }
        }

        if (mainCustomerTypes.Any())
        {
            query = query.Where(
                a =>
                    a.AdditionalProfileItems != null
                    && a.AdditionalProfileItems.MainCustomerType != null
                    && mainCustomerTypes.Contains(
                        a.AdditionalProfileItems.MainCustomerType.Value
                    )
            );
        }

        if (adviserWorkTypes.Any())
        {
            foreach (var adviser in query)
            {
                var workTypes = adviser.GetWorkTypes();
                if (
                    workTypes == null
                    || !workTypes.Any()
                    || !adviserWorkTypes.Any(w => workTypes.Contains(w))
                )
                    query = query.Where(a => a.Id != adviser.Id);
            }

            // query = query.Where(a => a.GetWorkTypes() != null
            //     && a.GetWorkTypes().Any()
            //     && adviserWorkTypes.Any(w => a.GetWorkTypes().Contains(w)));
        }

        if (workDays.Any())
        {
            query = query.Where(
                a =>
                    a.AdditionalProfileItems != null
                    && a.AdditionalProfileItems.WorkDays.Any(
                        d => stringWorkDays.Contains(d.Name)
                    )
            );
        }

        if (availableContactMethods.Any())
        {
            query = query.Where(
                a =>
                    a.AdditionalProfileItems != null
                    && a.AdditionalProfileItems.AvailableContactMethods.Any(
                        m => stringAvailableContactMethods.Contains(m.Name)
                    )
            );
        }

        if (cooperativeSecurityIds.Any())
        {
            query = query.Where(
                a =>
                    a.IfaCompany != null
                    && a.IfaCompany.CooperativeSecurities.Any(
                        s => cooperativeSecurityIds.Contains(s.Id)
                    )
            );
        }

        if (!string.IsNullOrEmpty(text))
        {
            text = text.Replace(" ", "").Replace("　", "");
            query = query.Where(
                a =>
                    EF.Functions
                        .Collate(
                            a.FamilyName + a.FirstName,
                            "utf8mb4_unicode_ci"
                        )
                        .Contains(text)
                    || EF.Functions
                        .Collate(
                            a.FamilyNameKana + a.FirstNameKana,
                            "utf8mb4_unicode_ci"
                        )
                        .Contains(text)
            );
        }

        if (ifaCompanyId != null && ifaCompanyId != Ulid.Empty)
        {
            query = query.Where(a => a.IfaCompanyId == ifaCompanyId);
        }

        if (assetRanges != null && assetRanges.Any())
        {
            // 一括紹介設定の資産範囲が設定されている場合資産範囲内であることを確認する
            query = query.Where(
                a =>
                    a.BatchIntroductionSetting == null
                    || !a.BatchIntroductionSetting.AcceptAssetRanges.Any()
                    || a.BatchIntroductionSetting.AcceptAssetRanges.Any(
                        r => assetRanges.Contains(r.Id)
                    )
            );

            // IFA法人が設定されていること、
            // 一括紹介設定のIFA法人の資産範囲が設定されている場合資産範囲内であることを確認する
            query = query.Where(
                a =>
                    a.IfaCompany != null
                    && (
                        a.IfaCompany.BatchIntroductionIfaCompanySetting == null
                        || !a.IfaCompany.BatchIntroductionIfaCompanySetting.AcceptAssetRanges.Any()
                        || a.IfaCompany.BatchIntroductionIfaCompanySetting.AcceptAssetRanges.Any(
                            r => assetRanges.Contains(r.Id)
                        )
                    )
            );
        }

        if (nightAvailable)
        { // 夜間対応可能の場合、営業時間が18時以降のアドバイザーのみを取得する
            query = query.Where(a => a.ClosingHour > 18);
        }

        if (fromSecurities)
        {
            query = query.Where(
                a =>
                    a.AdditionalProfileItems != null
                    && (
                        a.AdditionalProfileItems.CareerType
                            == CareerTypes.MajorSecurities
                        || a.AdditionalProfileItems.CareerType
                            == CareerTypes.ForeignSecurities
                        || a.AdditionalProfileItems.CareerType
                            == CareerTypes.OtherSecurities
                    )
            );
        }

        if (fromBank)
        {
            query = query.Where(
                a =>
                    a.AdditionalProfileItems != null
                    && (
                        a.AdditionalProfileItems.CareerType
                            == CareerTypes.MegaBank
                        || a.AdditionalProfileItems.CareerType
                            == CareerTypes.RegionalBank
                        || a.AdditionalProfileItems.CareerType
                            == CareerTypes.OtherBank
                    )
            );
        }

        // 一括紹介設定のスタッフ紹介のみの場合、スタッフ紹介のみのアドバイザーのみを取得する
        if (isStaffIntroductionOnly)
        {
            query = query.Where(
                a =>
                    a.BatchIntroductionSetting != null
                    && a.BatchIntroductionSetting.StaffIntroductionOnly
            );
        }

        return query;
    }

    public IQueryable<Adviser> GetRandomList(byte n)
    {
        List<Ulid> ids = PublishableAdviserQuery().Select(a => a.Id).ToList();
        List<Ulid> selectedIds = new();

        for (byte i = 0; i < n; i++)
        {
            if (ids.Count == 0)
            {
                break;
            }

            Random random = new();
            int selected = random.Next(ids.Count);
            selectedIds.Add(ids[selected]);
            _ = ids.Remove(ids[selected]);
        }

        return PublishableAdviserQuery().Where(a => selectedIds.Contains(a.Id));
    }

    public Task<int> GetCount(
        List<Prefectures>? areas = null,
        List<Prefectures>? visitPrefectures = null,
        List<Prefectures>? webMeetingPrefectures = null,
        List<ConsultationTypes>? consultations = null,
        string? text = null,
        bool publishedOnly = true,
        List<CustomerTypes>? mainCustomerTypes = null,
        List<AssetRanges>? assetRanges = null,
        List<AdviserWorkTypes>? adviserWorkTypes = null,
        List<Days>? workDays = null,
        List<ContactMethods>? availableContactMethods = null,
        List<int>? cooperativeSecurityIds = null,
        bool isBatchIntroductionSetting = false,
        bool isStaffIntroductionOnly = false,
        Ulid? ifaCompanyId = null
    )
    {
        return GetQuery(
                areas: areas,
                visitPrefectures: visitPrefectures,
                webMeetingPrefectures: webMeetingPrefectures,
                consultations: consultations,
                text: text,
                publishedOnly: publishedOnly,
                mainCustomerTypes: mainCustomerTypes,
                assetRanges: assetRanges,
                adviserWorkTypes: adviserWorkTypes,
                workDays: workDays,
                availableContactMethods: availableContactMethods,
                cooperativeSecurityIds: cooperativeSecurityIds,
                isBatchIntroductionSetting: isBatchIntroductionSetting,
                isStaffIntroductionOnly: isStaffIntroductionOnly,
                ifaCompanyId: ifaCompanyId
            )
            .CountAsync();
    }

    public Task<int> GetCount2(
        List<Prefectures>? areas = null,
        List<Prefectures>? visitPrefectures = null,
        List<Prefectures>? webMeetingPrefectures = null,
        List<ConsultationTypes>? consultations = null,
        List<CustomerTypes>? customerTypes = null,
        List<AssetRanges>? assetRanges = null,
        List<AdviserWorkTypes>? adviserWorkTypes = null,
        List<Days>? workDays = null,
        List<ContactMethods>? availableContactMethods = null,
        string? text = null,
        uint offset = 0,
        uint count = 20,
        AdviserOrderColumn orderColumn = AdviserOrderColumn.ModifiedTime,
        bool descendence = false,
        bool publishedOnly = true,
        List<int>? cooperativeSecurityIds = null,
        Ulid? ifaCompanyId = null,
        bool isBatchIntroductionSetting = false,
        bool isStaffIntroductionOnly = false,
        bool nightAvailable = false,
        bool fromSecurities = false,
        bool fromBank = false
    )
    {
        return GetQuery(
                areas: areas,
                visitPrefectures: visitPrefectures,
                webMeetingPrefectures: webMeetingPrefectures,
                consultations: consultations,
                mainCustomerTypes: customerTypes,
                assetRanges: assetRanges,
                adviserWorkTypes: adviserWorkTypes,
                workDays: workDays,
                availableContactMethods: availableContactMethods,
                text: text,
                publishedOnly: publishedOnly,
                cooperativeSecurityIds: cooperativeSecurityIds,
                isBatchIntroductionSetting: isBatchIntroductionSetting,
                isStaffIntroductionOnly: isStaffIntroductionOnly,
                ifaCompanyId: ifaCompanyId,
                nightAvailable: nightAvailable,
                fromSecurities: fromSecurities,
                fromBank: fromBank
            )
            .CountAsync();
    }

    public IQueryable<Adviser> Search(
        List<Prefectures>? areas = null,
        List<Prefectures>? visitPrefectures = null,
        List<Prefectures>? webMeetingPrefectures = null,
        List<ConsultationTypes>? consultations = null,
        List<CustomerTypes>? customerTypes = null,
        List<AssetRanges>? assetRanges = null,
        List<AdviserWorkTypes>? adviserWorkTypes = null,
        List<Days>? workDays = null,
        List<ContactMethods>? availableContactMethods = null,
        string? text = null,
        uint offset = 0,
        uint count = 20,
        AdviserOrderColumn orderColumn = AdviserOrderColumn.ModifiedTime,
        bool descendence = false,
        bool publishedOnly = true,
        List<int>? cooperativeSecurityIds = null,
        Ulid? ifaCompanyId = null,
        bool? isBatchIntroductionSetting = false,
        bool isStaffIntroductionOnly = false,
        bool nightAvailable = false,
        bool fromSecurities = false,
        bool fromBank = false
    )
    {
        var query = GetQuery(
            areas: areas,
            visitPrefectures: visitPrefectures,
            webMeetingPrefectures: webMeetingPrefectures,
            consultations: consultations,
            mainCustomerTypes: customerTypes,
            assetRanges: assetRanges,
            adviserWorkTypes: adviserWorkTypes,
            workDays: workDays,
            availableContactMethods: availableContactMethods,
            text: text,
            publishedOnly: publishedOnly,
            cooperativeSecurityIds: cooperativeSecurityIds,
            isBatchIntroductionSetting: isBatchIntroductionSetting,
            isStaffIntroductionOnly: isStaffIntroductionOnly,
            ifaCompanyId: ifaCompanyId,
            nightAvailable: nightAvailable,
            fromSecurities: fromSecurities,
            fromBank: fromBank
        );

        switch (orderColumn)
        {
            case AdviserOrderColumn.Id:
                query = descendence
                    ? query.OrderByDescending(a => a.Id)
                    : query.OrderBy(a => a.Id);
                break;
            case AdviserOrderColumn.Name:
                query = descendence
                    ? query
                        .OrderByDescending(a => a.FamilyName)
                        .ThenByDescending(a => a.FirstName)
                    : query.OrderBy(a => a.FamilyName).ThenBy(a => a.FirstName);
                break;
            case AdviserOrderColumn.CompanyName:
                query = descendence
                    ? query.OrderByDescending(
                        a => a.IfaCompany == null ? null : a.IfaCompany.Name
                    )
                    : query.OrderBy(
                        a => a.IfaCompany == null ? null : a.IfaCompany.Name
                    );
                break;
            case AdviserOrderColumn.BatchIntroductionStatus:
                query = descendence
                    ? query.OrderByDescending(
                        a => a.CompanyByCompanyIntroduction
                    )
                    : query.OrderBy(a => a.CompanyByCompanyIntroduction);
                break;
            case AdviserOrderColumn.BatchIntroductionSettingEnabled:
                query = descendence
                    ? query.OrderByDescending(
                        a =>
                            a.BatchIntroductionSetting != null
                            && a.BatchIntroductionSetting.Enabled
                    )
                    : query.OrderBy(
                        a =>
                            a.BatchIntroductionSetting != null
                            && a.BatchIntroductionSetting.Enabled
                    );
                break;
            case AdviserOrderColumn.ModifiedTime:
                query = descendence
                    ? query.OrderByDescending(a => a.ModifiedTime)
                    : query.OrderBy(a => a.ModifiedTime);
                break;
            case AdviserOrderColumn.AccessCount:
                query = SortAdvisersByIntroductionCount(query, 30);
                break;
            case AdviserOrderColumn.RequestedCount:
                query = SortAdvisersByRequestedCount(query, 30);
                break;
            // TotalScore(総合ランキング)の定義が不明なため、未実装
            case AdviserOrderColumn.TotalScore:
                break;
        }
        return query.Skip((int)offset).Take((int)count);
    }

    private static IQueryable<Adviser> SortAdvisersByRequestedCount(
        IQueryable<Adviser> query,
        int period,
        bool descendence = true
    )
    {
        var result = query.Select(
            a =>
                new
                {
                    Adviser = a,
                    Count = a.BatchIntroductions
                        .Where( // 30日以内のリクエストのみをカウント
                            b =>
                                b.Status == BatchIntroductionStatus.Request
                                && b.CreatedTime
                                    > DateTime.UtcNow.AddDays(period * -1)
                        )
                        .Count()
                }
        );

        result = descendence
            ? result.OrderByDescending(a => a.Count)
            : result.OrderBy(a => a.Count);

        return result.Select(a => a.Adviser);
    }

    private static IQueryable<Adviser> SortAdvisersByIntroductionCount(
        IQueryable<Adviser> query,
        int period,
        bool descendence = true
    )
    {
        var result = query.Select(
            a =>
                new
                {
                    Adviser = a,
                    Count = a.BatchIntroductions
                        .Where( // 30日以内のIntroductionのみをカウント
                            b =>
                                b.Status == BatchIntroductionStatus.Introduction
                                && b.CreatedTime
                                    > DateTime.UtcNow.AddDays(period * -1)
                        )
                        .Count()
                }
        );
        result = descendence
            ? result.OrderByDescending(a => a.Count)
            : result.OrderBy(a => a.Count);

        return result.Select(a => a.Adviser);
    }

    public void Update(Adviser adviser, IAdviserRequest data)
    {
        _ = data.To(adviser, _context);
    }

    public async Task AcceptAgreements(
        Ulid adviserId,
        IEnumerable<Agreements> agreementIds
    )
    {
        foreach (
            var agreementId in agreementIds.Where(
                ag =>
                    !_context.AdviserAgreements
                        .Where(
                            ad =>
                                ad.AdviserId == adviserId && ad.Agreement == ag
                        )
                        .Any()
            )
        )
        {
            _model.Create(
                new AdviserAgreement
                {
                    AdviserId = adviserId,
                    Agreement = agreementId,
                    ConfirmDate = DateTime.UtcNow
                }
            );
        }

        await _context.SaveChangesAsync();

        var adviser = await _model.SingleAsync(
            _context.Advisers
                .Include(a => a.Agreements)
                .Where(a => a.Id == adviserId)
        );

        adviser.AgreementsAccepted =
            AdviserProfileCommonLogic.IsAcceptedRequiredAgreements(
                adviser.Agreements
            );
        await _context.SaveChangesAsync();
    }

    public async Task RemoveAgreements(
        Ulid adviserId,
        IEnumerable<Agreements> agreementIds
    )
    {
        foreach (
            var agreement in _context.AdviserAgreements.Where(
                ag => ag.AdviserId == adviserId
            )
        )
        {
            if (agreementIds.Contains(agreement.Agreement))
                _context.Remove(agreement);
        }
        await _context.SaveChangesAsync();

        var adviser = await _model.SingleAsync(
            _context.Advisers
                .Include(a => a.Agreements)
                .Where(a => a.Id == adviserId)
        );

        adviser.AgreementsAccepted =
            AdviserProfileCommonLogic.IsAcceptedRequiredAgreements(
                adviser.Agreements
            );
        await _context.SaveChangesAsync();
    }

    public static void UpdateEmployerHistories(
        Adviser adviser,
        IEnumerable<AdviserEmployerRequest> data
    )
    {
        if (data.Where(item => item.IsPrimaryCompany).Count() > 1)
            throw new AdviserLimitPrimaryCompanyError();

        adviser.Employers.Clear();
        foreach (AdviserEmployerRequest? item in data)
        {
            adviser.Employers.Add(
                item.To(
                    new AdviserEmployer
                    {
                        Id = Ulid.NewUlid(),
                        Adviser = adviser,
                        CompanyName = item.CompanyName,
                        EmploymentYear = item.EmploymentYear,
                        RetirementYear = item.EmploymentYear,
                        IsPrimaryCompany = item.IsPrimaryCompany
                    }
                )
            );
        }
    }

    public async Task<IEnumerable<AdviserEmployerResponse>> GetEmployers(
        IAdviser adviser
    ) =>
        await _context.AdviserEmployers
            .Where(f => f.Adviser.Id == adviser.Id)
            .OrderBy(f => f.RetirementYear)
            .Select(f => new AdviserEmployerResponse(f))
            .ToListAsync();

    public async Task<MemoryStream> OutputCsv()
    {
        var config = new CsvConfiguration(CultureInfo.InvariantCulture)
        {
            HasHeaderRecord = true
        };

        var dataExport = AdviserCsvRecord.FromQuery(
            _context,
            _context.Advisers.AsNoTracking()
        );

        var stream = new MemoryStream();
        using (
            var writeFile = new StreamWriter(
                stream,
                Encoding.UTF8,
                -1,
                leaveOpen: true
            )
        )
        {
            var csv = new CsvWriter(writeFile, config);
            csv.WriteHeader<AdviserCsvRecord>();
            await csv.NextRecordAsync();
            await foreach (var record in dataExport)
            {
                csv.WriteRecord(record);
                await csv.NextRecordAsync();
            }
        }
        stream.Position = 0; // reset stream

        return stream;
    }

    public async Task UpdateCustomerStatics(
        Ulid adviserId,
        CustomerStaticsRequest data
    )
    {
        var adviser = await _context.Advisers
            .Include(a => a.CustomerStatics)
            .Where(c => c.Id == adviserId)
            .SingleAsync();

        if (adviser.CustomerStatics == null)
        {
            adviser.CustomerStatics = new CustomerStatics();
            adviser.CustomerStatics = _model.Create(
                data.To(adviser.CustomerStatics)
            );
            return;
        }
        _model.Update(data.To(adviser.CustomerStatics));
    }

    public Task<Adviser> GetAdviserFromOldId(int oldId)
    {
        return _model.SingleAsync(
            _context.Advisers.Where(a => a.OldId1 == oldId)
        );
    }

    public Task<Adviser> GetPublicAdviser(Ulid adviserId)
    {
        return _model.SingleAsync(
            PublishableAdviserQuery().Where(a => a.Id == adviserId)
        );
    }

    public Task<List<Adviser>> GetPublicAdvisersBatch(List<Ulid> adviserIds)
    {
        return AdviserQuery()
            .Where(a => adviserIds.Contains(a.Id))
            .ToListAsync();
    }

    public Task<Adviser> GetByStaff(Ulid adviserId)
    {
        return _model.FindAsync(_context.Advisers, adviserId);
    }

    public async Task UpdateAccessCount(Ulid adviserId)
    {
        var adviser = await _context.Advisers.FindAsync(adviserId);
        if (adviser != null)
        {
            adviser.ProfileAccessCount += 1;
            await _context.SaveChangesAsync();
        }
    }

    public async Task<CustomerStaticsResponse> GetCustomerStatics(
        Ulid adviserId
    )
    {
        var adviser = await _model.SingleAsync(
            _context.Advisers.Where(a => a.Id == adviserId)
        );

        return new CustomerStaticsResponse(adviser?.CustomerStatics);
    }

    private IQueryable<CustomerEvaluation> PublishDateCustomerEvaluationsQuery(
        Ulid adviserId
    ) =>
        _context.CustomerEvaluations
            .Where(c => c.AdviserId == adviserId)
            .Where(c => c.PublishDate <= DateTime.UtcNow);

    private IQueryable<CustomerEvaluation> CustomerEvaluationsQuery(
        Ulid adviserId,
        string? text
    )
    {
        IQueryable<CustomerEvaluation> query =
            PublishDateCustomerEvaluationsQuery(adviserId)
                .Where(c => c.AdviserId == adviserId);
        if (!string.IsNullOrEmpty(text))
            query = query.Where(
                a =>
                    a.CreatedUserEmail.Contains(text)
                    || a.Description.Contains(text)
            );
        return query;
    }

    public List<CustomerEvaluationResponse> GetPublishCustomerEvaluations(
        Ulid adviserId,
        string? text,
        uint offset = 0,
        uint count = 20
    ) =>
        CustomerEvaluationsQuery(adviserId, text)
            .OrderByDescending(a => a.ModifiedTime)
            .Skip((int)offset)
            .Take((int)count)
            .Select(c => new CustomerEvaluationResponse(c))
            .ToList();

    public int CountPublishCustomerEvaluations(
        Ulid adviserId,
        string? text,
        uint offset = 0,
        uint count = 20
    ) =>
        CustomerEvaluationsQuery(adviserId, text)
            .Skip((int)offset)
            .Take((int)count)
            .Count();

    public async Task<AdviserCustomerEvaluationResponse> GetAdviserCustomerEvaluationsStatics(
        Ulid adviserId
    )
    {
        var customerEvaluations = await PublishDateCustomerEvaluationsQuery(
                adviserId
            )
            .ToArrayAsync();
        return new AdviserCustomerEvaluationResponse(customerEvaluations);
    }

    public List<StaffAdviserCustomerEvaluationResponse> GetStaffAdviserCustomerEvaluationsStatics()
    {
        return _context.CustomerEvaluations
            .GroupBy(c => c.AdviserId)
            .Select(
                g =>
                    new StaffAdviserCustomerEvaluationResponse(
                        g.ToArray(),
                        g.Key
                    )
            )
            .ToList();
    }

    public async Task<Adviser> GetOrCreateAdviser(User user)
    {
        if (user.Adviser == null)
        {
            user.Adviser = _model.Create(
                new Adviser
                {
                    SequenceId = await _seq.GetValue(SequenceNumberId.Adviser),
                }.InitializeRequired(_context)
            );
            await _context.SaveChangesAsync();
        }

        return user.Adviser;
    }

    public async Task<AdviserResponse> ToAdviserResponse(Adviser adviser)
    {
        return new AdviserResponse(
            adviser,
            await _commonLogic.GetPhotoUrls(adviser),
            query: _context.VisitorRecords.Where(v => v.AdviserId == adviser.Id)
        // _context.ProfileItemInformations.ToArray()
        );
    }

    public async Task UpdateLocation(Adviser a, AdviserLocationRequest data)
    {
        _model.Update(data.To(a));
        await _context.SaveChangesAsync();
    }

    public async Task UpdateManagement(
        Ulid adviserId,
        AdviserManagementRequest data,
        User updatedUser
    )
    {
        var adviser = await _model.SingleAsync(
            _context.Advisers.Include(a => a.User).Where(a => a.Id == adviserId)
        );

        // // if change email, check if it is already registered.
        // if (
        //     !string.IsNullOrEmpty(data.EMail)
        //     && data.EMail != adviser.User?.MainEMail
        // )
        // {
        //     if (
        //         data.EMail != null
        //         && _context.Advisers.Any(
        //             a =>
        //                 a.User != null
        //                 && a.User.MainEMail == data.EMail
        //                 && a.Id != adviserId
        //         )
        //     )
        //         throw new AdviserEmailIsAlreadyRegisteredError(data.EMail);
        // }

        this.Update(adviser, data);
        adviser.ModifiedTime = DateTime.UtcNow;

        if (adviser.BatchIntroductionSetting != null)
        {
            _model.Create(
                new IntroductionSettingHistory(
                    adviser.BatchIntroductionSetting,
                    updatedUser
                )
            );
        }

        await _context.SaveChangesAsync();
    }

    public IQueryable<Adviser> AdviserRelatedQuery(Adviser adviser) =>
        _context.Advisers
            .Where(a => a.Id != adviser.Id)
            .Where(a => a.IfaCompanyId == adviser.IfaCompanyId);

    public int CountAdviserRelated(Adviser adviser) =>
        AdviserRelatedQuery(adviser).Count();

    public async IAsyncEnumerable<AdviserPublicResponse> GetAdviserRelated(
        Adviser adviser,
        uint offset = 0,
        uint count = 20
    )
    {
        foreach (
            var related in await AdviserRelatedQuery(adviser)
                .OrderByDescending(a => a.CreatedTime)
                .Skip((int)offset)
                .Take((int)count)
                .ToArrayAsync()
        )
        {
            yield return new AdviserPublicResponse(
                related,
                _context,
                await _commonLogic.GetPhotoUrls(related),
                _commonLogic.GetPhotoCollectionUrls(related)
            );
        }
    }

    public async Task<AdviserPublicResponse> GetPublicAdviserBySequenceId(
        int sequenceId
    )
    {
        var adviser = await _model.SingleAsync(
            PublishableAdviserQuery().Where(a => a.SequenceId == sequenceId)
        );
        return new AdviserPublicResponse(
            adviser,
            _context,
            await _commonLogic.GetPhotoUrls(adviser),
            _commonLogic.GetPhotoCollectionUrls(adviser)
        );
    }

    public async Task<int> GetSequenceIdFromId(Ulid adviserId)
    {
        var adviser = await _model.SingleAsync(
            PublishableAdviserQuery().Where(a => a.Id == adviserId)
        );
        return adviser.SequenceId;
    }

    public async Task<ICollection<SecuritiesCompany>> GetSecurityList(
        string? text
    )
    {
        var query = _context.SecuritiesCompanies.AsQueryable();
        if (text != null)
            query = query.Where(s => s.Name.Contains(text));

        return await query.OrderBy(s => s.Name).ToArrayAsync();
    }

    private int IfaCompanyAssetRangeCounts(AssetRanges investorAssetRange)
    {
        var count = GetQuery(
                assetRanges: new List<AssetRanges> { investorAssetRange },
                publishedOnly: false,
                isBatchIntroductionSetting: true
            )
            .Where(a => a.IfaCompany != null)
            .Select(a => a.IfaCompany)
            .Distinct()
            .Count();
        return count;
    }

    public StaffAssetCountsData GetCountIfaCompanyPerAsset()
    {
        var ifaCompanyPerAssetCounts = new StaffAssetCountsData
        {
            B0_A500 = IfaCompanyAssetRangeCounts(AssetRanges.B0_A500),
            B500_A1000 = IfaCompanyAssetRangeCounts(AssetRanges.B500_A1000),
            B1000_A2000 = IfaCompanyAssetRangeCounts(AssetRanges.B1000_A2000),
            B2000_A3000 = IfaCompanyAssetRangeCounts(AssetRanges.B2000_A3000),
            B3000_A5000 = IfaCompanyAssetRangeCounts(AssetRanges.B3000_A5000),
            B5000_A7000 = IfaCompanyAssetRangeCounts(AssetRanges.B5000_A7000),
            B7000_A10000 = IfaCompanyAssetRangeCounts(AssetRanges.B7000_A10000),
            B10000_A20000 = IfaCompanyAssetRangeCounts(
                AssetRanges.B10000_A20000
            ),
            B20000_A30000 = IfaCompanyAssetRangeCounts(
                AssetRanges.B20000_A30000
            ),
            B30000_A40000 = IfaCompanyAssetRangeCounts(
                AssetRanges.B30000_A40000
            ),
            B40000_A50000 = IfaCompanyAssetRangeCounts(
                AssetRanges.B40000_A50000
            ),
            B50000_A100000 = IfaCompanyAssetRangeCounts(
                AssetRanges.B50000_A100000
            ),
            B100000 = IfaCompanyAssetRangeCounts(AssetRanges.B100000),
            B1000_A3000 = IfaCompanyAssetRangeCounts(AssetRanges.B1000_A3000),
            B10000_A50000 = IfaCompanyAssetRangeCounts(
                AssetRanges.B10000_A50000
            ),
        };

        return ifaCompanyPerAssetCounts;
    }

    public async Task<StaffAssetCountsData> GetCountAdviserPerAsset()
    {
        StaffAssetCountsData adviserPerAssetCounts = new StaffAssetCountsData
        {
            B0_A500 = await GetQuery(
                    assetRanges: new List<AssetRanges> { AssetRanges.B0_A500 },
                    publishedOnly: false,
                    isBatchIntroductionSetting: true
                )
                .CountAsync(),
            B500_A1000 = await GetQuery(
                    assetRanges: new List<AssetRanges>
                    {
                        AssetRanges.B500_A1000
                    },
                    publishedOnly: false,
                    isBatchIntroductionSetting: true
                )
                .CountAsync(),
            B1000_A2000 = await GetQuery(
                    assetRanges: new List<AssetRanges>
                    {
                        AssetRanges.B1000_A2000
                    },
                    publishedOnly: false,
                    isBatchIntroductionSetting: true
                )
                .CountAsync(),
            B2000_A3000 = await GetQuery(
                    assetRanges: new List<AssetRanges>
                    {
                        AssetRanges.B2000_A3000
                    },
                    publishedOnly: false,
                    isBatchIntroductionSetting: true
                )
                .CountAsync(),
            B3000_A5000 = await GetQuery(
                    assetRanges: new List<AssetRanges>
                    {
                        AssetRanges.B3000_A5000
                    },
                    publishedOnly: false,
                    isBatchIntroductionSetting: true
                )
                .CountAsync(),
            B5000_A7000 = await GetQuery(
                    assetRanges: new List<AssetRanges>
                    {
                        AssetRanges.B5000_A7000
                    },
                    publishedOnly: false,
                    isBatchIntroductionSetting: true
                )
                .CountAsync(),
            B7000_A10000 = await GetQuery(
                    assetRanges: new List<AssetRanges>
                    {
                        AssetRanges.B7000_A10000
                    },
                    publishedOnly: false,
                    isBatchIntroductionSetting: true
                )
                .CountAsync(),
            B10000_A20000 = await GetQuery(
                    assetRanges: new List<AssetRanges>
                    {
                        AssetRanges.B10000_A20000
                    },
                    publishedOnly: false,
                    isBatchIntroductionSetting: true
                )
                .CountAsync(),
            B20000_A30000 = await GetQuery(
                    assetRanges: new List<AssetRanges>
                    {
                        AssetRanges.B20000_A30000
                    },
                    publishedOnly: false,
                    isBatchIntroductionSetting: true
                )
                .CountAsync(),
            B30000_A40000 = await GetQuery(
                    assetRanges: new List<AssetRanges>
                    {
                        AssetRanges.B30000_A40000
                    },
                    publishedOnly: false,
                    isBatchIntroductionSetting: true
                )
                .CountAsync(),
            B40000_A50000 = await GetQuery(
                    assetRanges: new List<AssetRanges>
                    {
                        AssetRanges.B40000_A50000
                    },
                    publishedOnly: false,
                    isBatchIntroductionSetting: true
                )
                .CountAsync(),
            B50000_A100000 = await GetQuery(
                    assetRanges: new List<AssetRanges>
                    {
                        AssetRanges.B50000_A100000
                    },
                    publishedOnly: false,
                    isBatchIntroductionSetting: true
                )
                .CountAsync(),
            B100000 = await GetQuery(
                    assetRanges: new List<AssetRanges> { AssetRanges.B100000 },
                    publishedOnly: false,
                    isBatchIntroductionSetting: true
                )
                .CountAsync(),
            B1000_A3000 = await GetQuery(
                    assetRanges: new List<AssetRanges>
                    {
                        AssetRanges.B1000_A3000
                    },
                    publishedOnly: false,
                    isBatchIntroductionSetting: true
                )
                .CountAsync(),
            B10000_A50000 = await GetQuery(
                    assetRanges: new List<AssetRanges>
                    {
                        AssetRanges.B10000_A50000
                    },
                    publishedOnly: false,
                    isBatchIntroductionSetting: true
                )
                .CountAsync(),
        };
        return adviserPerAssetCounts;
    }

    public async Task<Ulid> GetUserId(Ulid adviserId)
    {
        var user = await _context.Users
            .Where(u => u.AdviserId == adviserId)
            .SingleAsync();
        return user.Id;
    }

    public IEnumerable<BatchIntroductionSettingSummaryResponse> GetRegisteredIfaCompaniesStatistics()
    {
        var result = new List<BatchIntroductionSettingSummaryResponse>();
        var prefectures = _context.Prefectures
            .Select(p => new { p.Id, p.DisplayName })
            .AsNoTracking()
            .ToList();
        var assetRanges = _context.AssetRanges
            .Select(
                a =>
                    new
                    {
                        a.Id,
                        a.DisplayName,
                        a.BatchIntroductionSettings,
                        a.BatchIntroductionIfaCompanySetting
                    }
            )
            .AsNoTracking()
            .ToList();

        foreach (var prefecture in prefectures)
        {
            foreach (var assetRange in assetRanges)
            {
                int visitCount = 0;
                int webCount = 0;
                visitCount = GetQuery(
                        areas: new List<Prefectures> { prefecture.Id },
                        assetRanges: new List<AssetRanges> { assetRange.Id },
                        publishedOnly: false,
                        isBatchIntroductionSetting: true
                    )
                    .Where(a => a.IfaCompany != null)
                    .Select(a => a.IfaCompany)
                    .Distinct()
                    .Count();
                webCount = GetQuery(
                        assetRanges: new List<AssetRanges> { assetRange.Id },
                        publishedOnly: false,
                        isBatchIntroductionSetting: true
                    )
                    .Where(
                        a =>
                            a.IfaCompany != null
                            && a.WebMeetingPrefectures.Any(
                                v => v.Id == prefecture.Id
                            )
                    )
                    .Select(a => a.IfaCompany)
                    .Distinct()
                    .Count();

                result.Add(
                    new BatchIntroductionSettingSummaryResponse(
                        prefecture.DisplayName,
                        assetRange.DisplayName,
                        webCount,
                        visitCount
                    )
                );
            }
        }
        return result;
    }

    public async Task<MemoryStream> OutputIntroductionCsv()
    {
        var config = new CsvConfiguration(CultureInfo.InvariantCulture)
        {
            HasHeaderRecord = true
        };

        var dataExport = await IntroductionParameterCSVRecord.FromQuery(
            _context,
            _context.Advisers.AsNoTracking()
        );

        var stream = new MemoryStream();
        using (
            var writeFile = new StreamWriter(
                stream,
                Encoding.UTF8,
                -1,
                leaveOpen: true
            )
        )
        {
            var csv = new CsvWriter(writeFile, config);
            var hasHeaderBeenWritten = false;
            foreach (var row in dataExport)
            {
                if (!hasHeaderBeenWritten)
                {
                    foreach (var pair in row)
                    {
                        csv.WriteField(pair.Key);
                    }

                    hasHeaderBeenWritten = true;

                    csv.NextRecord();
                }

                foreach (var pair in row)
                {
                    csv.WriteField(pair.Value);
                }

                csv.NextRecord();
            }
        }
        stream.Position = 0; // reset stream

        return stream;
    }

    public async Task<MemoryStream> InvoiceStatusStatsCsv(
        YearAndMonth? month = null
    )
    {
        var config = new CsvConfiguration(CultureInfo.InvariantCulture)
        {
            HasHeaderRecord = true
        };

        var dataExport = AdviserInvoiceStatusCsvRecord.FromQuery(
            _context,
            month
        );

        var stream = new MemoryStream();
        using (
            var writeFile = new StreamWriter(
                stream,
                Encoding.UTF8,
                -1,
                leaveOpen: true
            )
        )
        {
            var csv = new CsvWriter(writeFile, config);
            csv.WriteHeader<AdviserInvoiceStatusCsvRecord>();
            await csv.NextRecordAsync();
            foreach (
                var record in dataExport.Result.OrderByDescending(
                    r => r.InvoiceCount
                )
            )
            {
                csv.WriteRecord(record);
                await csv.NextRecordAsync();
            }
        }
        stream.Position = 0; // reset stream

        return stream;
    }

    /// <summary>
    /// Get the statistics of BatchIntroduction for a specific adviser
    /// </summary>
    /// <param name="adviserId"></param>
    /// <param name="context"></param>
    public AdviserBatchIntroductionStatisticsData GetBatchIntroductionStatistics(
        Ulid adviserId,
        AdnaviDomainContext context
    )
    {
        return new AdviserBatchIntroductionStatisticsData(adviserId, context);
    }

    public async Task<AdviserInquiryResponse> GetInquiry(
        Ulid adviserId,
        Ulid inquiryId
    )
    {
        var inquiry = await _context.AdviserInquiries
            .Where(i => i.AdviserId == adviserId && i.Id == inquiryId)
            .SingleOrDefaultAsync();
        if (inquiry == null)
        {
            throw new AdviserInquiryNotFoundError(typeof(AdviserInquiry).Name);
        }

        return new AdviserInquiryResponse(inquiry);
    }

    public async Task<AdviserInquiryContactFormResponse> GetInquiryContactForm(
        Adviser adviser,
        Ulid inquiryId
    )
    {
        var name = adviser.FullName;
        var ifaCompanyName = adviser?.IfaCompany?.Name ?? "";
        var email = adviser?.User?.MainEMail ?? "";
        var inquiry = await _context.AdviserInquiries
            .Where(i => i.AdviserId == adviser!.Id && i.Id == inquiryId)
            .SingleOrDefaultAsync();
        if (inquiry == null)
        {
            throw new AdviserInquiryNotFoundError(
                typeof(AdviserInquiry).Name,
                adviser!.Id,
                inquiryId
            );
        }

        return new AdviserInquiryContactFormResponse(
            name,
            ifaCompanyName,
            email,
            inquiry
        );
    }

    public async Task<AdviserInquiryContactFormResponse> GetInquiryContactForm(
        Ulid adviserId,
        Ulid inquiryId
    )
    {
        var adviser = await _model.SingleAsync(
            _context.Advisers.Where(a => a.Id == adviserId)
        );
        return await GetInquiryContactForm(adviser, inquiryId);
    }

    public async Task<Ulid> CreateInquiry(
        AdviserContactFormRequestData data,
        Ulid? adviserId
    )
    {
        var inquiry = new AdviserInquiry
        {
            Id = Ulid.NewUlid(),
            AdviserId = adviserId,
            ContactFormType = data.ContactFormType,
            Detail = data.Detail,
            ReasonOfInvoiceExempt = data.ReasonOfInvoiceExempt,
            ContactInvestorLessThanThreeHours =
                data.ContactInvestorLessThanThreeHours,
            SubmitEvidence = data.SubmitEvidence
        };
        _context.AdviserInquiries.Add(inquiry);

        await _context.SaveChangesAsync();

        return inquiry.Id;
    }

    public async Task CreatePhotoCollection(
        Ulid adviserId,
        Ulid newPhotoFileKey
    )
    {
        var photoCollection = new AdviserPhotoCollection
        {
            Id = Ulid.NewUlid(),
            AdviserId = adviserId,
            NewPhotoFileKey = newPhotoFileKey
        };
        _model.Create(photoCollection);
        await _model.SaveChangesAsync();
    }

    public int CountAdviserOrderColumn(
        Adviser adviser,
        AdviserOrderColumn orderColumn,
        int period = 0
    )
    {
        return orderColumn switch
        {
            AdviserOrderColumn.RequestedCount
                => period > 0
                    ? adviser.BatchIntroductions.Count(
                        b =>
                            b.Status == BatchIntroductionStatus.Request
                            && b.CreatedTime
                                > DateTime.UtcNow.AddDays(period * -1)
                    )
                    : adviser.BatchIntroductions.Count(
                        b => b.Status == BatchIntroductionStatus.Request
                    ),

            AdviserOrderColumn.AccessCount
                => period > 0
                    ? adviser.BatchIntroductions.Count(
                        b =>
                            b.Status == BatchIntroductionStatus.Introduction
                            && b.CreatedTime
                                > DateTime.UtcNow.AddDays(period * -1)
                    )
                    : adviser.BatchIntroductions.Count(
                        b => b.Status == BatchIntroductionStatus.Introduction
                    ),

            _ => throw new RequestDataError()
        };
    }

    public int GetAdviserRankByAccessCount(Adviser adviser, int period = 30)
    {
        var targetCount = CountAdviserOrderColumn(
            adviser,
            AdviserOrderColumn.AccessCount,
            period
        );
        var betterCount = _context.Advisers
            .Select(
                a =>
                    a.BatchIntroductions
                        .Where(
                            b =>
                                b.Status == BatchIntroductionStatus.Introduction
                                && b.CreatedTime
                                    > DateTime.UtcNow.AddDays(period * -1)
                        )
                        .Count()
            )
            .Count(count => count > targetCount);
        return betterCount + 1;
    }

    /// <summary>
    /// アドバイザー個人と会社の資産範囲設定をマージ
    /// </summary>
    /// <param name="adviserAcceptAssetRanges">アドバイザー個人の資産範囲設定</param>
    /// <param name="companyAcceptAssetRanges">会社の資産範囲設定</param>
    /// <returns>会社と個人でマージされた資産範囲設定</returns>
    private static IEnumerable<AssetRanges> MergeAdviserCompanyAcceptAssetRanges(
        IEnumerable<AssetRanges>? adviserAcceptAssetRanges,
        IEnumerable<AssetRanges>? companyAcceptAssetRanges
    )
    {
        if (adviserAcceptAssetRanges == null || !adviserAcceptAssetRanges.Any())
            return companyAcceptAssetRanges ?? Enumerable.Empty<AssetRanges>();

        if (companyAcceptAssetRanges == null || !companyAcceptAssetRanges.Any())
            return adviserAcceptAssetRanges;

        return adviserAcceptAssetRanges.Intersect(companyAcceptAssetRanges);
    }

    /// <summary>
    /// カレンダー表示可能なアドバイザーを資産・都道府県マトリックスでCSV出力
    /// </summary>
    /// <returns>CSV形式のMemoryStream</returns>
    public async Task<MemoryStream> GetCalendarEligibleAdvisors()
    {
        var advisers = await AcceptedAdviserQueryForCalendar
            .Where(a => a.GoogleToken != null || a.MicrosoftTokenToken != null)
            .Include(a => a.BatchIntroductionSetting)
            .ThenInclude(b => b != null ? b.AcceptAssetRanges : null)
            .Include(a => a.IfaCompany)
            .ThenInclude(
                c => c != null ? c.BatchIntroductionIfaCompanySetting : null
            )
            .ThenInclude(s => s != null ? s.AcceptAssetRanges : null)
            .Include(a => a.WebMeetingPrefectures)
            .AsNoTracking()
            .ToListAsync();

        var allAssetRanges = Enum.GetValues<AssetRanges>().ToList();
        var allPrefectures = Enum.GetValues<Prefectures>().ToList();

        var adviserMatrix = allAssetRanges
            .SelectMany(
                ar =>
                    allPrefectures.Select(
                        p => new { AssetRange = ar, Prefecture = p }
                    )
            )
            .ToDictionary(
                x => (x.AssetRange, x.Prefecture),
                x => new List<string>()
            );

        var adviserData = advisers
            .Where(a => a.WebMeetingPrefectures?.Any() == true)
            .SelectMany(adviser =>
            {
                var adviserName = $"{adviser.FamilyName} {adviser.FirstName}";

                // アドバイザー個人の資産範囲設定
                var adviserAcceptAssetRanges =
                    adviser.BatchIntroductionSetting?.AcceptAssetRanges?.Select(
                        ar => ar.Id
                    );

                // 会社の資産範囲設定
                var companyAcceptAssetRanges =
                    adviser.IfaCompany?.BatchIntroductionIfaCompanySetting?.AcceptAssetRanges?.Select(
                        ar => ar.Id
                    );

                // アドバイザーと会社の資産範囲をマージ
                var acceptableAssetRanges =
                    MergeAdviserCompanyAcceptAssetRanges(
                            adviserAcceptAssetRanges,
                            companyAcceptAssetRanges
                        )
                        .ToList();

                // マージ結果が空の場合は全ての資産範囲に対応可能とみなす
                if (!acceptableAssetRanges.Any())
                {
                    acceptableAssetRanges = allAssetRanges;
                }

                var acceptablePrefectures =
                    adviser.WebMeetingPrefectures.Select(p => p.Id);

                return acceptableAssetRanges.SelectMany(
                    ar =>
                        acceptablePrefectures.Select(
                            p =>
                                new
                                {
                                    AssetRange = ar,
                                    Prefecture = p,
                                    AdviserName = adviserName
                                }
                        )
                );
            })
            .GroupBy(x => (x.AssetRange, x.Prefecture))
            .ToList();

        foreach (var group in adviserData)
        {
            adviserMatrix[group.Key].AddRange(group.Select(x => x.AdviserName));
        }

        var stream = new MemoryStream();
        using (
            var writer = new StreamWriter(
                stream,
                Encoding.UTF8,
                bufferSize: -1,
                leaveOpen: true
            )
        )
        {
            var header = new List<string> { "資産範囲" };
            header.AddRange(
                allPrefectures.Select(
                    p => ModelUtils.GetDisplayName(_context.Prefectures, p)
                )
            );
            writer.WriteLine(string.Join(",", header.Select(EscapeCsvField)));

            foreach (var assetRange in allAssetRanges)
            {
                var row = new List<string>
                {
                    ModelUtils.GetDisplayName(_context.AssetRanges, assetRange)
                };

                foreach (var prefecture in allPrefectures)
                {
                    var advisersForCell = adviserMatrix[
                        (assetRange, prefecture)
                    ];
                    var cellValue = string.Join("\n", advisersForCell);
                    row.Add(cellValue);
                }

                writer.WriteLine(string.Join(",", row.Select(EscapeCsvField)));
            }

            writer.Flush();
        }
        stream.Position = 0;
        return stream;
    }

    /// <summary>
    /// CSV用にフィールドをエスケープ
    /// </summary>
    private static string EscapeCsvField(string field)
    {
        if (string.IsNullOrEmpty(field))
            return "";

        if (field.Contains(",") || field.Contains("\"") || field.Contains("\n"))
        {
            return $"\"{field.Replace("\"", "\"\"")}\"";
        }

        return field;
    }
}
