using Adnavi.Domain.Models.Profiles;
using Adnavi.Utils.Models;
using NUlid;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Adnavi.Domain.Models.Consultations.InsuranceBatchIntroductions;

namespace Adnavi.Domain.Models.Consultations.BatchIntroductions;

/// <summary>
/// EvaluationRequest
/// /// </summary>
/// <seealso cref="StaffEvaluationRequestData" />
public class EvaluationRequest : IHasId
{
    [Required]
    public Ulid Id { get; set; }

    [Required]
    public Guid AccessKey { get; set; }

    [Required]
    public EvaluationStatuses Status { get; set; }

    public Ulid? BatchIntroductionId { get; set; }
    public virtual BatchIntroduction? BatchIntroduction { get; set; }

    public Ulid? InsuranceBatchIntroductionId { get; set; }
    public virtual InsuranceBatchIntroduction? InsuranceBatchIntroduction { get; set; }

    public Ulid? CustomerSurveyId { get; set; }
    public virtual CustomerSurvey? CustomerSurvey { get; set; }
    public Ulid? CustomerEvaluationId { get; set; }
    public virtual CustomerEvaluation? CustomerEvaluation { get; set; }
    public virtual CustomerBranchEvaluation? CustomerBranchEvaluation { get; set; }

    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public DateTime CreatedTime { get; set; }

    [DatabaseGenerated(DatabaseGeneratedOption.Computed)]
    public DateTime ModifiedTime { get; set; }
}
