﻿using System.ComponentModel.DataAnnotations.Schema;
using Adnavi.Domain.Logics.Invoices.MatchingInvoices;
using Adnavi.Utils;
using Adnavi.Utils.Models;
using Microsoft.EntityFrameworkCore;
using NUlid;

namespace Adnavi.Domain.Models.Invoices.MatchingInvoices;

/// <summary>
/// 紹介請求情報
/// </summary>
/// <seealso cref="IntroductionInvoiceSummaryData"/>
/// <seealso cref="RefundRequestData"/>
/// <seealso cref="StaffIntroductionInvoiceData"/>
[Index(nameof(SequenceNumber), IsUnique = true)]
public class IntroductionInvoice : IHasId
{
    /// <summary>ID</summary>
    public Ulid Id { get; set; }

    /// <summary>紹介請求管理ID</summary>
    public Ulid MatchingInvoiceManagementId { get; set; }

    /// <summary>紹介請求管理</summary>
    public virtual MatchingInvoiceManagement MatchingInvoiceManagement { get; set; }

    public int? SequenceNumber { get; set; }

    ///<summary>請求タイプ</summary>
    public InvoiceTypes InvoiceType { get; set; }

    ///<summary>請求状況</summary>
    public InvoiceStatuses InvoiceStatus { get; set; } = InvoiceStatuses.Hold;

    /// <summary>請求月</summary>
    public YearAndMonth InvoiceMonth { get; set; }

    /// <summary>作成日</summary>
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public DateTime? CreatedTime { get; set; }

    /// <summary>修正日</summary>
    [DatabaseGenerated(DatabaseGeneratedOption.Computed)]
    public DateTime? ModifiedTime { get; set; }

    /// <summary>請求金額</summary>
    public int? InvoiceAmount { get; set; }

    /// <summary>適用キャンペーン名</summary>
    public string? PromotionId { get; set; }

    /// <summary>返金元請求</summary>
    public Ulid? RefundInvoiceId { get; set; }

    /// <summary>返金申請事由</summary>
    public string RefundReason { get; set; } = "";

    /// <summary>備考</summary>
    public string Remarks { get; set; } = "";

    public IntroductionInvoice() { }
}
