using Adnavi.Domain.Models.PhoneAppointment;
using CsvHelper.Configuration.Attributes;
using Microsoft.EntityFrameworkCore;

namespace Adnavi.Domain.Logics.PhoneAppointment;

public sealed class PhoneAppointmentCsvRecord
{
    [Name("ID")]
    public string Id { get; set; }

    [Name("作成日時")]
    public DateTime CreatedTime { get; set; }

    [Name("更新日時")]
    public DateTime? ModifiedTime { get; set; }

    [Name("担当スタッフID")]
    public string? AssignedStaffId { get; set; }

    [Name("ステータス")]
    public string Status { get; set; }

    [Name("通話回数")]
    public int CallCount { get; set; }

    [Name("姓")]
    public string FamilyName { get; set; }

    [Name("名")]
    public string FirstName { get; set; }

    [Name("姓(かな)")]
    public string FamilyNameKana { get; set; }

    [Name("名(かな)")]
    public string FirstNameKana { get; set; }

    [Name("メールアドレス")]
    public string EMail { get; set; }

    [Name("電話番号")]
    public string TelephoneNumber { get; set; }

    [Name("年齢")]
    public int Age { get; set; }

    [Name("職業")]
    public string InvestorOccupationType { get; set; }

    [Name("都道府県")]
    public string Prefecture { get; set; }

    [Name("年収")]
    public string AnnualIncome { get; set; }

    [Name("金融資産")]
    public string AssetRange { get; set; }

    [Name("同意済")]
    public bool AgreementsAccepted { get; set; }

    [Name("メールマガジン同意済")]
    public bool MailMagazineAccepted { get; set; }

    [Name("資産運用をしているか")]
    public bool IsAssetManagement { get; set; }

    [Name("関心：株式")]
    public bool AssetManagementInterestStock { get; set; }

    [Name("関心：投資信託")]
    public bool AssetManagementInterestInvestmentTrust { get; set; }

    [Name("関心：債権")]
    public bool AssetManagementInterestClaims { get; set; }

    [Name("関心：保険")]
    public bool AssetManagementInterestInsurance { get; set; }

    [Name("関心：不動産")]
    public bool AssetManagementInterestRealEstate { get; set; }

    [Name("関心：その他")]
    public bool AssetManagementInterestOthers { get; set; }

    [Name("相談の意思")]
    public string ConsultationInterests { get; set; }

    [Name("申し込み元URL")]
    public string OriginUrl { get; set; }

    [Name("メモ")]
    public string Memo { get; set; }

    [Name("電話番号承認状況")]
    public bool? IsTelephoneNumberVerified { get; set; }

    [Name("回答社名")]
    public string? UtmSource { get; set; }

    private PhoneAppointmentCsvRecord() { }

    public static async IAsyncEnumerable<PhoneAppointmentCsvRecord> FromQuery(
        AdnaviDomainContext context
    )
    {
        var query = context.PhoneAppointmentSurveys.AsNoTracking();
        var verifiedPhoneNumbers = context.PhoneNumberVerifications
            .AsNoTracking()
            .Where(x => x.IsVerified)
            .Select(x => x.PhoneNumber)
            .ToList();

        var result = query.Select<
            PhoneAppointmentSurvey,
            PhoneAppointmentCsvRecord
        >(
            a =>
                new PhoneAppointmentCsvRecord
                {
                    Id = a.Id.ToString(),
                    AssignedStaffId = a.AssignedStaffId.ToString(),
                    Age = a.Age,
                    InvestorOccupationType =
                        a.InvestorOccupationType.ToString(),
                    Prefecture = a.Prefecture.ToString(),
                    AnnualIncome = a.AnnualIncome.ToString(),
                    AssetRange = a.AssetRange.ToString(),
                    IsAssetManagement = a.IsAssetManagement ?? false,
                    AssetManagementInterestStock =
                        a.AssetManagementInterestStock ?? false,
                    AssetManagementInterestInvestmentTrust =
                        a.AssetManagementInterestInvestmentTrust ?? false,
                    AssetManagementInterestClaims =
                        a.AssetManagementInterestClaims ?? false,
                    AssetManagementInterestInsurance =
                        a.AssetManagementInterestInsurance ?? false,
                    AssetManagementInterestRealEstate =
                        a.AssetManagementInterestRealEstate ?? false,
                    AssetManagementInterestOthers =
                        a.AssetManagementInterestOthers ?? false,
                    ConsultationInterests = (
                        a.ConsultationInterests
                        ?? Models.PhoneAppointment.ConsultationInterests.No
                    ).ToString(),
                    FamilyName = a.FamilyName,
                    FirstName = a.FirstName,
                    FamilyNameKana = a.FamilyNameKana,
                    FirstNameKana = a.FirstNameKana,
                    EMail = a.EMail,
                    TelephoneNumber = a.TelephoneNumber,
                    AgreementsAccepted = a.AgreementsAccepted,
                    MailMagazineAccepted = a.MailMagazineAccepted,
                    Status = a.Status.ToString(),
                    CallCount = a.CallCount,
                    CreatedTime = a.CreatedTime,
                    ModifiedTime = a.ModifiedTime,
                    OriginUrl = a.OriginUrl ?? "",
                    Memo = a.Memo ?? string.Empty,
                    IsTelephoneNumberVerified = verifiedPhoneNumbers.Contains(
                        a.TelephoneNumber
                    ),
                    UtmSource = GetUtmSourceFromUrl(a.OriginUrl)
                }
        );

        await foreach (var record in result.AsAsyncEnumerable())
        {
            yield return record;
        }
    }

    private static string? GetUtmSourceFromUrl(string? url)
    {
        if (string.IsNullOrEmpty(url))
            return null;

        try
        {
            var uri = new Uri(url);
            var queryParams = System.Web.HttpUtility.ParseQueryString(
                uri.Query
            );
            return queryParams["utm_source"];
        }
        catch
        {
            return null;
        }
    }
}
