using Adnavi.Utils.Exceptions;

namespace Adnavi.Domain.Logics.Consultations.ConsultationRequests;

public class CSIntroductionSingleOnlyError : BadRequestException
{
    private readonly string _companyName;
    private readonly int _advisorCount;

    public CSIntroductionSingleOnlyError(string companyName, int advisorCount)
    {
        _companyName = companyName;
        _advisorCount = advisorCount;
    }

    public override string Message =>
        $"会社『{_companyName}』では、アドバイザーのご紹介は1名までとなっております。{_advisorCount}人を選択することはできません。";
}
