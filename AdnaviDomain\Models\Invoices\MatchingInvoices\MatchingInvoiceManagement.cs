﻿using System.ComponentModel.DataAnnotations.Schema;
using Adnavi.Domain.Models.Consultations.BatchIntroductions;
using Adnavi.Domain.Models.Consultations.InsuranceBatchIntroductions;
using Adnavi.Domain.Models.Profiles.AdviserProfiles;
using Adnavi.Domain.Models.Profiles.IfaCompanyProfiles;
using Adnavi.Utils.Models;
using NUlid;

namespace Adnavi.Domain.Models.Invoices.MatchingInvoices;

/// <summary>
/// 紹介請求管理情報
/// </summary>
/// <seealso cref="MatchingInvoiceManagementSummaryData"/>
/// <seealso cref="AdviserIfaCompanyInvoiceHeaderData" />
public class MatchingInvoiceManagement : IHasId
{
    /// <summary>ID</summary>
    public Ulid Id { get; set; }

    /// <summary>一括紹介</summary>
    public virtual BatchIntroduction BatchIntroduction { get; set; }

    // 記録ように投資家名を保存しておく。他の用途で使用しない。
    [Obsolete("For recording. Use BatchIntroduction instead.")]
    public string? InvestorName { get; set; }

    public virtual InsuranceBatchIntroduction? InsuranceBatchIntroduction { get; set; }

    /// <summary>アドバイザーId</summary>
    public Ulid AdviserId { get; set; }

    /// <summary>アドバイザー</summary>
    public virtual Adviser Adviser { get; set; }

    /// <summary>アドバイザー名</summary>
    public string? AdviserFullName { get; set; }

    /// <summary>IFA企業ID</summary>
    public Ulid? IfaCompanyId { get; set; }

    /// <summary>IFA企業名</summary>
    public virtual IfaCompany? IfaCompany { get; set; }

    /// <summary>請求リスト</summary>
    public virtual ICollection<IntroductionInvoice> Invoices { get; set; }

    /// <summary>面談日付</summary>
    public DateTime? InterviewDate { get; set; }

    /// <summary>進捗状況</summary>
    public ProgressStatuses? ProgressStatus { get; set; }

    /// <summary>送客クオリティー</summary>
    public SendingQualities? Quality { get; set; }

    /// <summary>詳細</summary>
    public string? Details { get; set; }

    /// <summary>作成時刻</summary>
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public DateTime? CreatedTime { get; set; }

    /// <summary>修正時刻</summary>
    [DatabaseGenerated(DatabaseGeneratedOption.Computed)]
    public DateTime? ModifiedTime { get; set; }

    [NotMapped]
    public bool ModificationRequired =>
        ProgressStatus != null
        && (
            ProgressStatus == ProgressStatuses.Hold
            || ProgressStatus == ProgressStatuses.WaitingReply
            || ProgressStatus == ProgressStatuses.ScheduledInterview
        );

    public MatchingInvoiceManagement() { }

    public MatchingInvoiceManagement(BatchIntroduction batch, Adviser adviser)
    {
        BatchIntroduction = batch;

        // 記録のために投資家名を保管しておく
#pragma warning disable CS0618 // Type or member is obsolete
        InvestorName = batch.Name;
#pragma warning restore CS0618 // Type or member is obsolete

        Adviser = adviser;
        AdviserId = adviser.Id;
        AdviserFullName = adviser.FullName;
        IfaCompanyId = adviser.IfaCompanyId;
        Invoices = new List<IntroductionInvoice>();
        ProgressStatus = ProgressStatuses.Hold;
        Details = "";
    }
}
