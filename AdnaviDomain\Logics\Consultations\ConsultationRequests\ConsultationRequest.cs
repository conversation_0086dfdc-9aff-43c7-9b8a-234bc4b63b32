using Adnavi.Domain.Models;
using Adnavi.Domain.Models.Consultations.ConsultationRequests;
using Adnavi.Utils;
using Adnavi.Utils.Exceptions;
using NUlid;
using Serilog;
using Adnavi.Domain.DTOs.Consultations.ConsultationRequests;
using Adnavi.Domain.Logics.Common.EMails;

namespace Adnavi.Domain.Logics.Consultations.ConsultationRequests;

public class ConsultationRequest
{
    private readonly InvestorsSettings _settings;
    private readonly SmtpServerSettings _smtp;
    private readonly AdnaviDomainContext _context;
    private readonly ModelUtils _model;
    private readonly EMailService _emailService;

    public ConsultationRequest(
        InvestorsSettings settings,
        SmtpServerSettings smtp,
        ModelUtils model,
        EMailService emailService,
        AdnaviDomainContext context
    )
    {
        _settings = settings;
        _smtp = smtp;
        _context = context;
        _emailService = emailService;
        _model = model;
    }

    private class MailTemplateFactory
    {
        private static bool IsInsuranceTemplate(ConsultationRequestData data) =>
            data.OriginName == "lpi07";

        public static IConsultationRequestMailTemplate ChooseMailTemplate(
            ConsultationRequestData data,
            string timerexUrl,
            AdnaviDomainContext context,
            InvestorsSettings settings
        )
        {
            if (IsInsuranceTemplate(data))
            {
                return new InsuranceConsultationRequestMailTemplate(
                    data,
                    timerexUrl,
                    context,
                    settings
                );
            }
            else
            {
                return new ConsultationRequestMailTemplate(
                    data,
                    timerexUrl,
                    context,
                    settings
                );
            }
        }
    }

    public async Task SendMail(
        ConsultationRequestData data,
        Ulid requestId,
        string remoteHost
    )
    {
        var originName2 = data.OriginName?.Replace("-", "/") ?? "";

        // 送信元によってTimerexのURLを変える。
        var timerexUrl = data.OriginName switch
        {
            "lp-31-am"
                => $"https://adviser-navi.co.jp/{originName2}/thanks/?request_id={requestId}&s=mail",

            "lp05"
            or "lp06"
            or "lp07"
            or "lp08"
            or "lpi07"
            or "lp23"
                => $"https://adviser-navi.co.jp/{data.OriginName}/thanks/?request_id={requestId}&s=mail&assetRange={data.AssetRange}",

            "wac_contact"
                => $"http://adviser-navi.co.jp/wac/thanks.php?request_id={requestId}&s=mail",

            _
                => $"https://adviser-navi.co.jp/a/mail/consultation-request/thanks/?request_id={requestId}&s=mail",
        };

        Log.Information(
            "Contact data by {MailAddress} from {OriginUrl}, timerexUrl: {TimerexUrl}",
            data.MailAddress,
            data.OriginUrl,
            timerexUrl
        );

        var mailTemplate = MailTemplateFactory.ChooseMailTemplate(
            data,
            timerexUrl,
            _context,
            _settings
        );

        var error = false;

        var notificationContents = mailTemplate.GetContentsForNotification(
            requestId,
            remoteHost
        );

        // 社内通知用メールアドレスに送る
        foreach (
            var mail in mailTemplate
                .GetNotifications()
                .NotificationMailAddresses
        )
        {
            error =
                !await _emailService.TrySendMail(
                    mailTemplate.GetNotificationSubject(data.OriginName ?? ""),
                    mailTemplate.GetFromMailAddress(),
                    mail,
                    notificationContents,
                    data.OriginUrl
                ) || error;
        }

        // 申し込んだ投資家に返信する
        error =
            !await _emailService.TrySendMail(
                mailTemplate.GetInvestorSubject(),
                mailTemplate.GetFromMailAddress(),
                data.MailAddress,
                mailTemplate.GetContentsForInvestor(),
                data.OriginUrl
            ) || error;

        if (error)
            throw new NotificationMailError(data.OriginUrl, data.MailAddress);
    }

    public async Task Record(
        ConsultationRequestData data,
        Ulid requestId,
        string remoteHost
    )
    {
        _model.Create(
            new AdviserConsultationRequest
            {
                Name = data.Name,
                TelephoneNumber = data.TelephoneNumber,
                Age = data.Age,
                MailAddress = data.MailAddress,
                ContactMethod = data.ContactMethod,
                AssetRange = data.AssetRange,
                ConsultationTypes =
                    (ICollection<ConsultationRequestTypes>)
                        data.ConsultationTypes,
                Contents = data.Contents,
                //AppointedAdviserId = data.AppointedAdviserId,
                OriginUrl = data.OriginUrl,
                OriginName = data.OriginName,
                RemoteHost = remoteHost,
                DepositAssets = data.DepositAssets,
                StockAssets = data.StockAssets,
                InsuranceAssets = data.InsuranceAssets,
                OtherAssets = data.OtherAssets,
                RelatedFinancialInstitutions =
                    data.RelatedFinancialInstitutions,
                ChargeStaff = "",
                SiteName = "",
                PointStatus = PointStatuses.Unconfirmed,
                StaffRemarks = "",
                Prefecture = data.Prefecture,
            },
            requestId
        );
        await _context.SaveChangesAsync();
    }
}
