using System.Runtime.CompilerServices;
using Adnavi.Domain.DTOs.Consultations.BatchIntroductions;
using Adnavi.Domain.DTOs.Profiles.AdviserProfiles;
using Adnavi.Domain.Logics.Accounts;
using Adnavi.Domain.Models.Profiles.AdviserProfiles;
using Adnavi.Domain.Models.Profiles.IfaCompanyProfiles;
using Adnavi.Utils.Images;
using Microsoft.Extensions.Logging;
using NUlid;

namespace Adnavi.Domain.Logics.Common;

public class LogDictionary
{
    private readonly ILogger<LogDictionary> _logger;
    private readonly AccessUserLogic _accessUser;

    public LogDictionary(
        ILogger<LogDictionary> logger,
        AccessUserLogic accessUser
    )
    {
        _logger = logger;
        _accessUser = accessUser;
    }

    #region private methods
    private string? Name([CallerMemberName] string? caller = null) => caller;

    private record LogData(string Key, object Value);

    /// <summary>
    /// レベルと追加情報を指定してログを出力。
    /// </summary>
    /// <param name="logLevel">ログレベル</param>
    /// <param name="name">ログメッセージ</param>
    /// <param name="data">追加情報</param>
    private void Log(LogLevel logLevel, string? name, params LogData[] data)
    {
        // 出力パラメーターにアクセスしたユーザーのIDとEMailを追加
        var user = _accessUser.GetCurrentUserCertificationOrNull();
        var allData = new List<LogData>
        {
            new("UserId", _accessUser.GetRequestUserId() ?? ""),
            new("UserEMail", user?.EMail ?? ""),
        };
        allData.AddRange(data);

        // 各パラメーターのキーを"Key={Key}"の形式にして、"|"で接続してメッセージの後ろに出力
        string message =
            $"{name}|"
            + string.Join("|", allData.Select(x => $"{x.Key}={{{x.Key}}}"));

        // パラメーターの値を配列に変換
        var parameters = allData.Select(x => x.Value).ToArray();

        // パラメーターを指定してログ出力
        _logger.Log(logLevel, message, parameters);
    }
    #endregion

    /// <summary>IFA法人の一括紹介の設定が変更されました。</summary>
    public void IfaCompanyBatchIntroductionSettingIsChanged(
        IfaCompany ifaCompany,
        BatchIntroductionIfaCompanySettingData data
    )
    {
        Log(
            LogLevel.Information,
            $"LOG_DICT:{Name()}",
            new LogData("IfaCompanyId", ifaCompany.Id),
            new LogData("IfaCompanyName", ifaCompany.Name),
            new LogData("Data", data)
        );
    }

    /// <summary>IFAからお問い合わせフォームにリクエストがありました。</summary>
    public void AdviserContactFormRequest(
        Adviser adviser,
        AdviserContactFormRequestData data
    )
    {
        Log(
            LogLevel.Information,
            $"LOG_DICT:{Name()}",
            new LogData("AdviserId", adviser.Id),
            new LogData("ContactFormType", data.ContactFormType),
            new LogData("Detail", data.Detail),
            new LogData(
                "ReasonOfInvoiceExempt",
                data.ReasonOfInvoiceExempt
                    ?? AdviserReasonOfInvoiceExempt.NotSet
            ),
            new LogData(
                "ContactInvestorLessThanThreeHours",
                data.ContactInvestorLessThanThreeHours ?? false
            ),
            new LogData("SubmitEvidence", data.SubmitEvidence ?? false)
        );
    }

    /// <summary>申し込み不許可メールドメインリストに登録されたメールアドレスからの申し込みです。</summary>
    public void DisallowedRequestByDomainList(string eMail)
    {
        Log(
            LogLevel.Warning,
            $"LOG_DICT:{Name()}",
            new LogData("EMail", eMail)
        );
    }

    /// <summary>同一Ipアドレスからの申し込みが存在します</summary>
    public void DuplicateRequestBySameIp(string ipAddress, string? AdnParentId)
    {
        Log(
            LogLevel.Warning,
            $"LOG_DICT:{Name()}",
            new LogData("IpAddress", ipAddress),
            new LogData("AdnParentId", AdnParentId ?? "")
        );
    }

    /// <summary>申し込み不許可IPリストに登録されたIPアドレスからの申し込みです。</summary>
    public void DisallowedRequestByIpList(string ipAddress)
    {
        Log(
            LogLevel.Warning,
            $"LOG_DICT:{Name()}",
            new LogData("EMail", ipAddress)
        );
    }

    /// <summary>IFAが請求書のPDFをダウンロードしました。</summary>
    public void IfaCompanyInvoicesPDFIsDownloaded(
        Ulid companyId,
        string ifaCompanyName,
        string fileName
    )
    {
        Log(
            LogLevel.Information,
            $"LOG_DICT:{Name()}",
            new LogData("CompanyId", companyId),
            new LogData("IfaCompanyName", ifaCompanyName),
            new LogData("FileName", fileName)
        );
    }

    /// <summary>アドバイザーのアイキャッチ画像の生成に失敗しました。</summary>
    public void AdviserEyeCatchImageGenerationFailed(
        string profile_image_key,
        string message
    )
    {
        Log(
            LogLevel.Error,
            $"LOG_DICT:{Name()}",
            new LogData("Adviser.NewPhotoFileKey", profile_image_key),
            new LogData("ErrorMessage", message)
        );
    }

    /// <summary>
    /// 画像リサイズの元画像サイズが上限を上回りました。
    /// </summary>
    /// <param name="resizedKey">The key of the image being processed</param>
    /// <param name="type">The type requested</param>
    /// <param name="width">The width requested</param>
    /// <param name="height">The height requested</param>
    public void ImageResizeSourceSizeExceeded(
        string resizedKey,
        ImageFileTypes type,
        int width,
        int height
    )
    {
        Log(
            LogLevel.Warning,
            $"LOG_DICT:{Name()}",
            new LogData("FileName", resizedKey),
            new LogData("Type", type.ToString()),
            new LogData("Width", width),
            new LogData("Height", height)
        );
    }

    /// <summary>
    /// 画像リサイズに失敗しました。
    /// </summary>
    /// <param name="resizedKey">The key of the image being processed</param>
    /// <param name="type">The type requested</param>
    /// <param name="width">The width requested</param>
    /// <param name="height">The height requested</param>
    /// <param name="message">The error message describing the failure</param>

    public void ImageResizeFailed(
        string resizedKey,
        ImageFileTypes type,
        int width,
        int height,
        string message
    )
    {
        Log(
            LogLevel.Error,
            $"LOG_DICT:{Name()}",
            new LogData("FileName", resizedKey),
            new LogData("Type", type.ToString()),
            new LogData("Width", width),
            new LogData("Height", height),
            new LogData("ErrorMessage", message)
        );
    }

    /// <summary>
    /// プライバシー情報を削除しました。
    /// </summary>
    /// <param name="name"></param>
    /// <param name="eMail"></param>
    public void DeletePrivacyInformation(string name, string eMail)
    {
        Log(
            LogLevel.Information,
            $"LOG_DICT:{Name()}",
            new LogData("Name", name),
            new LogData("EMail", eMail)
        );
    }

    /// <summary>
    /// 終日イベントの開始日、終了日のパースに失敗しました。
    /// </summary>
    /// <param name="startDate"></param>
    /// <param name="endDate"></param>
    public void AllDayEventDateParseFailed(string startDate, string endDate)
    {
        Log(
            LogLevel.Warning,
            $"LOG_DICT:{Name()}",
            new LogData("StartDate", startDate),
            new LogData("EndDate", endDate)
        );
    }
}
