using System.Runtime.Serialization;
using Adnavi.Utils.Exceptions;

namespace Adnavi.Domain.Logics.Consultations.BatchIntroductions;

internal class CompanyHasNotChangingSettingRangePermissionError
    : ForbiddenException
{
    public CompanyHasNotChangingSettingRangePermissionError()
        : base(
            "Cannot change setting range because company has not changing setting range permission."
        ) { }
}
