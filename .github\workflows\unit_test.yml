name: Unit Test

on: [push]

env:
  AWS_SECRET_ACCESS_KEY: ${{ secrets.d1_AWS_SECRET_ACCESS_KEY }}

  AWS_REGION: ${{ vars.d1_AWS_REGION }}
  AWS_ACCESS_KEY_ID: ${{ vars.d1_AWS_ACCESS_KEY_ID }}
  ECR_REPOSITORY: ${{ vars.d1_ECR_REPOSITORY }}

  API_URL: "http://127.0.0.1:9000/swagger/v1/swagger.json"
  NODE_VERSION: 18.5

jobs:
  build:
    runs-on: ubuntu-latest
    timeout-minutes: 15
    if: ${{ !endsWith(github.event.repository.name, '-release') }}
    strategy:
      matrix:
        dotnet-version: ["6.0.x"]

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ env.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ env.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Setup .NET Core SDK ${{ matrix.dotnet-version }}
        uses: actions/setup-dotnet@v2
        with:
          dotnet-version: ${{ matrix.dotnet-version }}

      - name: Install format check tool.
        run: dotnet tool install csharpier --version 0.23.0 -g
      - name: Install format check tool2.
        run: dotnet tool restore
      - name: Check format.
        run: dotnet csharpier --check .

      - name: Check spell.
        run: npx cspell "**" 2>/dev/null

      - name: Check Line Limit
        run: |
          chmod +x ./scripts/check_line_limit.sh
          ./scripts/check_line_limit.sh

      - name: Install dependencies
        run: dotnet restore
      - name: Build
        run: dotnet build --configuration Release --no-restore --warnaserror

      - name: Test
        run: dotnet test --no-restore --verbosity normal 2>&1
