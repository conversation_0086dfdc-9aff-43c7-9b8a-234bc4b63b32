using Adnavi.Domain.Models.Commons;
using Adnavi.Domain.Models.Consultations.BatchIntroductions;
using Adnavi.Domain.Models.Profiles.AdviserProfiles;
using Adnavi.Domain.Models.Profiles.InvestorProfiles;
using CsvHelper.Configuration.Attributes;

namespace Adnavi.Domain.Logics.Invoices.MatchingInvoices;

public class MatchingInvoiceManagementCsvRecord
{
    [Name("#")]
    public string Id { get; set; }

    [Name("お名前")]
    public string Name { get; set; }

    [Name("年齢")]
    public int Age { get; set; }

    [Name("金融資産")]
    public string AssetRange { get; set; }

    [Name("性別")]
    public string Gender { get; set; }

    [Name("希望する連絡方法")]
    public string PreferredFirstContactMethod { get; set; }

    [Name("携帯電話番号")]
    public string TelephoneNumber { get; set; }

    [Name("メールアドレス")]
    public string EMail { get; set; }

    [Name("都道府県")]
    public string Prefecture { get; set; }

    [Name("職業")]
    public string InvestorOccupationType { get; set; }

    [Name("相談したい内容")]
    public string AdviserWorkType { get; set; }

    [Name("希望する面談方法")]
    public string ContactMethod { get; set; }

    [Name("カスタマーサポート連絡欄")]
    public string? InvestorRemarks { get; set; }
}
