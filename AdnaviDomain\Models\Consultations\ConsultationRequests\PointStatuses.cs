﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnavi.Domain.Models.Consultations.ConsultationRequests;

public enum PointStatuses
{
    /// <summary>未確認</summary>
    Unconfirmed = 100,

    /// <summary>認証</summary>
    Approved = 200,

    /// <summary>非認証</summary>
    Rejected = 300,

    /// <summary>保留</summary>
    Hold = 400,

    /// <summary>対象外</summary>
    NotApplicable = 500,
}
