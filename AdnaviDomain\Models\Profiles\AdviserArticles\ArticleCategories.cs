﻿namespace Adnavi.Domain.Models.Profiles.AdviserArticles;

public enum ArticleCategories
{
    /// <summary>IR</summary>
    Ir = 1000,

    /// <summary>NISA・iDeCo</summary>
    Nisa = 1100,

    /// <summary>オルタナティブ</summary>
    Alternative = 1200,

    /// <summary>不動産投資</summary>
    RealEstate = 1300,

    /// <summary>保険</summary>
    Insurance = 1400,

    /// <summary>債券</summary>
    Bonds = 1500,

    /// <summary>投資信託・ファンド</summary>
    InvestmentTrust = 1600,

    /// <summary>株式</summary>
    Stocks = 1700,

    /// <summary>相続</summary>
    Inheritance = 1800,

    /// <summary>経済</summary>
    Economics = 1900,

    /// <summary>証券会社</summary>
    Securities = 2000,

    /// <summary>資産運用・資産管理</summary>
    AssetManagement = 2100,

    /// <summary>退職金</summary>
    Retirement = 2200,

    /// <summary>通貨</summary>
    Currencies = 2300,
}
