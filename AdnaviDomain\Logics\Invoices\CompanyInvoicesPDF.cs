using Adnavi.Utils;
using Adnavi.Utils.PDFs;
using NUlid;
using Microsoft.Extensions.Options;
using Adnavi.Domain.DTOs.Invoices;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Adnavi.Domain.Logics.Invoices;

public class UploadData
{
    public UploadData(
        string assignedUrl,
        Ulid companyId,
        PDFFileTypes contentType
    )
    {
        AssignedUrl = assignedUrl;
        CompanyId = companyId;
        ContentType = contentType;
    }

    [Required]
    public string AssignedUrl { get; set; }

    [Required]
    public Ulid CompanyId { get; set; }

    [Required]
    public PDFFileTypes ContentType { get; set; }
};

public class InvoicesPDF
{
    private readonly IOptions<AdnaviS3Settings> _settings;
    private const string _pdfRootPath = "invoices";
    private const string _documentsPath = "documents";
    private const string _downloadCountsPath = "download_counts";

    private readonly Dictionary<
        PDFFileTypes,
        string
    > _pdfFileTypeToContentTypeMap;

    private readonly S3Utils _s3Utils;

    public InvoicesPDF(IOptions<AdnaviS3Settings> settings)
    {
        _settings = settings;
        _pdfFileTypeToContentTypeMap = new()
        {
            { PDFFileTypes.Pdf, "downloads/pdf" }
        };
        _s3Utils = new S3Utils(
            _settings.Value.S3DefaultRegion,
            _settings.Value.ApiPrivateBucketName
        );
    }

    private string GetDocumentKey(Ulid companyId, string fileName) =>
        $"{_pdfRootPath}/companies/{companyId}/{_documentsPath}/{fileName}";

    private string GetIFADownloadCountKey(Ulid companyId, string fileName) =>
        $"{_pdfRootPath}/companies/{companyId}/{_downloadCountsPath}/{fileName}";

    public async Task IncrementIFADownloadCount(Ulid companyId, string fileName)
    {
        var countKey = GetIFADownloadCountKey(companyId, fileName);
        var currentCount = await GetIFADownloadCount(companyId, fileName);
        var newCount = (currentCount + 1).ToString();

        using var stream = new MemoryStream(Encoding.UTF8.GetBytes(newCount));
        await _s3Utils.Put(countKey, "text/plain", stream);
    }

    private async Task<int> GetIFADownloadCount(Ulid companyId, string fileName)
    {
        var countKey = GetIFADownloadCountKey(companyId, fileName);
        using var stream = await _s3Utils.Get(countKey);

        if (stream == null)
        {
            return 0;
        }

        using var reader = new StreamReader(stream);
        var countString = await reader.ReadToEndAsync();

        return int.TryParse(countString, out int count) ? count : 0;
    }

    public async Task<IEnumerable<PdfFileResponse>> ListPDFs(Ulid companyId)
    {
        var prefix = $"{_pdfRootPath}/companies/{companyId}/{_documentsPath}/";
        var fileResponses = new List<PdfFileResponse>();
        await foreach (var key in _s3Utils.List(prefix))
        {
            var fileName = Path.GetFileName(key);
            string downloadUrl;
            downloadUrl =
                await _s3Utils.CreateDownloadUrlIfExists(key) ?? string.Empty;

            if (!string.IsNullOrEmpty(downloadUrl))
            {
                var downloadCount = await GetIFADownloadCount(
                    companyId,
                    fileName
                );
                fileResponses.Add(
                    new PdfFileResponse(fileName, downloadUrl, downloadCount)
                );
            }
        }

        return fileResponses;
    }

    public async Task<UploadData> UploadPDF(
        Stream pdfStream,
        string fileName,
        Ulid companyId,
        PDFFileTypes contentType
    )
    {
        var objectKey = GetDocumentKey(companyId, fileName);
        var contentTypeString = _pdfFileTypeToContentTypeMap[PDFFileTypes.Pdf];

        await _s3Utils.Put(objectKey, contentTypeString, pdfStream);
        var downloadUrl = await _s3Utils.CreateDownloadUrlIfExists(objectKey);
        if (downloadUrl == null)
        {
            throw new Exception("Failed to generate download URL.");
        }

        var ifaDownloadCountKey = GetIFADownloadCountKey(companyId, fileName);
        using var stream = new MemoryStream(Encoding.UTF8.GetBytes("0"));

        await _s3Utils.Put(ifaDownloadCountKey, "text/plain", stream);
        return new UploadData(downloadUrl, companyId, contentType);
    }

    public async Task DeletePDF(Ulid companyId, string fileName)
    {
        var documentKey = GetDocumentKey(companyId, fileName);
        var downloadCountKey = GetIFADownloadCountKey(companyId, fileName);
        await Task.WhenAll(
            _s3Utils.Delete(documentKey),
            _s3Utils.Delete(downloadCountKey)
        );
    }
}
