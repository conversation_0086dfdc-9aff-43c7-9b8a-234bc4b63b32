using Adnavi.Domain.Common;
using Adnavi.Domain.DTOs.Common.PhoneNumbers;
using Adnavi.Domain.Models;
using Adnavi.Domain.Models.Commons.PhoneNumbers;
using Adnavi.Utils.Exceptions;
using Microsoft.Extensions.Options;
using Microsoft.EntityFrameworkCore;
using NUlid;
using Twilio;
using Twilio.Rest.Verify.V2.Service;

namespace Adnavi.Domain.Logics.Common.PhoneNumbers;

public class PhoneNumberService
{
    private readonly string _accountSid;
    private readonly string _authToken;
    private readonly string _verifyServiceSid;
    private readonly ModelUtils _model;
    private readonly AdnaviDomainContext _context;

    public PhoneNumberService(
        IOptions<TwilioSettings> settings,
        ModelUtils model,
        AdnaviDomainContext context
    )
    {
        _model = model;
        _context = context;
        _accountSid = settings.Value.AccountSid;
        _authToken = settings.Value.AuthToken;
        _verifyServiceSid = settings.Value.VerifyServiceSid;
        TwilioClient.Init(_accountSid, _authToken);
    }

    public async Task<Ulid> SendOtpAsync(
        string phoneNumber,
        string channel,
        VerificationSourceTypes? verificationSourceType
    )
    {
        var internationalPhoneNumber = ConvertToInternationalPhoneNumber(
            phoneNumber
        );

        int recentVerifications = 0;
        if (
            verificationSourceType != null
            && phoneNumber != "***********"
            && phoneNumber != "***********"
        )
        {
            recentVerifications =
                await _context.PhoneNumberVerifications.CountAsync(
                    v =>
                        v.PhoneNumber == phoneNumber
                        && v.IsVerified == true
                        && v.VerificationSourceType == verificationSourceType
                        && v.CreatedTime >= DateTime.UtcNow.AddMonths(-1)
                );
        }

        if (recentVerifications >= 4)
        {
            throw new PhoneNumberVerificationExceededError(
                phoneNumber,
                verificationSourceType
            );
        }
        else
        {
            var verification = await VerificationResource.CreateAsync(
                to: internationalPhoneNumber,
                channel: channel,
                pathServiceSid: _verifyServiceSid
            );

            if (verification.Status != "pending")
                throw new InternalException(
                    $"Failed to send OTP to {phoneNumber} by {channel}"
                );

            var phoneNumberVerification = _model.Create(
                new PhoneNumberVerification(phoneNumber)
            );
            phoneNumberVerification.VerificationChannel = channel;
            phoneNumberVerification.VerificationSourceType =
                verificationSourceType;
            await _context.SaveChangesAsync();
            return new Ulid();
        }
    }

    public PhoneNumberVerificationResponse VerifyOtpAsync(
        Ulid id,
        string code
    )
    {
        return new PhoneNumberVerificationResponse
        {
            Id = Ulid.NewUlid(),
            VerificationCode = "0000"
        };
    }

    public async Task<bool> CheckVerification(Ulid phoneVerificationId)
    {
        var phoneNumberVerification = await _model.FindAsync(
            _context.PhoneNumberVerifications,
            phoneVerificationId
        );
        if (phoneNumberVerification == null)
            return false;
        return true;
    }

    private static string ConvertToInternationalPhoneNumber(string phoneNumber)
    {
        return $"+81{phoneNumber[1..]}";
    }
}