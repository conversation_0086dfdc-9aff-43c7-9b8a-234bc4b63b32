using System.Text.Json;
using Adnavi.Domain.DTOs.Profiles.AdviserProfiles;
using Adnavi.Domain.Models;
using Adnavi.Domain.Models.Profiles.AdviserProfiles;
using Adnavi.Utils.Exceptions;
using Microsoft.EntityFrameworkCore;
using NUlid;

namespace Adnavi.Domain.Logics.Profiles.AdviserProfiles;

/// <summary>
/// Microsoftカレンダー認可・トークン管理ロジック
/// </summary>
public class AdviserMicrosoftCalendarLogic
{
    private readonly TokenProtectLogic _protector;
    private readonly ModelUtils _model;
    private readonly AdnaviDomainContext _context;
    private readonly HttpClient _httpClient;

    public AdviserMicrosoftCalendarLogic(
        TokenProtectLogic tokenProtectLogic,
        ModelUtils model,
        AdnaviDomainContext context,
        HttpClient httpClient
    )
    {
        _model = model;
        _protector = tokenProtectLogic;
        _context = context;
        _httpClient = httpClient;
    }

    // Microsoft認可URL生成
    public string GetMicrosoftAuthUrl(
        string clientId,
        string redirectUri,
        string[] scopes
    )
    {
        var scope = string.Join(" ", scopes);
        var authUrl =
            $"{CalendarConstants.MicrosoftAuthUrl}"
            + $"?client_id={clientId}"
            + $"&redirect_uri={Uri.EscapeDataString(redirectUri)}"
            + $"&response_type={CalendarConstants.MicrosoftResponseType}"
            + $"&scope={Uri.EscapeDataString(scope)}"
            + $"&response_mode={CalendarConstants.MicrosoftResponseMode}"
            + $"&prompt={CalendarConstants.MicrosoftPrompt}";
        return authUrl;
    }

    // Microsoft認可コールバック処理
    public async Task HandleMicrosoftCallbackAsync(
        string code,
        Ulid adviserId,
        string clientId,
        string clientSecret,
        string redirectUri
    )
    {
        var tokenRequestBody = new Dictionary<string, string>
        {
            { "code", code },
            { "client_id", clientId },
            { "client_secret", clientSecret },
            { "redirect_uri", redirectUri },
            { "grant_type", "authorization_code" },
            { "scope", CalendarConstants.MicrosoftDefaultScope }
        };

        var tokenData = await RequestOAuth2TokenAsync(
            CalendarConstants.MicrosoftTokenUrl,
            tokenRequestBody,
            ex => new MicrosoftAuthException(ex)
        );

        if (tokenData == null || string.IsNullOrEmpty(tokenData.AccessToken))
        {
            throw new MicrosoftAuthException(
                "Failed to parse access token from Microsoft response."
            );
        }
        var accessToken = tokenData.AccessToken;
        var refreshToken = tokenData.RefreshToken ?? "";
        var expiresIn = tokenData.ExpiresIn;

        if (string.IsNullOrEmpty(refreshToken))
        {
            // This can happen if 'offline_access' scope was not requested or if consent was not granted for it.
            throw new MicrosoftAuthException(
                "Refresh token was not returned by Microsoft. Ensure 'offline_access' scope is requested and consented."
            );
        }

        await SaveAdviserMicrosoftToken(
            adviserId,
            accessToken,
            refreshToken,
            expiresIn
        );
    }

    // 有効なMicrosoftアクセストークン取得（必要に応じてリフレッシュ）
    public async Task<string?> GetValidMicrosoftAccessTokenAsync(
        Ulid adviserId,
        string clientId,
        string clientSecret
    )
    {
        var token = await _context.AdviserMicrosoftTokens
            .AsNoTracking()
            .SingleOrDefaultAsync(t => t.AdviserId == adviserId);

        if (token == null)
        {
            return null;
        }

        if (token.AccessTokenExpiry > DateTime.UtcNow.AddMinutes(2))
        {
            return _protector.Unprotect(token.AccessToken);
        }

        var refreshToken = _protector.Unprotect(token.RefreshToken);
        if (string.IsNullOrEmpty(refreshToken))
        {
            // Cannot refresh without a refresh token.
            // This might indicate an issue with initial token acquisition or storage.
            throw new MicrosoftAuthException(
                "Microsoft refresh token is missing. Cannot refresh access token."
            );
        }

        var refreshTokenRequestBody = new Dictionary<string, string>
        {
            { "client_id", clientId },
            {
                "scope",
                "https://graph.microsoft.com/Calendars.ReadWrite offline_access"
            }, // Scopes might be required by some IdPs
            { "refresh_token", refreshToken },
            { "grant_type", "refresh_token" },
            { "client_secret", clientSecret }
        };

        var refreshTokenResponse = await _httpClient.PostAsync(
            CalendarConstants.MicrosoftTokenUrl,
            new FormUrlEncodedContent(refreshTokenRequestBody)
        );
        var refreshTokenResponseBody =
            await refreshTokenResponse.Content.ReadAsStringAsync();

        if (!refreshTokenResponse.IsSuccessStatusCode)
        {
            _context.AdviserMicrosoftTokens.Remove(token);
            await _context.SaveChangesAsync();
            throw new MicrosoftAuthException(
                $"Failed to refresh access token from Microsoft. Status: {refreshTokenResponse.StatusCode}, Response: {refreshTokenResponseBody}"
            );
        }

        var refreshTokenData = JsonSerializer.Deserialize<OAuth2TokenResponse>(
            refreshTokenResponseBody
        );

        if (
            refreshTokenData == null
            || string.IsNullOrEmpty(refreshTokenData.AccessToken)
        )
        {
            throw new MicrosoftAuthException(
                $"Failed to parse refreshed access token from Microsoft response. Response: {refreshTokenResponseBody}"
            );
        }
        var newAccessToken = refreshTokenData.AccessToken;
        var newRefreshToken = refreshTokenData.RefreshToken ?? refreshToken;
        var newExpiresIn = refreshTokenData.ExpiresIn;

        await SaveAdviserMicrosoftToken(
            adviserId,
            newAccessToken,
            newRefreshToken,
            newExpiresIn
        );
        return newAccessToken;
    }

    // Microsoftカレンダー連携解除
    public async Task DisconnectOutlookCalendarAsync(Ulid adviserId)
    {
        var token = await _context.AdviserMicrosoftTokens.SingleOrDefaultAsync(
            t => t.AdviserId == adviserId
        );

        if (token != null)
        {
            _context.AdviserMicrosoftTokens.Remove(token);
            await _context.SaveChangesAsync();
        }
    }

    // Microsoftカレンダー連携済みか判定
    public async Task<bool> IsMicrosoftCalendarConnectedAsync(Ulid adviserId)
    {
        return await _context.AdviserMicrosoftTokens
            .AsNoTracking()
            .AnyAsync(t => t.AdviserId == adviserId);
    }

    // 内部: OAuth2トークン取得
    private async Task<OAuth2TokenResponse?> RequestOAuth2TokenAsync(
        string tokenUrl,
        Dictionary<string, string> requestBody,
        Func<string, Exception> exceptionFactory
    )
    {
        var response = await _httpClient.PostAsync(
            tokenUrl,
            new FormUrlEncodedContent(requestBody)
        );
        var responseBody = await response.Content.ReadAsStringAsync();
        if (!response.IsSuccessStatusCode)
        {
            throw exceptionFactory(
                $"Failed to get access token. Response: {responseBody}"
            );
        }
        var tokenData = JsonSerializer.Deserialize<OAuth2TokenResponse>(
            responseBody
        );
        return tokenData;
    }

    // 内部: AdviserMicrosoftToken保存
    private async Task SaveAdviserMicrosoftToken(
        Ulid adviserId,
        string accessToken,
        string refreshToken,
        int expiresIn
    )
    {
        var expiryDate = DateTime.UtcNow.AddSeconds(expiresIn);

        var existing =
            await _context.AdviserMicrosoftTokens.SingleOrDefaultAsync(
                t => t.AdviserId == adviserId
            );

        if (existing != null)
        {
            existing.AccessToken = _protector.Protect(accessToken);
            existing.RefreshToken = _protector.Protect(refreshToken);
            existing.AccessTokenExpiry = expiryDate;
            _model.Update(existing);
        }
        else
        {
            _model.Create(
                new AdviserMicrosoftToken
                {
                    AdviserId = adviserId,
                    AccessToken = _protector.Protect(accessToken),
                    RefreshToken = _protector.Protect(refreshToken),
                    AccessTokenExpiry = expiryDate
                }
            );
        }
        await _context.SaveChangesAsync();
    }

    // Microsoftカレンダーの予定（busy slot）を取得
    public async Task<
        List<(DateTimeOffset Start, DateTimeOffset End)>
    > GetMicrosoftBusySlots(
        string accessToken,
        DateTimeOffset startDateTime,
        DateTimeOffset endDateTime
    )
    {
        var scopes = new[] { "https://graph.microsoft.com/.default" };
        var graphClient = new Microsoft.Graph.GraphServiceClient(
            new AccessTokenCredential(accessToken),
            scopes
        );

        var events = await graphClient.Me.CalendarView.GetAsync(requestConfig =>
        {
            // JST（Asia/Tokyo）で送信
            var jstOffset = TimeSpan.FromHours(9);
            requestConfig.QueryParameters.StartDateTime = startDateTime
                .ToOffset(jstOffset)
                .ToString("o");
            requestConfig.QueryParameters.EndDateTime = endDateTime
                .ToOffset(jstOffset)
                .ToString("o");
            requestConfig.Headers.Add(
                "Prefer",
                "outlook.timezone=\"Asia/Tokyo\""
            );
        });

        var busySlots = new List<(DateTimeOffset Start, DateTimeOffset End)>();
        if (events?.Value != null)
        {
            foreach (var e in events.Value)
            {
                var startStr = e.Start?.DateTime;
                var endStr = e.End?.DateTime;
                // 終日予定（IsAllDay==true）の場合、その日の0:00〜endの日付の0:00をbusyとする
                if (
                    e.IsAllDay == true
                    && !string.IsNullOrEmpty(startStr)
                    && !string.IsNullOrEmpty(endStr)
                )
                {
                    if (
                        DateTime.TryParse(startStr, out var allDayStartDate)
                        && DateTime.TryParse(endStr, out var allDayEndDate)
                    )
                    {
                        var allDayStart = new DateTimeOffset(
                            allDayStartDate.Date,
                            TimeSpan.Zero
                        );
                        var allDayEnd = new DateTimeOffset(
                            allDayEndDate.Date,
                            TimeSpan.Zero
                        );
                        busySlots.Add((allDayStart, allDayEnd));
                    }
                }
                // 通常の予定
                else if (
                    !string.IsNullOrEmpty(startStr)
                    && !string.IsNullOrEmpty(endStr)
                )
                {
                    if (
                        DateTimeOffset.TryParse(startStr, out var start)
                        && DateTimeOffset.TryParse(endStr, out var end)
                    )
                    {
                        busySlots.Add((start, end));
                    }
                }
            }
        }
        return busySlots.OrderBy(b => b.Start).ToList();
    }
}
