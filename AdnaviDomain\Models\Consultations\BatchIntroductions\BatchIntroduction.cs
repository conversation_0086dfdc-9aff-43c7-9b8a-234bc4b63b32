﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Web;
using Adnavi.Domain.Logics.Consultations.BatchIntroductions;
using Adnavi.Domain.Models.Commons;
using Adnavi.Domain.Models.Consultations.ConsultationRequests;
using Adnavi.Domain.Models.Profiles.AdviserProfiles;
using Adnavi.Domain.Models.Profiles.InvestorProfiles;
using Adnavi.Domain.Models.Profiles.PersonalityTraits;
using Adnavi.Utils.Models;
using Microsoft.EntityFrameworkCore;
using NUlid;

namespace Adnavi.Domain.Models.Consultations.BatchIntroductions
{
    /// <summary>
    /// BatchIntroduction
    /// /// </summary>
    /// <seealso cref="BatchIntroductionData" />
    [Index(nameof(EMail), IsUnique = false)]
    [Index(nameof(EMail), nameof(CreatedTime), IsUnique = true)]
    [Index(nameof(Name), IsUnique = false)]
    [Index(nameof(SequenceNumber), IsUnique = true)]
    [Index(nameof(RemoteHost), IsUnique = false)]
    public class BatchIntroduction
        : IHasId,
            IIntroduction,
            IInvestor,
            ICanValidateInvestor,
            ISingleIntroductionPredicator
    {
        [Required]
        public Ulid Id { get; set; }

        public int? SequenceNumber { get; set; }

        public Ulid? ParentIntroductionId { get; set; }
        public string? AdnParentId { get; set; }

        [Required]
        public BatchIntroductionStatus Status { get; set; }

        public int Age { get; set; }
        public AssetRanges AssetRange { get; set; }
        public ConsultationRequestTypes ConsultationRequestType { get; set; }

        [Required]
        public string Name { get; set; }
        public string Furigana { get; set; }
        public GenderTypes? Gender { get; set; }
        public string TelephoneNumber { get; set; }

        [Required]
        public string EMail { get; set; }
        public Prefectures Prefecture { get; set; }
        public string PostalCode { get; set; }
        public string Address { get; set; }
        public virtual ICollection<AdviserWorkType> AdviserWorkTypes { get; set; }
        public InvestmentPurposes? InvestmentPurpose { get; set; }
        public virtual ICollection<ContactMethod> ContactMethods { get; set; }

        /// <summary>
        /// 投資家が入力した資産運用アドバイザーへの要望
        //  AdviserConsultationRequest.Contentsが代入される
        /// </summary>
        public string Remarks { get; set; }

        /// <summary>
        /// カスタマーサポートがアドバイザーへ送信するメールに含める,投資家には見られない
        /// AdviserConsultationRequest.StaffRemarksが代入されている
        /// </summary>
        public string? InvestorRemarks { get; set; }
        public AnnualIncomes? AnnualIncome { get; set; }
        public InvestorOccupationTypes InvestorOccupationType { get; set; }
        public virtual ICollection<ConsultationType> ConsultationTypes { get; set; }
        public virtual ICollection<AdviserPreference> AdviserPreferences { get; set; }

        //    = new List<AdviserPreference>();
        public bool AcceptPrivacy { get; set; }
        public bool AcceptMailMagazine { get; set; }

        public virtual ICollection<Adviser> Advisers { get; set; }

        public virtual PersonalitySurvey? PersonalitySurvey { get; set; }

        [Required]
        public string RemoteHost { get; set; }

        [Required]
        public string OriginUrl { get; set; }

        [Required]
        public string OriginName { get; set; }

        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public DateTime CreatedTime { get; set; }

        public FirstContactMethodTypes? FirstContactMethodType { get; set; }
        public bool SendRemindMail { get; set; }
        public bool? SendScheduleAdjustmentRemind24HoursAgo { get; set; }
        public bool? SendScheduleAdjustmentRemind1HourAgo { get; set; }
        public bool StaffContacted { get; set; }
        public string? RegisterEmail { get; set; }
        public AdviserSortOptionForBatchIntroduction SortOption { get; set; }
        public virtual ICollection<FavorableAdviserExperience> FavorableAdviserExperiences { get; set; }
        public string? FreeInputForFavorableAdvisers { get; set; }
        public bool AsSecondOpinion { get; set; }

        /// <summary> スタッフによるメモ的役割 </summary>
        public string? RemarksFromStaff { get; set; }
        public string? ConsultationContents { get; set; }
        public string? AssetStatusText { get; set; }
        public string? CustomerAttributeText { get; set; }
        public virtual EvaluationRequest? EvaluationRequest { get; set; }
        public DateOnly? RegisteredInvestorIntroduceDate { get; set; }
        public string? AdditionalInformation { get; set; }

        public virtual ICollection<AdviserBatchIntroductionPoint> AdviserBatchIntroductionPoints { get; set; }

        public bool? IsCalendarIntegrated { get; set; }
        public DateTimeOffset? ConsultationStartTime { get; set; }
        public DateTimeOffset? ConsultationEndTime { get; set; }

        public BatchIntroduction() { }

        [NotMapped]
        public bool IsStaffCreated => !string.IsNullOrEmpty(RegisterEmail);

        /// <summary>
        /// アドバイザーに単独紹介を行った時に true を返す。
        /// </summary>
        /// <remarks>
        /// 単独紹介の場合は支払額が異なるため、アドバイザーに単独紹介を行ったかどうかを判定する。
        /// 現在は、CS紹介のみが単独紹介となる。
        /// </remarks>
        /// <returns></returns>
        [NotMapped]
        public bool IsSingleIntroduction => IsStaffCreated;

        public void DeleteInvestorPrivacyInformation()
        {
            Name = "";
            Furigana = "";
            TelephoneNumber = "";
            EMail = "";
            PostalCode = "";
            Address = "";
        }

        public bool IsTest()
        {
            Uri.TryCreate(OriginUrl, UriKind.Absolute, out var uri);
            var urlQuery = HttpUtility.ParseQueryString(uri?.Query ?? "");

            if (Status == BatchIntroductionStatus.Introduction)
            {
                return EMail.EndsWith("@adviser-navi.co.jp")
                    || EMail.EndsWith("@groups.adviser-navi.co.jp")
                    || Name.Contains("テスト")
                    || Name.Contains("てすと")
                    || Name.Contains("test")
                    || urlQuery.Get("utm_source") == "test";
            }
            else if (Status == BatchIntroductionStatus.Request)
            {
                return urlQuery.Get("utm_source") == "test"
                    && urlQuery.Get("utm_medium") == "test"
                    && urlQuery.Get("utm_campaign") == "test"
                    && Status == BatchIntroductionStatus.Request;
            }

            return false;
        }

        public static IQueryable<BatchIntroduction> QueryNotTest(
            IQueryable<BatchIntroduction> query
        )
        {
            return query.Where(
                b =>
                    (
                        b.Status == BatchIntroductionStatus.Introduction
                        && !b.EMail.EndsWith("@adviser-navi.co.jp")
                        && !b.EMail.EndsWith("@groups.adviser-navi.co.jp")
                        && !b.Name.Contains("テスト")
                        && !b.Name.Contains("てすと")
                        && !b.Name.Contains("test")
                    )
                    || (
                        b.Status == BatchIntroductionStatus.Request
                        && !(
                            b.OriginUrl.Contains("utm_source=test")
                            && b.OriginUrl.Contains("utm_medium=test")
                            && b.OriginUrl.Contains("utm_campaign=test")
                        )
                    )
            );
        }

        public string GetOriginLpUrl() =>
            OriginName switch
            {
                "一括紹介(投資信託)"
                    => "https://adviser-navi.co.jp/watashi-ifa/hikaku/03/",

                "一括紹介(退職金)" or "一括紹介(退職金ナビ)" => "https://taisyokukin-navi.com/",

                //"一括紹介(資産)" or "一括紹介(資産運用ナビ)" or
                _ => "https://adviser-navi.co.jp/watashi-ifa/hikaku/10/"
            };

        public static void SetupModel(ModelBuilder modelBuilder)
        {
            modelBuilder
                .Entity<BatchIntroduction>()
                .HasMany(e => e.Advisers)
                .WithMany(c => c.BatchIntroductions)
                .UsingEntity(j => j.ToTable("BatchIntroductionsAdvisers"));

            modelBuilder
                .Entity<BatchIntroduction>()
                .HasMany(e => e.AdviserWorkTypes)
                .WithMany(c => c.BatchIntroductions)
                .UsingEntity(
                    j => j.ToTable("BatchIntroductionsAdviserWorkTypes")
                );

            modelBuilder
                .Entity<BatchIntroduction>()
                .HasMany(e => e.ContactMethods)
                .WithMany(c => c.BatchIntroductions)
                .UsingEntity(
                    j => j.ToTable("BatchIntroductionsContactMethods")
                );

            modelBuilder
                .Entity<BatchIntroduction>()
                .HasMany(e => e.ConsultationTypes)
                .WithMany(c => c.BatchIntroductions)
                .UsingEntity(
                    j => j.ToTable("BatchIntroductionsConsultationTypes")
                );
        }
    }
}
