using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Adnavi.Domain.Logics.Consultations.BatchIntroductions;
using Adnavi.Domain.Models.Commons;
using Adnavi.Domain.Models.Consultations.BatchIntroductions;
using Adnavi.Domain.Models.Consultations.ConsultationRequests;
using Adnavi.Domain.Models.Profiles.AdviserProfiles;
using Adnavi.Domain.Models.Profiles.IfaCompanyProfiles;
using Adnavi.Domain.Models.Profiles.InvestorProfiles;
using Adnavi.Utils.Models;
using NUlid;

namespace Adnavi.Domain.Models.Consultations.BatchIntroductionsCompany
{
    /// <summary>
    /// BatchIntroduction
    /// /// </summary>
    /// <seealso cref="BatchIntroductionData" />
    // [Index(nameof(EMail), IsUnique = false)]
    // [Index(nameof(EMail), nameof(CreatedTime), IsUnique = true)]
    // [Index(nameof(Name), IsUnique = false)]
    // [Index(nameof(SequenceNumber), IsUnique = true)]
    public class BatchIntroductionCompany
        : IHasId,
            IIntroduction,
            IInvestor,
            ICanValidateInvestor
    {
        [Required]
        public Ulid Id { get; set; }

        public Ulid? ParentIntroductionId { get; set; }
        public string? AdnParentId { get; set; }

        [Required]
        public BatchIntroductionStatus Status { get; set; }

        public int Age { get; set; }
        public AssetRanges AssetRange { get; set; }
        public ConsultationRequestTypes ConsultationRequestType { get; set; }

        [Required]
        public string Name { get; set; }
        public string Furigana { get; set; }
        public GenderTypes? Gender { get; set; }
        public string TelephoneNumber { get; set; }

        [Required]
        public string EMail { get; set; }
        public Prefectures Prefecture { get; set; }
        public string PostalCode { get; set; }
        public string Address { get; set; }
        public virtual ICollection<AdviserWorkType> AdviserWorkTypes { get; set; }
        public InvestmentPurposes? InvestmentPurpose { get; set; }
        public virtual ICollection<ContactMethod> ContactMethods { get; set; }
        public string Remarks { get; set; }
        public string? InvestorRemarks { get; set; }
        public AnnualIncomes? AnnualIncome { get; set; }
        public InvestorOccupationTypes InvestorOccupationType { get; set; }
        public virtual ICollection<ConsultationType> ConsultationTypes { get; set; }
        public virtual ICollection<AdviserPreference> AdviserPreferences { get; set; }

        //    = new List<AdviserPreference>();
        public bool AcceptPrivacy { get; set; }
        public bool AcceptMailMagazine { get; set; }

        public virtual ICollection<IfaCompany> IfaCompanies { get; set; }

        [Required]
        public string RemoteHost { get; set; }

        [Required]
        public string OriginUrl { get; set; }

        [Required]
        public string OriginName { get; set; }

        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public DateTime CreatedTime { get; set; }

        public FirstContactMethodTypes? FirstContactMethodType { get; set; }
        public bool SendRemindMail { get; set; }
        public bool StaffContacted { get; set; }
        public string? RegisterEmail { get; set; }
        public AdviserSortOptionForBatchIntroduction SortOption { get; set; }
        public virtual ICollection<FavorableAdviserExperience> FavorableAdviserExperiences { get; set; }
        public string? FreeInputForFavorableAdvisers { get; set; }
        public bool AsSecondOpinion { get; set; }
        public string? RemarksFromStaff { get; set; }
        public string? ConsultationContents { get; set; }
        public string? AssetStatusText { get; set; }
        public string? CustomerAttributeText { get; set; }
        public virtual EvaluationRequest? EvaluationRequest { get; set; }
        public DateOnly? RegisteredInvestorIntroduceDate { get; set; }
        public string? AdditionalInformation { get; set; }

        [NotMapped]
        public bool IsStaffCreated => !string.IsNullOrEmpty(RegisterEmail);
    }
}
