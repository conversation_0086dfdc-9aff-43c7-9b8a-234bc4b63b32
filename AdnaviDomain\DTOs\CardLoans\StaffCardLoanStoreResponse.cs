using Adnavi.Domain.Models.CardLoans;
using Adnavi.Utils.Models;
using Microsoft.EntityFrameworkCore;
using NUlid;

namespace Adnavi.Domain.DTOs.CardLoans;

public class StaffCardLoanStoreResponse : IDataTransferObject
{
    public Ulid Id { get; set; }
    public string CardLoanName { get; set; }
    public string Name { get; set; }
    public string Prefecture { get; set; }
    public string Address { get; set; }

    public static async IAsyncEnumerable<StaffCardLoanStoreResponse> FromQuery(
        IQueryable<CardLoanStore> query
    )
    {
        var data = await query
            .AsNoTracking()
            .Select(
                s =>
                    new StaffCardLoanStoreResponse
                    {
                        Id = s.Id,
                        CardLoanName = s.CardLoan.Name,
                        Name = s.Name,
                        Prefecture = s.Prefecture.Name,
                        Address = s.Address,
                    }
            )
            .ToArrayAsync();

        foreach (var d in data)
        {
            yield return d;
        }
    }
}
