using Adnavi.Domain.Logics.Consultations.BatchIntroductions;

namespace Adnavi.Domain.Logics.Consultations.MinimumRequirements;

public class After : IRequirement
{
    private readonly int _after;

    public After(int after)
    {
        _after = after;
    }

    public bool CheckAdviser(AdviserRequirementsCheck data)
    {
        if (data.ClosingHour <= _after)
            return false;
        return true;
    }
}
