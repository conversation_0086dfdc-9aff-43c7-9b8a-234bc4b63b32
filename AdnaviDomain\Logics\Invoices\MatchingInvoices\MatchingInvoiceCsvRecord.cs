﻿using Adnavi.Domain.Models.Invoices.MatchingInvoices;
using Adnavi.Utils;
using Adnavi.Utils.Models;
using CsvHelper.Configuration.Attributes;
using Microsoft.EntityFrameworkCore;

namespace Adnavi.Domain.Logics.Invoices.MatchingInvoices;

public sealed class MatchingInvoiceCsvRecord
{
    [Name("送客送信日時")]
    public string CreatedTime { get; set; }

    [Name("管理ID")]
    public string Id { get; set; }

    [Name("一括紹介ID")]
    public string BatchIntroductionId { get; set; }

    [Name("会社名")]
    public string CompanyName { get; set; }

    [Name("担当IFA")]
    public string AdviserName { get; set; }

    [Name("紹介者名")]
    public string BatchName { get; set; }

    [Name("金融資産")]
    public string AssetRange { get; set; }

    [Name("面談日")]
    public string? InterviewDate { get; set; }

    [Name("進捗状況")]
    public string? ProgressStatus { get; set; }

    [Name("送客クオリティ")]
    public string? Quality { get; set; }

    [Name("詳細")]
    public string? Details { get; set; }

    [Name("変更日")]
    public string? ModifiedDate { get; set; }

    [Name("請求ID")]
    public string InvoiceId { get; set; }

    [Name("請求種別")]
    public string InvoiceType { get; set; }

    [Name("請求月")]
    public string InvoiceMonth { get; set; }

    [Name("請求状況")]
    public string InvoiceStatus { get; set; }

    [Name("請求金額")]
    public long? InvoiceAmount { get; set; }

    [Name("請求備考")]
    public string? Remarks { get; set; }

    [Name("請求変更日")]
    public string? InvoiceModifiedDate { get; set; }

    [Name("適用キャンペーン")]
    public string? PromotionId { get; set; }

    private MatchingInvoiceCsvRecord() { }

    public static async IAsyncEnumerable<MatchingInvoiceCsvRecord> FromQuery(
        IQueryable<MatchingInvoiceManagement> query
    )
    {
        var timeZone = DateUtils.GetJapanTimeZoneInfo();
        var query2 = query.Select(
            a =>
                new
                {
                    Record = new MatchingInvoiceCsvRecord
                    {
                        CreatedTime = TimeZoneInfo
                            .ConvertTimeFromUtc(
                                a.CreatedTime ?? DateTime.MinValue,
                                timeZone
                            )
                            .ToString("yyyy/MM/dd HH:mm:ss"),
                        Id = a.Id.ToString(),
                        BatchIntroductionId = a.BatchIntroduction.Id.ToString(),
                        CompanyName =
                            a.IfaCompany != null ? a.IfaCompany.Name : "",
                        AdviserName = a.Adviser.FullName,
                        BatchName = a.BatchIntroduction.Name,
                        AssetRange = a.BatchIntroduction.AssetRange.ToString(),
                        InterviewDate =
                            a.InterviewDate == null
                                ? ""
                                : TimeZoneInfo
                                    .ConvertTimeFromUtc(
                                        a.InterviewDate.Value,
                                        timeZone
                                    )
                                    .ToString("yyyy/MM/dd"),
                        ProgressStatus = a.ProgressStatus.ToString(),
                        Quality =
                            a.Quality == null ? "Hold" : a.Quality.ToString(),
                        Details = a.Details,
                        ModifiedDate =
                            a.ModifiedTime == null
                                ? ""
                                : TimeZoneInfo
                                    .ConvertTimeFromUtc(
                                        a.ModifiedTime.Value,
                                        timeZone
                                    )
                                    .ToString("yyyy/MM/dd HH:mm:ss")
                    },
                    Invoices = a.Invoices
                }
        );

        foreach (var result in await query2.ToArrayAsync())
        {
            foreach (var invoice in result.Invoices)
            {
                yield return new MatchingInvoiceCsvRecord
                {
                    CreatedTime = result.Record.CreatedTime,
                    Id = result.Record.Id,
                    BatchIntroductionId = result.Record.BatchIntroductionId,
                    CompanyName = result.Record.CompanyName,
                    AdviserName = result.Record.AdviserName,
                    BatchName = result.Record.BatchName,
                    AssetRange = result.Record.AssetRange,
                    InterviewDate = result.Record.InterviewDate,
                    ProgressStatus = result.Record.ProgressStatus,
                    Quality = result.Record.Quality,
                    Details = result.Record.Details,
                    ModifiedDate = result.Record.ModifiedDate,
                    InvoiceId = invoice.Id.ToString(),
                    InvoiceType = invoice.InvoiceType.ToString(),
                    InvoiceMonth = invoice.InvoiceMonth.ToString(),
                    InvoiceStatus = invoice.InvoiceStatus.ToString(),
                    InvoiceAmount = invoice.InvoiceAmount,
                    Remarks = invoice.Remarks,
                    InvoiceModifiedDate =
                        invoice.ModifiedTime == null
                            ? ""
                            : TimeZoneInfo
                                .ConvertTimeFromUtc(
                                    invoice.ModifiedTime.Value,
                                    timeZone
                                )
                                .ToString("yyyy/MM/dd HH:mm:ss"),
                    PromotionId = invoice.PromotionId
                };
            }
        }
    }
}
