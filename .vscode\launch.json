{
  // IntelliSense を使用して利用可能な属性を学べます。
  // 既存の属性の説明をホバーして表示します。
  // 詳細情報は次を確認してください: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "CoreOneWebApi",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build",
      "program": "${workspaceFolder}/CoreOneWebApi/bin/Debug/net6.0/CoreOneWebApi.dll",
      "args": [],
      "cwd": "${workspaceFolder}/CoreOneWebApi",
      "stopAtEntry": false,
      "serverReadyAction": {
        "action": "openExternally",
        "pattern": "\\bNow listening on:\\s+http://[\\w\\.-]+:(\\d+)",
        "uriFormat": "http://localhost:%s/swagger/"
      },
      "env": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      },
      "sourceFileMap": {
        "/Views": "${workspaceFolder}/Views"
      }
    },
    {
      "name": "DataImporter",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build",
      "program": "${workspaceFolder}/DataImporter/bin/Debug/net6.0/DataImporter.dll",
      "args": [],
      "cwd": "${workspaceFolder}/DataImporter",
      "stopAtEntry": false,
      "console": "integratedTerminal"
    },
    {
      "name": ".NET Core Attach",
      "type": "coreclr",
      "request": "attach"
    }
  ]
}
