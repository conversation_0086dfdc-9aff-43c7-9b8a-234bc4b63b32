﻿using System.ComponentModel.DataAnnotations;
using Adnavi.Utils;
using Adnavi.Utils.Models;
using Microsoft.EntityFrameworkCore;
using NUlid;

namespace Adnavi.Domain.Models.Invoices.RecruitInvoices;

[Index(nameof(EmploymentId), nameof(YearNumber), IsUnique = true)]
public class EmployeeFee : IHasId
{
    /// <summary>ID</summary>
    [Required]
    public Ulid Id { get; set; }

    /// <summary>バージョン</summary>
    [Timestamp]
    public byte[]? RowVersion { get; set; }

    /// <summary>雇用ID</summary>
    [Required]
    public Ulid EmploymentId { get; set; }

    /// <summary>雇用</summary>
    public virtual Employment Employment { get; set; }

    /// <summary>年数</summary>
    [Required]
    public uint YearNumber { get; set; }

    /// <summary>開始年月</summary>
    [Required]
    public YearAndMonth StartYearMonth { get; set; }

    /// <summary>終了年月</summary>
    [Required]
    public YearAndMonth EndYearMonth { get; set; }

    /// <summary>料率(%)</summary>
    [Required]
    public byte RatePercent { get; set; }
}
