using Adnavi.Domain.Models.Accounts;
using Adnavi.Utils.Models;
using NUlid;

namespace Adnavi.Domain.DTOs.Accounts;

/// <summary>
/// User DTO
/// </summary>
/// <seealso cref="User" />
public sealed class UserSummaryData : IDataTransferObject
{
    public Ulid Id { get; set; }

    public string? MainEMail { get; set; }

    public Ulid? AdviserId { get; set; }

    public string? AdviserName { get; set; }

    public Ulid? OrganizationId { get; set; }

    public string? OrganizationName { get; set; }

    public DateTime? CreatedTime { get; set; }

    public DateTime? ModifiedTime { get; set; }

    public UserSummaryData() { }

    public static IQueryable<UserSummaryData> FromQuery(IQueryable<User> query)
    {
        return query.Select(
            u =>
                new UserSummaryData
                {
                    Id = u.Id,
                    MainEMail = u.MainEMail,
                    CreatedTime = u.CreatedTime,
                    ModifiedTime = u.ModifiedTime,
                    AdviserId = u.AdviserId,
                    AdviserName =
                        u.Adviser == null
                            ? null
                            : u.Adviser.FamilyName + " " + u.Adviser.FirstName,
                    OrganizationId =
                        u.OrganizationMembership == null
                            ? null
                            : u.OrganizationMembership.OrganizationId,
                    OrganizationName =
                        u.OrganizationMembership == null
                            ? null
                            : u.OrganizationMembership.Organization.Name
                }
        );
    }
}
