﻿using Adnavi.Domain.Models.Consultations.BatchIntroductions;
using Adnavi.Domain.Models.Profiles.AdviserProfiles;
using Adnavi.Utils;
using Adnavi.Utils.Models;
using CsvHelper.Configuration.Attributes;
using Microsoft.EntityFrameworkCore;
using NUlid;

namespace Adnavi.Domain.Logics.Profiles.AdviserProfiles;

public sealed class AdviserCsvRecord
{
    [Name("ID")]
    public string Id { get; set; }

    [Name("姓")]
    public string FamilyName { get; set; }

    [Name("名")]
    public string FirstName { get; set; }

    [Name("姓(かな)")]
    public string FamilyName<PERSON>ana { get; set; }

    [Name("名(かな)")]
    public string FirstNameKana { get; set; }

    [Name("公開可能")]
    public bool Published { get; set; }

    [Name("メールアドレス")]
    public string? EMail { get; set; }

    [Name("生年月日")]
    public DateTime? Birthday { get; set; }

    [Name("IFA開始月")]
    public YearAndMonth? IfaStartMonth { get; set; }

    [Name("営業開始月")]
    public YearAndMonth? SalesStartMonth { get; set; }

    [Name("預かり資産(万円)")]
    public int? DepositedAssetsMan { get; set; }

    [Name("顧客数（世帯）")]
    public uint? NumberOfClientFamilies { get; set; }

    [Name("作成日時")]
    public DateTime CreatedTime { get; set; }

    [Name("更新日時")]
    public DateTime ModifiedTime { get; set; }

    [Name("プロファイル閲覧数")]
    public uint ProfileAccessCount { get; set; }

    [Name("同意済")]
    public bool AgreementsAccepted { get; set; }

    [Name("IFA法人名")]
    public string? IfaCompanyName { get; set; }

    [Name("一括紹介設定")]
    public string CompanyByCompanyIntroduction { get; set; }

    [Name("一括紹介有効(個人設定)")]
    public bool BatchIntroductionEnabled { get; set; }

    [Name("最低受付金融資産")]
    public long? MinimumAcceptAssets { get; set; }

    [Name("最大受付金融資産")]
    public long? MaximumAcceptAssets { get; set; }

    [Name("紹介受付金融資産")]
    public string AcceptAssets { get; set; }

    [Name("IFA法人最低受付金融資産")]
    public long? IfaCompanyMinimumAcceptAssets { get; set; }

    [Name("IFA法人最大受付金融資産")]
    public long? IfaCompanyMaximumAcceptAssets { get; set; }

    [Name("IFA法人紹介受付金融資産")]
    public string IfaCompanyAcceptAssets { get; set; }

    [Name("訪問・来店可能場所")]
    public string VisitPrefectures { get; set; }

    [Name("Web面談可能場所")]
    public string WebMeetingPrefectures { get; set; }

    [Name("アドバイザー属性")]
    public string? AdviserType { get; set; }

    [Name("自己申告の法人名")]
    public string? CompanyNameTmp { get; set; }

    [Name("対応業務")]
    public string? WorkType { get; set; }

    [Name("得意な提案領域")]
    public string? ConsultationType { get; set; }

    [Name("対応可能な曜日")]
    public string? WorkDay { get; set; }

    [Name("業務開始時間")]
    public int OpeningHour { get; set; }

    [Name("業務終了時間")]
    public int ClosingHour { get; set; }

    [Name("面談方法")]
    public string? AvailableContactMethod { get; set; }

    [Name("メインのお客様")]
    public string? MainCustomerType { get; set; }

    [Name("資格")]
    public string? License { get; set; }

    [Name("提案に対する考え方、特徴")]
    public string? Philosophy { get; set; }

    [Name("自己紹介")]
    public string? SelfIntroduction { get; set; }

    [Name("性別")]
    public string? Gender { get; set; }

    [Name("居住地")]
    public string? LivingArea { get; set; }

    [Name("趣味・ご関心ごと")]
    public string? Hobby { get; set; }

    [Name("受賞")]
    public string? Awards { get; set; }

    [Name("最終学歴")]
    public string? School { get; set; }

    [Name("出身キャリア")]
    public string? CareerType { get; set; }

    [Name("出身校")]
    public string? AcademicBackground { get; set; }

    [Name("電話番号")]
    public string? TelephoneNumber { get; set; }

    [Name("メインのキャリア")]
    public string? MainCareer { get; set; }

    [Name("検索表示数")]
    public int IntroductionCount { get; set; }

    [Name("申込数")]
    public int RequestCount { get; set; }

    private AdviserCsvRecord() { }

    public static async IAsyncEnumerable<AdviserCsvRecord> FromQuery(
        AdnaviDomainContext context,
        IQueryable<Adviser> query
    )
    {
        var query2 = query.Select(
            a =>
                new
                {
                    Record = new AdviserCsvRecord
                    {
                        Id = a.Id.ToString(),
                        FamilyName = a.FamilyName,
                        FirstName = a.FirstName,
                        FamilyNameKana = a.FamilyNameKana,
                        FirstNameKana = a.FirstNameKana,
                        Published = a.Published,
                        EMail =
                            a.User == null || a.User.MainEMail == null
                                ? ""
                                : a.User.MainEMail,
                        MinimumAcceptAssets = a.MinimumAcceptAssets,
                        MaximumAcceptAssets = a.MaximumAcceptAssets,
                        // AcceptAssets = a.BatchIntroductionSetting == null
                        IfaCompanyMinimumAcceptAssets =
                            a.IfaCompany == null
                                ? null
                                : a.IfaCompany.MinimumAcceptAssets,
                        IfaCompanyMaximumAcceptAssets =
                            a.IfaCompany == null
                                ? null
                                : a.IfaCompany.MaximumAcceptAssets,
                        // IfaCompanyAcceptAssets = a.IfaCompany == null
                        Birthday = a.Birthday,
                        IfaStartMonth = a.IfaStartMonth,
                        SalesStartMonth = a.SalesStartMonth,
                        DepositedAssetsMan = a.DepositedAssetsMan,
                        NumberOfClientFamilies = a.NumberOfClientFamilies,
                        CreatedTime = a.CreatedTime,
                        ModifiedTime = a.ModifiedTime,
                        ProfileAccessCount = a.ProfileAccessCount,
                        AgreementsAccepted = a.AgreementsAccepted,
                        // CompanyByCompanyIntroduction =
                        BatchIntroductionEnabled =
                            a.BatchIntroductionSetting == null
                                ? false
                                : a.BatchIntroductionSetting.Enabled,
                        OpeningHour = a.OpeningHour,
                        ClosingHour = a.ClosingHour,
                        IfaCompanyName =
                            a.IfaCompany == null ? null : a.IfaCompany.Name,
                        MainCareer = a.MainCareer,
                        AdviserType =
                            a.AdditionalProfileItems == null
                                ? null
                                : a.AdditionalProfileItems.AdviserType.ToString(),
                        CompanyNameTmp =
                            a.AdditionalProfileItems == null
                                ? null
                                : a.AdditionalProfileItems.CompanyName,
                        // WorkDay = a.AdditionalProfileItems == null
                        // AvailableContactMethod = a.AdditionalProfileItems == null
                        MainCustomerType =
                            a.AdditionalProfileItems == null
                                ? null
                                : a.AdditionalProfileItems.MainCustomerType.ToString(),
                        // License =a.AdditionalProfileItems == null
                        Philosophy =
                            a.AdditionalProfileItems == null
                                ? null
                                : a.AdditionalProfileItems.Philosophy,
                        SelfIntroduction =
                            a.AdditionalProfileItems == null
                                ? null
                                : a.AdditionalProfileItems.SelfIntroduction,
                        Gender =
                            a.AdditionalProfileItems == null
                                ? null
                                : a.AdditionalProfileItems.Gender.ToString(),
                        LivingArea =
                            a.AdditionalProfileItems == null
                                ? null
                                : a.AdditionalProfileItems.LivingArea.ToString(),
                        // Hobby =a.AdditionalProfileItems == null
                        // Awards =a.AdditionalProfileItems == null
                        School =
                            a.AdditionalProfileItems == null
                                ? null
                                : a.AdditionalProfileItems.School,
                        CareerType =
                            a.AdditionalProfileItems == null
                                ? null
                                : a.AdditionalProfileItems.CareerType.ToString(),
                        AcademicBackground =
                            a.AdditionalProfileItems == null
                                ? null
                                : a.AdditionalProfileItems.AcademicBackground.ToString(),
                        TelephoneNumber =
                            a.AdditionalProfileItems == null
                                ? null
                                : a.AdditionalProfileItems.TelephoneNumber,

                        // WorkType = a.GetWorkTypes().ToList(),
                        // ConsultationType = string.Join(
                        // IntroductionCount = a.BatchIntroductions
                        // RequestCount = a.BatchIntroductions
                    },
                    Id = a.Id,
                    BatchIntroductionSettingsId = a.BatchIntroductionSetting
                    == null
                        ? null
                        : a.BatchIntroductionSetting.Id as Ulid?,
                    IfaCompanyBatchIntroductionSettingsId = a.IfaCompany == null
                    || a.IfaCompany.BatchIntroductionIfaCompanySetting == null
                        ? null
                        : a.IfaCompany.BatchIntroductionIfaCompanySetting.Id
                            as Ulid?,
                    AdditionalProfileItemsId = a.AdditionalProfileItems == null
                        ? null
                        : a.AdditionalProfileItems.Id as Ulid?,
                    a.CompanyByCompanyIntroduction,
                }
        );

        foreach (var result in await query2.ToArrayAsync())
        {
            var record = result.Record;

            if (result.BatchIntroductionSettingsId != null)
            {
                record.AcceptAssets = await JoinList(
                    context.AssetRanges.Where(
                        b =>
                            b.BatchIntroductionSettings.Any(
                                c => c.Id == result.BatchIntroductionSettingsId
                            )
                    )
                );
            }

            if (result.IfaCompanyBatchIntroductionSettingsId != null)
            {
                record.IfaCompanyAcceptAssets = await JoinList(
                    context.AssetRanges.Where(
                        b =>
                            b.BatchIntroductionIfaCompanySetting.Any(
                                c =>
                                    c.Id
                                    == result.IfaCompanyBatchIntroductionSettingsId
                            )
                    )
                );
            }
            record.VisitPrefectures = await JoinList(
                context.Prefectures.Where(
                    b =>
                        b.VisitAdvisers.Any(
                            c => c.Id == result.Id && c.VisitPrefectures.Any()
                        )
                )
            );
            record.WebMeetingPrefectures = await JoinList(
                context.Prefectures.Where(
                    b =>
                        b.WebMeetingAdvisers.Any(
                            c =>
                                c.Id == result.Id
                                && c.WebMeetingPrefectures.Any()
                        )
                )
            );

            record.CompanyByCompanyIntroduction =
                GetCompanyIntroductionDescription(
                    result.CompanyByCompanyIntroduction
                );

            if (result.AdditionalProfileItemsId != null)
            {
                var pId = result.AdditionalProfileItemsId;
                record.WorkDay = await JoinList(
                    context.Days.Where(
                        b =>
                            b.AdditionalProfileItems.Any(
                                c => c.Id == result.AdditionalProfileItemsId
                            )
                    )
                );

                record.AvailableContactMethod = await JoinList(
                    context.ContactMethods.Where(
                        b =>
                            b.AdditionalProfileItems.Any(
                                c => c.Id == result.AdditionalProfileItemsId
                            )
                    )
                );

                record.License = await JoinList(
                    context.AdviserLicenses.Where(
                        b =>
                            b.AdditionalProfileItems.Any(
                                c => c.Id == result.AdditionalProfileItemsId
                            )
                    )
                );

                record.Hobby = await JoinList(
                    context.Hobbies.Where(
                        b =>
                            b.AdditionalProfileItems.Any(
                                c => c.Id == result.AdditionalProfileItemsId
                            )
                    )
                );

                record.Awards = await JoinList(
                    context.AwardTypes.Where(
                        b => b.AdditionalProfileItems.Any(c => c.Id == pId)
                    )
                );
            }

            var a = await context.Advisers
                .Include(a => a.CustomerStatics)
                .Where(a => a.Id == result.Id)
                .SingleAsync();

            record.WorkType = string.Join(";", a.GetWorkTypes());

            record.ConsultationType = string.Join(
                ";",
                a.GetConsultationTypes()
            );
            record.IntroductionCount = BatchIntroduction
                .QueryNotTest(context.BatchIntroductions)
                .Where(
                    b =>
                        b.Advisers.Any(c => c.Id == result.Id)
                        && b.Status == BatchIntroductionStatus.Introduction
                        && b.ParentIntroductionId == null
                )
                .Count();

            record.RequestCount = BatchIntroduction
                .QueryNotTest(context.BatchIntroductions)
                .Where(
                    b =>
                        b.Advisers.Any(c => c.Id == result.Id)
                        && b.Status == BatchIntroductionStatus.Request
                )
                .Count();

            yield return record;
        }
    }

    private static async Task<string> JoinList(
        IQueryable<IHasDisplayName> query
    )
    {
        return string.Join(
            ";",
            await query.Select(b => b.DisplayName).ToListAsync()
        );
    }

    private static string GetCompanyIntroductionDescription(
        bool? companyByCompanyIntroduction
    )
    {
        if (companyByCompanyIntroduction == null)
            return "";
        if (companyByCompanyIntroduction == true)
            return "企業で1人紹介";
        else
            return "企業で複数紹介";
    }
}
