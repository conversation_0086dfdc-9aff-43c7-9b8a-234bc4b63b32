﻿using System.ComponentModel.DataAnnotations;
using Adnavi.Utils.EnumTables;

namespace Adnavi.Domain.Models.Consultations.ConsultationRequests;

public class FavorableAdviserExperience
    : IEnumRecord<FavorableAdviserExperiences>
{
    /// <summary>ID</summary>
    [Required]
    public FavorableAdviserExperiences Id { get; set; }

    /// <summary>名前</summary>
    [Required]
    public string Name { get; set; }

    /// <summary>表示名</summary>
    [Required]
    public string DisplayName { get; set; }
}
