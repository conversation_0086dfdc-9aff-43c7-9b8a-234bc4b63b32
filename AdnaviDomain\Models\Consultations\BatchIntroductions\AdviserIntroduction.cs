﻿using Adnavi.Domain.Logics.Consultations.BatchIntroductions;
using Adnavi.Domain.Models.Profiles.AdviserProfiles;
using Adnavi.Domain.Models.Profiles.IfaCompanyProfiles;
using Adnavi.Utils.Models;
using NUlid;

namespace Adnavi.Domain.Models.Consultations.BatchIntroductions;

public class AdviserIntroduction : IHasId
{
    public Ulid Id { get; set; }
    public virtual Adviser Adviser { get; set; }
    public virtual IfaCompany? IfaCompany { get; set; }
    public virtual BatchIntroduction BatchIntroduction { get; set; }
    public AdviserSortOptionForBatchIntroduction SortOption { get; set; }
    public int Rank { get; set; }

    /// <summary>アドバイザーへのメールに含める。アドバイザーごとの個別のメッセージ </summary>
    public string? StaffRemarksToAdvisers { get; set; }
}
