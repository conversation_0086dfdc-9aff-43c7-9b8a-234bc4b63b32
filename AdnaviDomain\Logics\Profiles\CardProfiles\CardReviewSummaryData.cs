﻿using Adnavi.Domain.Models.Profiles.CardProfiles;
using NUlid;

namespace Adnavi.Domain.Logics.Profiles.CardProfiles;

public class CardReviewSummaryData
{
    public Ulid Id { get; set; }
    public string Title { get; set; }
    public string Contents { get; set; }
    public DateTime CreatedTime { get; set; }
    public DateTime ModifiedTime { get; set; }

    public CardReviewSummaryData(CardReview c)
    {
        Id = c.Id;
        Title = c.Title;
        Contents = c.Contents;
        CreatedTime = c.CreatedTime;
        ModifiedTime = c.ModifiedTime;
    }
}
