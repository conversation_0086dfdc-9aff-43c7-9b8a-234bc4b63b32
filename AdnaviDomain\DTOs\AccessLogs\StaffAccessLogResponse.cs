using Adnavi.Utils.Models;

namespace Adnavi.Domain.DTOs.AccessLogs;

public class StaffAccessLogResponse : IDataTransferObject
{
    public DateTime AccessTime { get; set; }
    public string OrganizationName { get; set; }
    public int Advsn { get; set; }
    public string Details { get; set; }
    public string IpAddress { get; set; }
    public string Kind { get; set; }
    public string? CvId { get; set; }
    public string? BrowserId { get; set; }
}
