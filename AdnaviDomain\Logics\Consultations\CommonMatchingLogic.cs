using Adnavi.Domain.Logics.Common;
using Adnavi.Domain.Models.Consultations.BatchIntroductions;
using Adnavi.Domain.Models.Consultations.InsuranceBatchIntroductions;
using Microsoft.Extensions.Options;

namespace Adnavi.Domain.Logics.Consultations;

public class CommonMatchingLogic
{
    private readonly IEnumerable<string> _disallowedDomains;
    private readonly IEnumerable<string> _disallowedIpList;
    private readonly LogDictionary _logDictionary;

    public CommonMatchingLogic(
        IOptions<AdnaviDomainSettings> adnaviDomainSettings,
        LogDictionary logDictionary
    )
    {
        _disallowedDomains = adnaviDomainSettings
            .Value
            .DisallowedMailDomainList;
        _disallowedIpList = adnaviDomainSettings.Value.DisallowedIpList;
        _logDictionary = logDictionary;
    }

    public void CheckValidInvestor(ICanValidateInvestor entity)
    {
        CheckMail(entity.EMail);
        CheckRemoteHost(entity.RemoteHost);
    }

    public void CheckValidInvestor(InsuranceBatchIntroduction entity)
    {
        CheckMail(entity.EMail);
        CheckRemoteHost(entity.RemoteHost);
    }

    public void CheckValidInvestor(string mailAddress, string remoteHost)
    {
        CheckMail(mailAddress);
        CheckRemoteHost(remoteHost);
    }

    private void CheckMail(string eMail)
    {
        foreach (var domain in _disallowedDomains)
        {
            if (eMail.EndsWith(domain))
            {
                _logDictionary.DisallowedRequestByDomainList(eMail);
                throw new DisallowedMailError();
            }
        }
    }

    private void CheckRemoteHost(string remoteHost)
    {
        foreach (var disallowIp in _disallowedIpList)
        {
            var sp = remoteHost.Split(",", StringSplitOptions.TrimEntries);
            if (sp.Length >= 1 && sp[0] == disallowIp)
            {
                _logDictionary.DisallowedRequestByIpList(disallowIp);
                throw new DisallowedIpError();
            }
        }
    }
}
