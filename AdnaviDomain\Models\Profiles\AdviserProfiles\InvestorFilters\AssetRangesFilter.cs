using Adnavi.Domain.Models.Consultations.BatchIntroductions;
using Adnavi.Domain.Models.Profiles.InvestorProfiles;

namespace Adnavi.Domain.Models.Profiles.AdviserProfiles.InvestorFilters;

public class AssetRangesFilter : IInvestorFilter
{
    private readonly IEnumerable<AssetRanges> _includeAssetRanges;

    public AssetRangesFilter(IEnumerable<AssetRanges> includeAssetRanges)
    {
        _includeAssetRanges = includeAssetRanges;
    }

    public bool IsMatched(IInvestor investor)
    {
        return _includeAssetRanges.Contains(investor.AssetRange)
            // 投資家の資産が1000万円〜3000万円未満の場合、1000万円〜2000万円未満と2000万円〜3000万円未満のどちらかが含まれている場合にマッチする
            || (
                investor.AssetRange == AssetRanges.B1000_A3000
                && (
                    _includeAssetRanges.Contains(AssetRanges.B1000_A2000)
                    || _includeAssetRanges.Contains(AssetRanges.B2000_A3000)
                )
            )
            // 投資家の資産が1億円〜5億円未満の場合、1億円〜2億円未満、2億円〜3億円未満、3億円〜4億円未満、4億円〜5億円未満のどれかが含まれている場合にマッチする
            || (
                investor.AssetRange == AssetRanges.B10000_A50000
                && (
                    _includeAssetRanges.Contains(AssetRanges.B10000_A20000)
                    || _includeAssetRanges.Contains(AssetRanges.B20000_A30000)
                    || _includeAssetRanges.Contains(AssetRanges.B30000_A40000)
                    || _includeAssetRanges.Contains(AssetRanges.B40000_A50000)
                )
            )
            // アドバイザーが1000万円〜3000万円未満を受け付ける場合、1000万円〜2000万円未満と2000万円〜3000万円未満の投資家を受け付ける
            || _includeAssetRanges.Contains(AssetRanges.B1000_A3000)
                && (
                    investor.AssetRange == AssetRanges.B1000_A2000
                    || investor.AssetRange == AssetRanges.B2000_A3000
                )
            // アドバイザーが1億円〜5億円未満を受け付ける場合、1億円〜2億円未満、2億円〜3億円未満、3億円〜4億円未満、4億円〜5億円未満の投資家を受け付ける
            || _includeAssetRanges.Contains(AssetRanges.B10000_A50000)
                && (
                    investor.AssetRange == AssetRanges.B10000_A20000
                    || investor.AssetRange == AssetRanges.B20000_A30000
                    || investor.AssetRange == AssetRanges.B30000_A40000
                    || investor.AssetRange == AssetRanges.B40000_A50000
                );
    }
}
