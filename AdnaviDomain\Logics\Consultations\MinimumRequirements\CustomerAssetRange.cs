using Adnavi.Domain.Logics.Consultations.BatchIntroductions;
using Adnavi.Domain.Models.Profiles.InvestorProfiles;

namespace Adnavi.Domain.Logics.Consultations.MinimumRequirements;

public class CustomerAssetRange : IRequirement
{
    private readonly AssetRanges _assetRange;

    public CustomerAssetRange(AssetRanges assetRange)
    {
        _assetRange = assetRange;
    }

    public bool CheckAdviser(AdviserRequirementsCheck data)
    {
        var customerStatics = data.CustomerStatics;
        if (customerStatics == null)
            return false;
        if (_assetRange == AssetRanges.B0_A500)
        {
            if (
                customerStatics.OwnedAssetsLess500 == null
                || customerStatics.OwnedAssetsLess500 < 20
            )
                return false;
        }
        else if (_assetRange == AssetRanges.B500_A1000)
        {
            if (
                customerStatics.OwnedAssetsLess1000 == null
                || customerStatics.OwnedAssetsLess1000 < 20
            )
                return false;
        }
        else if (
            _assetRange == AssetRanges.B1000_A2000
            || _assetRange == AssetRanges.B2000_A3000
        )
        {
            if (
                customerStatics.OwnedAssetsLess3000 == null
                || customerStatics.OwnedAssetsLess3000 < 20
            )
                return false;
        }
        else if (_assetRange == AssetRanges.B3000_A5000)
        {
            if (
                customerStatics.OwnedAssetsLess5000 == null
                || customerStatics.OwnedAssetsLess5000 < 20
            )
                return false;
        }
        else if (_assetRange == AssetRanges.B5000_A7000)
        {
            if (
                customerStatics.OwnedAssetsLess7000 == null
                || customerStatics.OwnedAssetsLess7000 < 20
            )
                return false;
        }
        else if (_assetRange == AssetRanges.B7000_A10000)
        {
            if (
                customerStatics.OwnedAssetsLess10000 == null
                || customerStatics.OwnedAssetsLess10000 < 20
            )
                return false;
        }
        else if (
            _assetRange == AssetRanges.B10000_A20000
            || _assetRange == AssetRanges.B20000_A30000
            || _assetRange == AssetRanges.B30000_A40000
            || _assetRange == AssetRanges.B40000_A50000
        )
        {
            if (
                customerStatics.OwnedAssetsLess50000 == null
                || customerStatics.OwnedAssetsLess50000 < 20
            )
                return false;
        }
        else if (
            _assetRange == AssetRanges.B50000_A100000
            || _assetRange == AssetRanges.B100000
        )
        {
            if (
                customerStatics.OwnedAssetGreaterEqual50000 == null
                || customerStatics.OwnedAssetGreaterEqual50000 < 20
            )
                return false;
        }
        return true;
    }
}
