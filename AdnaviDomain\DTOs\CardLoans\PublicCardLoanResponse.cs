using Adnavi.Domain.Models.CardLoans;
using Adnavi.Utils.Models;
using Microsoft.EntityFrameworkCore;
using NUlid;

namespace Adnavi.Domain.DTOs.CardLoans;

public class PublicCardLoanResponse : IDataTransferObject
{
    public string Name { get; set; }
    public CardLoanOrganizationTypes CardLoanOrganizationType { get; set; }

    public static async IAsyncEnumerable<PublicCardLoanResponse> FromQuery(
        IQueryable<CardLoan> query
    )
    {
        var data = await query
            .AsNoTracking()
            .Select(
                c =>
                    new PublicCardLoanResponse
                    {
                        Name = c.Name,
                        CardLoanOrganizationType = c.CardLoanOrganizationType.Id
                    }
            )
            .ToArrayAsync();

        foreach (var d in data)
        {
            yield return d;
        }
    }
}
