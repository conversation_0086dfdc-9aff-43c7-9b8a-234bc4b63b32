using Adnavi.Domain.DTOs.Invoices.MatchingInvoices;
using Adnavi.Domain.Models.CardLoans;
using Adnavi.Utils.Models;
using NUlid;

namespace Adnavi.Domain.DTOs.CardLoans;

public class PublicCardLoanSummaryResponse : IDataTransferObject
{
    public Ulid? Id { get; set; }
    public string Name { get; set; }
    public CardLoanOrganizationTypes CardLoanOrganizationType { get; set; }
    public IEnumerable<BorrowMethods> BorrowMethods { get; set; }
    public IEnumerable<PayBackMethods> PayBackMethods { get; set; }
    public float Point { get; set; }
    public decimal? MaximumAnnualInterest { get; set; }
    public decimal? MinimumAnnualInterest { get; set; }
    public string AnnualInterestText { get; set; }
    public int? MaximumScreeningSeconds { get; set; }
    public int? MinimumScreeningSeconds { get; set; }
    public string ScreeningTimeText { get; set; }
    public int? MaximumFinancingSeconds { get; set; }
    public int? MinimumFinancingSeconds { get; set; }
    public string FinancingTimeText { get; set; }
    public int MaximumLoanAmount { get; set; }
    public string MaximumLoanAmountText { get; set; }
    public bool NoIncomeCertificate { get; set; }
    public string IncomeCertificateRequirements { get; set; }
    public bool OtherLoanAvailable { get; set; }
    public bool WeekendAvailable { get; set; }
    public bool NotVisitingStore { get; set; }
    public bool WebOnlyRequest { get; set; }
    public bool NoPhysicallyMail { get; set; }
    public bool PhoneNumberSpecification { get; set; }
    public ConvenienceStoreSwitchesData AvailableConvenienceStore { get; set; }
    public string OfficialSiteUrl { get; set; }
    public string SalesPoint { get; set; }
    public string Disclaimer { get; set; }
    public IEnumerable<CardLoanRecommendationData> Recommendations { get; set; }
    public IEnumerable<CardLoanNotRecommendationData> NotRecommendations { get; set; }
    public bool Hidden { get; set; }
    public Ulid? OfficialImageFileKey { get; set; }
    public int? ScreeningSecondsAssessmentPoint { get; set; }
    public int? FinancingSecondsAssessmentPoint { get; set; }
    public int? IncomeCertificateAssessmentPoint { get; set; }
    public int? AnnualInterestAssessmentPoint { get; set; }
    public bool? NoEnrollmentConfirmation { get; set; }

    public int? DailyApplyEndTime { get; set; }

    public PublicCardLoanSummaryResponse() { }

    public PublicCardLoanSummaryResponse(CardLoan cardLoan)
    {
        Id = cardLoan.Id;
        Name = cardLoan.Name;
        CardLoanOrganizationType = cardLoan.CardLoanOrganizationTypeId;
        BorrowMethods = cardLoan.BorrowMethods.Select(b => b.Id);
        PayBackMethods = cardLoan.PayBackMethods.Select(p => p.Id);
        Point = cardLoan.Point;
        MaximumAnnualInterest = cardLoan.MaximumAnnualInterest;
        MinimumAnnualInterest = cardLoan.MinimumAnnualInterest;
        AnnualInterestText = cardLoan.AnnualInterestText;
        MaximumScreeningSeconds = cardLoan.MaximumScreeningSeconds;
        MinimumScreeningSeconds = cardLoan.MinimumScreeningSeconds;
        ScreeningTimeText = cardLoan.ScreeningTimeText;
        MaximumFinancingSeconds = cardLoan.MaximumFinancingSeconds;
        MinimumFinancingSeconds = cardLoan.MinimumFinancingSeconds;
        FinancingTimeText = cardLoan.FinancingTimeText;
        MaximumLoanAmount = cardLoan.MaximumLoanAmount;
        MaximumLoanAmountText = cardLoan.MaximumLoanAmountText;
        NoIncomeCertificate = cardLoan.NoIncomeCertificate;
        IncomeCertificateRequirements = cardLoan.IncomeCertificateRequirements;
        OtherLoanAvailable = cardLoan.OtherLoanAvailable;
        WeekendAvailable = cardLoan.WeekendAvailable;
        NotVisitingStore = cardLoan.NotVisitingStore;
        WebOnlyRequest = cardLoan.WebOnlyRequest;
        NoPhysicallyMail = cardLoan.NoPhysicallyMail;
        PhoneNumberSpecification = cardLoan.PhoneNumberSpecification;
        AvailableConvenienceStore = new ConvenienceStoreSwitchesData(
            cardLoan.AvailableConvenienceStore
        );
        OfficialSiteUrl = cardLoan.OfficialSiteUrl;
        SalesPoint = cardLoan.SalesPoint;
        Disclaimer = cardLoan.Disclaimer;
        Recommendations = cardLoan.Recommendations
            .Select(x => new CardLoanRecommendationData(x))
            .ToList();
        NotRecommendations = cardLoan.NotRecommendations
            .Select(x => new CardLoanNotRecommendationData(x))
            .ToList();
        Hidden = cardLoan.Hidden;
        OfficialImageFileKey = cardLoan.OfficialImageFileKey;
        ScreeningSecondsAssessmentPoint =
            cardLoan.ScreeningSecondsAssessmentPoint;
        FinancingSecondsAssessmentPoint =
            cardLoan.FinancingSecondsAssessmentPoint;
        IncomeCertificateAssessmentPoint =
            cardLoan.IncomeCertificateAssessmentPoint;
        AnnualInterestAssessmentPoint = cardLoan.AnnualInterestAssessmentPoint;
        NoEnrollmentConfirmation = cardLoan.NoEnrollmentConfirmation;
        DailyApplyEndTime = cardLoan.DailyApplyEndTime;
    }
}
