using Adnavi.Domain.Common;
using MimeKit;

namespace Adnavi.Domain.Logics.Consultations.ConsultationRequests;

public interface IConsultationRequestMailTemplate
{
    MailNotification GetNotifications();
    MailboxAddress GetFromMailAddress();
    string GetInvestorSubject();
    string GetNotificationSubject(string originName);
    string GetContentsForInvestor();
    string GetContentsForNotification(NUlid.Ulid requestId, string remoteHost);
}
