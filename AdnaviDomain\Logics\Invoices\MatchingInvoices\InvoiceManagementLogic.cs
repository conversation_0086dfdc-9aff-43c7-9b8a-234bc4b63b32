/* AIによる要約(2025-06-17)

### `InvoiceManagementLogic` クラスの要約

この `InvoiceManagementLogic` クラスは、特定の**請求月 (`InvoiceMonth`) を基軸とした請求データの操作**に特化したロジックを提供します。
先の `MatchingInvoicesLogic` が案件全体（`MatchingInvoiceManagement`）を主軸にしていたのに対し、
このクラスは個別の請求レコード（`IntroductionInvoice`）そのものを操作の中心に据えています。

主に、月次の請求締め処理のような業務で利用されることを想定しています。

#### 主な機能

1.  **請求レコードの検索と集計**
    *   IFA法人、アドバイザー、**請求月**、ステータス（保留中/承認済）といった条件で、個別の請求レコードを精密に検索します (`GetQuery`, `Search`)。
    *   検索条件に合致する請求の合計金額、承認済み金額、法人数、アドバイザー数などを集計する機能 (`AggregateInvoices`) を提供し、ダッシュボードやレポート作成を支援します。

2.  **請求の一括承認**
    *   このクラスの最も特徴的な機能は、特定の請求月における「保留中 (`Hold`)」の請求を**一括で「承認済 (`Approved`)」ステータスに更新する** `ApproveInvoices` メソッドです。
    *   これにより、管理者は月末の締め処理として、その月の請求を確定させる操作を効率的に行うことができます。

#### クラスの役割と位置づけ

このクラスは、システムにおける**月次の請求締めプロセス**の中核を担います。管理者が「2024年5月分の請求」を確認し、
問題がなければ一括で承認して請求を確定させる、といった一連のワークフローを実現するためのバックエンドロジックを提供します。

要するに、**個々の請求レコードを「請求月」という単位で束ね、レビューや確定処理を行うためのツール**として機能するクラスです。
*/

using Adnavi.Domain.Models.Invoices.MatchingInvoices;
using Adnavi.Utils;
using Adnavi.Utils.Exceptions;
using Microsoft.EntityFrameworkCore;
using NUlid;

namespace Adnavi.Domain.Logics.Invoices.MatchingInvoices;

/// <summary>
/// 請求月をベースとした請求ロジック
/// </summary>
public partial class InvoiceManagementLogic
{
    private readonly AdnaviDomainContext _context;

    public InvoiceManagementLogic(AdnaviDomainContext context)
    {
        _context = context;
    }

    private IQueryable<IntroductionInvoice> GetQuery(
        Ulid? ifaCompanyId = null,
        Ulid? adviserId = null,
        InvoiceTypes? invoiceType = null,
        YearAndMonth? invoiceMonth = null,
        bool holdOnly = false,
        bool approvedOnly = false,
        MatchingInvoiceOrderColumn? orderColumn = null,
        bool descendence = false
    )
    {
        var query = _context.IntroductionInvoices
            .IgnoreQueryFilters()
            .AsQueryable();
        if (ifaCompanyId != null)
            query = query.Where(
                i => i.MatchingInvoiceManagement.IfaCompanyId == ifaCompanyId
            );
        if (adviserId != null)
            query = query.Where(
                i => i.MatchingInvoiceManagement.AdviserId == adviserId
            );
        if (invoiceType != null)
            query = query.Where(i => i.InvoiceType == invoiceType);

        if (invoiceMonth != null)
        {
            var month = invoiceMonth.Value;
            query = query.Where(i => i.InvoiceMonth == month);
        }

        if (holdOnly)
            query = query.Where(i => i.InvoiceStatus == InvoiceStatuses.Hold);

        if (approvedOnly)
            query = query.Where(
                i => i.InvoiceStatus == InvoiceStatuses.Approved
            );

        if (orderColumn != null)
        {
            query = (orderColumn, descendence) switch
            {
                (MatchingInvoiceOrderColumn.Id, true)
                    => query.OrderByDescending(
                        a => a.MatchingInvoiceManagementId
                    ),
                (MatchingInvoiceOrderColumn.Id, false)
                    => query.OrderBy(a => a.MatchingInvoiceManagementId),

                (MatchingInvoiceOrderColumn.CreatedTime, true)
                    => query
                        .OrderByDescending(
                            a => a.MatchingInvoiceManagement.CreatedTime
                        )
                        .ThenByDescending(a => a.MatchingInvoiceManagementId),
                (MatchingInvoiceOrderColumn.CreatedTime, false)
                    => query
                        .OrderBy(a => a.MatchingInvoiceManagement.CreatedTime)
                        .ThenBy(a => a.MatchingInvoiceManagementId),

                (MatchingInvoiceOrderColumn.CompanyName, true)
                    => query.OrderByDescending(
                        a =>
                            a.MatchingInvoiceManagement.IfaCompany == null
                                ? null
                                : a.MatchingInvoiceManagement.IfaCompany.Name
                    ),
                (MatchingInvoiceOrderColumn.CompanyName, false)
                    => query.OrderBy(
                        a =>
                            a.MatchingInvoiceManagement.IfaCompany == null
                                ? null
                                : a.MatchingInvoiceManagement.IfaCompany.Name
                    ),

                (MatchingInvoiceOrderColumn.AssetRange, true)
                    => query.OrderByDescending(
                        a =>
                            a.MatchingInvoiceManagement
                                .BatchIntroduction
                                .AssetRange
                    ),

                (MatchingInvoiceOrderColumn.AssetRange, false)
                    => query.OrderBy(
                        a =>
                            a.MatchingInvoiceManagement
                                .BatchIntroduction
                                .AssetRange
                    ),

                (MatchingInvoiceOrderColumn.ProgressStatus, true)
                    => query.OrderByDescending(
                        a => a.MatchingInvoiceManagement.ProgressStatus
                    ),

                (MatchingInvoiceOrderColumn.ProgressStatus, false)
                    => query.OrderBy(
                        a => a.MatchingInvoiceManagement.ProgressStatus
                    ),

                _ => throw new UnreachableCodeError(),
            };
        }

        return query;
    }

    public Task<int> GetCount(
        Ulid? ifaCompanyId = null,
        Ulid? adviserId = null,
        InvoiceTypes? invoiceType = null,
        YearAndMonth? invoiceMonth = null,
        bool holdOnly = false,
        bool approvedOnly = false
    )
    {
        var query = GetQuery(
            ifaCompanyId,
            adviserId,
            invoiceType,
            invoiceMonth,
            holdOnly,
            approvedOnly
        );
        return query.CountAsync();
    }

    public IQueryable<IntroductionInvoice> Search(
        Ulid? ifaCompanyId = null,
        Ulid? adviserId = null,
        InvoiceTypes? invoiceType = null,
        YearAndMonth? invoiceMonth = null,
        bool holdOnly = false,
        bool approvedOnly = false,
        uint offset = 0,
        uint count = 20,
        MatchingInvoiceOrderColumn orderColumn =
            MatchingInvoiceOrderColumn.CreatedTime,
        bool descendence = false
    )
    {
        var query = GetQuery(
            ifaCompanyId,
            adviserId,
            invoiceType,
            invoiceMonth,
            holdOnly,
            approvedOnly,
            orderColumn,
            descendence
        );

        return query
            .AsNoTracking()
            .Include(i => i.MatchingInvoiceManagement)
            .ThenInclude(m => m.BatchIntroduction)
            .ThenInclude(b => b.Advisers)
            .Skip((int)offset)
            .Take((int)count);
    }

    public async Task<MatchingInvoicesAggregateData> AggregateInvoices(
        Ulid? ifaCompanyId = null,
        Ulid? adviserId = null,
        InvoiceTypes? invoiceType = null,
        YearAndMonth? invoiceMonth = null,
        bool holdOnly = false,
        bool approvedOnly = false
    )
    {
        var query = GetQuery(
            ifaCompanyId: ifaCompanyId,
            adviserId: adviserId,
            invoiceType: invoiceType,
            invoiceMonth: invoiceMonth,
            holdOnly: holdOnly,
            approvedOnly: approvedOnly
        );

        // aggregate adviser count and total amount of invoice.
        var result = await query
            .GroupBy(m => 1)
            .Select(
                g =>
                    new MatchingInvoicesAggregateData(
                        g.Select(i => i.MatchingInvoiceManagement.IfaCompanyId)
                            .Distinct()
                            .Count(),
                        g.Select(i => i.MatchingInvoiceManagement.AdviserId)
                            .Distinct()
                            .Count(),
                        g.Select(
                                i =>
                                    i.MatchingInvoiceManagement
                                        .BatchIntroduction
                                        .Id
                            )
                            .Distinct()
                            .Count(),
                        // 取り消しされていない請求の合計金額
                        g.Where(
                                i => i.InvoiceStatus != InvoiceStatuses.Canceled
                            )
                            .Sum(i => i.InvoiceAmount) ?? 0,
                        g.Where(
                                i => i.InvoiceStatus == InvoiceStatuses.Approved
                            )
                            .Sum(i => i.InvoiceAmount) ?? 0,
                        // 保留中の請求の数
                        g.Where(i => i.InvoiceStatus == InvoiceStatuses.Hold)
                            .Count()
                    )
            )
            .SingleOrDefaultAsync();

        return result ?? new MatchingInvoicesAggregateData();
    }

    /// <summary>
    /// 請求データを確認完了にする
    /// </summary>
    /// <param name="year"></param>
    /// <param name="month"></param>
    /// <param name="ifaCompanyId"></param>
    /// <param name="adviserId"></param>
    public async Task ApproveInvoices(
        ushort year,
        byte month,
        Ulid? ifaCompanyId = null,
        Ulid? adviserId = null,
        InvoiceTypes? invoiceType = null
    )
    {
        var query = GetQuery(
            ifaCompanyId,
            adviserId,
            invoiceType,
            new YearAndMonth(year, month)
        );

        foreach (var invoice in query.ToArray())
        {
            if (invoice.InvoiceStatus == InvoiceStatuses.Hold)
            {
                invoice.InvoiceStatus = InvoiceStatuses.Approved;
                invoice.ModifiedTime = DateTime.UtcNow;
            }
        }
        await _context.SaveChangesAsync();
    }
}
