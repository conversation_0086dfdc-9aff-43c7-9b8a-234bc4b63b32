/* AIによる要約(2025-06-17)

### `MatchingInvoicesExportLogic` クラスの要約

この `MatchingInvoicesExportLogic` クラスは、**マッチング請求データをCSV形式でエクスポートする機能**に特化したロジックを担っています。
データベースから請求情報を取得し、CSVファイルとして整形して、メモリ上のストリーム (`MemoryStream`) として返す責務を持ちます。

Webアプリケーションにおいて、ユーザーが「CSVダウンロード」ボタンをクリックした際に、サーバーサイドでこのクラスが呼び出されることを想定しています。

#### 主な機能

1.  **利用者に応じたデータのエクスポート**
    *   **社内スタッフ向け (`StaffOutputCsv`)**: 全てのIFA法人の請求データをCSVとして出力します。
    *   **IFA法人向け (`IfaCompanyOutputCsv`)**: 指定された特定のIFA法人の請求データのみをCSVとして出力します。

2.  **請求月で絞り込んだエクスポート**
    *   上記の機能に加え、特定の「請求月」でデータをフィルタリングしたCSVを生成する機能も提供します
     (`StaffOutputCsvOnInvoiceMonth`, `IfaCompanyOutputCsvOnInvoiceMonth`)。これにより、月次の請求レポートを簡単に作成できます。

#### 技術的な特徴

*   **データベースアクセス**: `AdnaviDomainContext` を通じてデータベースから請求情報を読み取ります。
*   **CSV生成**: `CsvHelper` ライブラリを利用して、取得したデータを効率的にCSVフォーマットへ変換します。
*   **出力形式**: 生成されたCSVデータは、ファイルに直接書き出すのではなく `MemoryStream` オブジェクトとして返されます。
     これにより、Webサーバーは生成されたデータをファイルとしてクライアントに直接ダウンロードさせることが容易になります。

要するに、このクラスは「**誰が、どの期間の請求データをCSVで欲しいか**」という要求に応じて、適切なデータを出力するためのシンプルな窓口として機能します。
*/

using System.Globalization;
using System.Text;
using Adnavi.Utils;
using CsvHelper;
using CsvHelper.Configuration;
using Microsoft.EntityFrameworkCore;
using NUlid;

namespace Adnavi.Domain.Logics.Invoices.MatchingInvoices;

public partial class MatchingInvoicesExportLogic
{
    private readonly AdnaviDomainContext _context;

    public MatchingInvoicesExportLogic(AdnaviDomainContext context)
    {
        _context = context;
    }

    public async Task<MemoryStream> StaffOutputCsv()
    {
        return await GeneralOutputCsv(false, null);
    }

    public async Task<MemoryStream> StaffOutputCsvOnInvoiceMonth(
        YearAndMonth month
    )
    {
        var config = new CsvConfiguration(CultureInfo.InvariantCulture)
        {
            HasHeaderRecord = true
        };

        var dataExport = MatchingInvoiceCsvRecordOnInvoiceMonth.FromQuery(
            _context,
            month,
            null
        );

        var stream = new MemoryStream();
        using (
            var writeFile = new StreamWriter(
                stream,
                Encoding.UTF8,
                -1,
                leaveOpen: true
            )
        )
        {
            var csv = new CsvWriter(writeFile, config);
            csv.WriteHeader<MatchingInvoiceCsvRecordOnInvoiceMonth>();
            await csv.NextRecordAsync();
            foreach (var record in dataExport.Result)
            {
                csv.WriteRecord(record);
                await csv.NextRecordAsync();
            }
        }
        stream.Position = 0; // reset stream

        return stream;
    }

    public async Task<MemoryStream> IfaCompanyOutputCsv(Ulid id)
    {
        return await GeneralOutputCsv(true, id);
    }

    private async Task<MemoryStream> GeneralOutputCsv(
        bool isStaff,
        Ulid? ifaCompanyId
    )
    {
        var config = new CsvConfiguration(CultureInfo.InvariantCulture)
        {
            HasHeaderRecord = true
        };

        IAsyncEnumerable<MatchingInvoiceCsvRecord> dataExport;

        if (!isStaff && ifaCompanyId != null)
            dataExport = MatchingInvoiceCsvRecord.FromQuery(
                _context.MatchingInvoiceManagements
                    .IgnoreQueryFilters()
                    .Where(m => m.IfaCompanyId == ifaCompanyId)
                    .AsNoTracking()
            );
        else
            dataExport = MatchingInvoiceCsvRecord.FromQuery(
                _context.MatchingInvoiceManagements
                    .IgnoreQueryFilters()
                    .AsNoTracking()
            );

        var stream = new MemoryStream();
        using (
            var writeFile = new StreamWriter(
                stream,
                Encoding.UTF8,
                -1,
                leaveOpen: true
            )
        )
        {
            var csv = new CsvWriter(writeFile, config);
            csv.WriteHeader<MatchingInvoiceCsvRecord>();
            await csv.NextRecordAsync();
            await foreach (var record in dataExport)
            {
                csv.WriteRecord(record);
                await csv.NextRecordAsync();
            }
        }
        stream.Position = 0; // reset stream

        return stream;
    }

    public async Task<MemoryStream> IfaCompanyOutputCsvOnInvoiceMonth(
        YearAndMonth month,
        Ulid companyId
    )
    {
        var config = new CsvConfiguration(CultureInfo.InvariantCulture)
        {
            HasHeaderRecord = true
        };

        var dataExport = MatchingInvoiceCsvRecordOnInvoiceMonth.FromQuery(
            _context,
            month,
            companyId
        );

        var stream = new MemoryStream();
        using (
            var writeFile = new StreamWriter(
                stream,
                Encoding.UTF8,
                -1,
                leaveOpen: true
            )
        )
        {
            var csv = new CsvWriter(writeFile, config);
            csv.WriteHeader<MatchingInvoiceCsvRecordOnInvoiceMonth>();
            await csv.NextRecordAsync();
            foreach (var record in dataExport.Result)
            {
                csv.WriteRecord(record);
                await csv.NextRecordAsync();
            }
        }
        stream.Position = 0; // reset stream

        return stream;
    }
}
