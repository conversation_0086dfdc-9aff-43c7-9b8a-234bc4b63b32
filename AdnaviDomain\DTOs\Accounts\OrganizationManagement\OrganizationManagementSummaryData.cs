using System.ComponentModel.DataAnnotations;
using Adnavi.Domain.Models.Accounts;
using Adnavi.Utils.Models;
using Microsoft.EntityFrameworkCore;
using NUlid;

namespace Adnavi.Domain.DTOs.Accounts.OrganizationManagement;

public sealed class OrganizationManagementSummaryData : IDataTransferObject
{
    [Required]
    public string Name { get; set; }

    [Required]
    public Ulid Id { get; set; }

    public Ulid? ManagedIfaCompanyId { get; set; }

    public string? ManagedIfaCompanyName { get; set; }

    public Ulid? ManagedInsuranceCompanyId { get; set; }

    public string? ManagedInsuranceCompanyName { get; set; }

    [Required]
    public IEnumerable<UserManagementSummaryData> Users { get; set; }

    public static async IAsyncEnumerable<OrganizationManagementSummaryData> FromQuery(
        IQueryable<Organization> query,
        AdnaviDomainContext context
    )
    {
        var data = await query
            .AsNoTracking()
            .Select(
                o =>
                    new OrganizationManagementSummaryData
                    {
                        Name = o.Name,
                        Id = o.Id,
                        ManagedIfaCompanyId = o.ManagedIfaCompanyId,
                        ManagedIfaCompanyName =
                            o.ManagedIfaCompany == null
                                ? null
                                : o.ManagedIfaCompany.Name,
                        ManagedInsuranceCompanyId = o.ManagedInsuranceCompanyId,
                        ManagedInsuranceCompanyName =
                            o.ManagedInsuranceCompany == null
                                ? null
                                : o.ManagedInsuranceCompany.Name,
                    }
            )
            .ToArrayAsync();

        var organizationIds = data.Select(d => d.Id).ToList();
        var memberships = await UserManagementSummaryData
            .FromQuery(
                context.OrganizationMemberships
                    .AsNoTracking()
                    .Where(m => organizationIds.Contains(m.OrganizationId))
            )
            .ToArrayAsync();

        foreach (var o in data)
        {
            o.Users = memberships.Where(m => m.OrganizationId == o.Id);
            yield return o;
        }
    }
}
