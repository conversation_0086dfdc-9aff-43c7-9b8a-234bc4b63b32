using System.Text.Json.Serialization;

namespace Adnavi.Domain.Common.PostcodeJP
{
    public class PostcodeJPPostcodeResponse
    {
        [JsonPropertyName("prefCode")]
        public string PrefectureCode { get; set; } = "";

        [JsonPropertyName("cityCode")]
        public string CityCode { get; set; } = "";

        [JsonPropertyName("postcode")]
        public string PostCode { get; set; } = "";

        [JsonPropertyName("oldPostcode")]
        public string OldPostCode { get; set; } = "";

        [JsonPropertyName("pref")]
        public string Pref { get; set; } = "";

        [JsonPropertyName("city")]
        public string City { get; set; } = "";

        [JsonPropertyName("town")]
        public string Town { get; set; } = "";

        [JsonPropertyName("allAddress")]
        public string AllAddress { get; set; } = "";

        [JsonPropertyName("hiragana")]
        public Ruby Hiragana { get; set; } = new();

        [JsonPropertyName("halfWidthKana")]
        public Ruby KanaHalf { get; set; } = new();

        [JsonPropertyName("fullWidthKana")]
        public Ruby Katakana { get; set; } = new();

        [JsonPropertyName("generalPostcode")]
        public bool IsGeneral { get; set; }

        [JsonPropertyName("officePostcode")]
        public bool IsOffice { get; set; }

        [JsonPropertyName("location")]
        public Location Location { get; set; } = new();
    }

    public class Location
    {
        [JsonPropertyName("latitude")]
        public double? Latitude { get; set; }

        [JsonPropertyName("longitude")]
        public double? Longitude { get; set; }
    }

    public class Ruby
    {
        [JsonPropertyName("pref")]
        public string Prefecture { get; set; } = "";

        [JsonPropertyName("city")]
        public string City { get; set; } = "";

        [JsonPropertyName("town")]
        public string Town { get; set; } = "";

        [JsonPropertyName("allAddress")]
        public string Address { get; set; } = "";
    }
}
