name: Deploy to Amazon ECS(develop1)

on:
  push:
    branches: [develop, deploy]
  #pull_request:
  #  branches: [master]

env:
  ENVIRONMENT: "development"
  URL: "https://api.test-navi.net"

  AWS_SECRET_ACCESS_KEY: ${{ secrets.d1_AWS_SECRET_ACCESS_KEY }}

  AWS_REGION: ${{ vars.d1_AWS_REGION }}
  AWS_ACCESS_KEY_ID: ${{ vars.d1_AWS_ACCESS_KEY_ID }}
  ECR_REPOSITORY: ${{ vars.d1_ECR_REPOSITORY }}

defaults:
  run:
    shell: bash

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    timeout-minutes: 15
    permissions:
      packages: write
      contents: read
      deployments: write

    steps:
      - name: Create GitHub deployment
        uses: chrnorm/deployment-action@v2
        id: deployment
        with:
          token: "${{ github.token }}"
          environment: ${{ env.ENVIRONMENT }}
          environment-url: ${{ env.URL }}

      - name: Checkout
        uses: actions/checkout@v3

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ env.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ env.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
        with:
          mask-password: "true"

      - name: Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_TAG: latest
          DOCKER_BUILDKIT: 1
        run: |
          echo "${{ github.sha }}" > .revision
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG . -f CoreOneWebApi/Dockerfile.mig
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          echo “image=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG” >> $GITHUB_OUTPUT

      - name: Update deployment status (success)
        if: success()
        uses: chrnorm/deployment-status@v2
        with:
          token: "${{ github.token }}"
          environment-url: ${{ steps.deployment.outputs.environment_url }}
          deployment-id: ${{ steps.deployment.outputs.deployment_id }}
          state: "success"
