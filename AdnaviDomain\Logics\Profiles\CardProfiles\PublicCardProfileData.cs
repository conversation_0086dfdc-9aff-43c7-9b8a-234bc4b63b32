﻿using Adnavi.Domain.Models.Profiles.CardProfiles;
using NUlid;

namespace Adnavi.Domain.Logics.Profiles.CardProfiles;

/// <summary>
/// PublicCardProfileSummaryData
/// /// </summary>
/// <seealso cref="CardProfile" />
public sealed class PublicCardProfileData
{
    public Ulid Id { get; set; }
    public string CardCompanyName { get; set; }
    public string CardName { get; set; }
    public string CardRankName { get; set; }
    public string ReferenceID { get; set; }
    public ICollection<CardIssuanceConditionData> IssuanceConditions { get; set; }
    public ICollection<CardRedemptionData> Redemptions { get; set; }
    public ICollection<CardPromotionData> Promotions { get; set; }
    public ICollection<InternationalCardBrands> InternationalCardBrands { get; set; }
    public ICollection<CardSensitivityData> Sensitivities { get; set; }
    public string IntroductionText { get; set; }
    public int? MaximumAnnualFee { get; set; }
    public int? MinimumAnnualFee { get; set; }
    public string? Url { get; set; }
    public Ulid? CardImage { get; set; }

    public bool PickUp { get; set; }
    public bool? IsMilePoint { get; set; }

    public PublicCardProfileData(CardProfile c, AdnaviDomainContext context)
    {
        Id = c.Id;
        CardCompanyName = c.CardCompanyName;
        CardName = c.CardName;
        CardRankName = c.CardRankName;
        ReferenceID = c.ReferenceID;
        IssuanceConditions = context.CardIssuanceConditions
            .Where(x => x.CardProfileId == c.Id)
            .Select(
                x => new CardIssuanceConditionData() { Statement = x.Statement }
            )
            .ToList();
        Redemptions = context.CardRedemptions
            .Where(x => x.CardProfileId == c.Id)
            .Select(
                x =>
                    new CardRedemptionData()
                    {
                        MinimumPercent = x.MinimumPercent,
                        MaximumPercent = x.MaximumPercent,
                        Statement = x.Statement
                    }
            )
            .ToList();
        Promotions = context.CardPromotions
            .Where(x => x.CardProfileId == c.Id)
            .Select(
                x =>
                    new CardPromotionData()
                    {
                        Statement = x.Statement,
                        StartDate = x.StartDate,
                        EndDate = x.EndDate
                    }
            )
            .ToList();
        InternationalCardBrands = c.InternationalCardBrands
            .Select(x => x.Id)
            .ToList();
        Sensitivities = context.CardSensitivities
            .Where(x => x.CardProfileId == c.Id)
            .Select(x => new CardSensitivityData() { Statement = x.Statement })
            .ToList();
        IntroductionText = c.IntroductionText;
        MaximumAnnualFee = c.MaximumAnnualFee;
        MinimumAnnualFee = c.MinimumAnnualFee;
        Url = c.Url;
        CardImage = c.CardImage;
        PickUp = c.PickUp;
        IsMilePoint = c.IsMilePoint;
    }
}
