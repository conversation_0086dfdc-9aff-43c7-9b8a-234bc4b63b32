﻿using Adnavi.Domain.Models.Invoices.MatchingInvoices;
using Adnavi.Domain.Models.Profiles.InvestorProfiles;
using Adnavi.Utils;
using CsvHelper.Configuration.Attributes;
using Microsoft.EntityFrameworkCore;

namespace Adnavi.Domain.Logics.Profiles.AdviserProfiles;

public sealed class AdviserInvoiceStatusCsvRecord
{
    [Name("ID")]
    public string Id { get; set; }

    [Name("期間")]
    public string Period { get; set; }

    [Name("名前")]
    public string FullName { get; set; }

    [Name("IFA法人名")]
    public string? IfaCompanyName { get; set; }

    [Name("金融資産")]
    public string AssetRange { get; set; }

    [Name("未入力")]
    public int HoldCount { get; set; }

    [Name("送客件数")]
    public int InvoiceCount { get; set; }

    [Name("返信件数")]
    public int ContactCount { get; set; }

    [Name("面談件数")]
    public int InterviewedCount { get; set; }

    [Name("成約件数")]
    public int ExecutedCount { get; set; }

    [Name("ペンディング件数")]
    public int PendingCount { get; set; }

    private AdviserInvoiceStatusCsvRecord() { }

    public static async Task<
        IEnumerable<AdviserInvoiceStatusCsvRecord>
    > FromQuery(AdnaviDomainContext context, YearAndMonth? matchingMonth = null)
    {
        var matchingInvoices = await context.MatchingInvoiceManagements
            .Select(
                m =>
                    new
                    {
                        CreatedTime = m.CreatedTime ?? DateTime.MinValue,
                        AdviserId = m.AdviserId,
                        ProgressStatus = m.ProgressStatus
                            ?? ProgressStatuses.Hold,
                        AssetRange = m.BatchIntroduction.AssetRange
                    }
            )
            .AsNoTracking()
            .ToListAsync();

        if (matchingMonth != null)
        {
            matchingInvoices = matchingInvoices
                .Where(
                    m =>
                        (ushort)m.CreatedTime.Year == matchingMonth.Value.Year
                        && (byte)m.CreatedTime.Month
                            == matchingMonth.Value.Month
                )
                .ToList();
        }

        var advisers = await context.Advisers
            .Where(a => a.IfaCompany != null)
            .Select(
                a =>
                    new
                    {
                        AdviserId = a.Id,
                        FullName = a.FullName,
                        IfaCompanyName = a.IfaCompany == null
                            ? ""
                            : a.IfaCompany.Name,
                        CompanyByCompanyIntroduction = a.CompanyByCompanyIntroduction,
                        BatchIntroductionEnabled = a.BatchIntroductionSetting
                        == null
                            ? false
                            : a.BatchIntroductionSetting.Enabled,
                    }
            )
            .AsNoTracking()
            .ToListAsync();

        var records = new List<AdviserInvoiceStatusCsvRecord>();
        foreach (var adviser in advisers)
        {
            foreach (var assetRange in context.AssetRanges)
            {
                var result = new AdviserInvoiceStatusCsvRecord
                {
                    Id = adviser.AdviserId.ToString(),
                    Period =
                        matchingMonth == null
                            ? "全期間"
                            : matchingMonth.Value.ToString(),
                    FullName = adviser.FullName,
                    IfaCompanyName = adviser.IfaCompanyName,
                    AssetRange = assetRange.DisplayName ?? "",
                    HoldCount = matchingInvoices.Count(
                        m =>
                            m.AdviserId == adviser.AdviserId
                            && m.AssetRange == assetRange.Id
                            && m.ProgressStatus == ProgressStatuses.Hold
                    ),
                    InvoiceCount = matchingInvoices.Count(
                        m =>
                            m.AdviserId == adviser.AdviserId
                            && m.AssetRange == assetRange.Id
                            && m.ProgressStatus != ProgressStatuses.Hold
                    ),
                    ContactCount = matchingInvoices.Count(
                        m =>
                            m.AdviserId == adviser.AdviserId
                            && m.AssetRange == assetRange.Id
                            && (int)m.ProgressStatus
                                >= (int)ProgressStatuses.OnlyOnceContact
                    ),
                    InterviewedCount = matchingInvoices.Count(
                        m =>
                            m.AdviserId == adviser.AdviserId
                            && m.AssetRange == assetRange.Id
                            && (int)m.ProgressStatus
                                >= (int)ProgressStatuses.Interviewed
                            && m.ProgressStatus
                                != ProgressStatuses.ScheduledInterview
                    ),
                    ExecutedCount = matchingInvoices.Count(
                        m =>
                            m.AdviserId == adviser.AdviserId
                            && m.AssetRange == assetRange.Id
                            && (int)m.ProgressStatus
                                >= (int)ProgressStatuses.Executed
                    ),
                    PendingCount = matchingInvoices.Count(
                        m =>
                            m.AdviserId == adviser.AdviserId
                            && m.AssetRange == assetRange.Id
                            && (int)m.ProgressStatus
                                >= (int)ProgressStatuses.Suggested
                            && m.ProgressStatus != ProgressStatuses.Executed
                    )
                };
                if (result.InvoiceCount == 0 && result.HoldCount == 0)
                    continue;
                records.Add(result);
            }
        }
        return records;
    }
}
