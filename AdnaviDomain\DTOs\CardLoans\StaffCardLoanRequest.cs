using Adnavi.Domain.DTOs.Invoices.MatchingInvoices;
using Adnavi.Domain.Models.CardLoans;
using Adnavi.Utils.Models;
using NUlid;

namespace Adnavi.Domain.DTOs.CardLoans;

public class StaffCardLoanRequest : IDataTransferObject
{
    public Ulid? Id { get; set; }
    public string Name { get; set; }
    public CardLoanOrganizationTypes CardLoanOrganizationType { get; set; }
    public IEnumerable<BorrowMethods> BorrowMethods { get; set; }
    public IEnumerable<PayBackMethods> PayBackMethods { get; set; }
    public float Point { get; set; }
    public decimal? MaximumAnnualInterest { get; set; }
    public decimal? MinimumAnnualInterest { get; set; }
    public string AnnualInterestText { get; set; }
    public int? MaximumScreeningSeconds { get; set; }
    public int? MinimumScreeningSeconds { get; set; }
    public string ScreeningTimeText { get; set; }
    public int? MaximumFinancingSeconds { get; set; }
    public int? MinimumFinancingSeconds { get; set; }
    public string FinancingTimeText { get; set; }
    public int MaximumLoanAmount { get; set; }
    public string MaximumLoanAmountText { get; set; }
    public bool NoIncomeCertificate { get; set; }
    public string IncomeCertificateRequirements { get; set; }
    public bool OtherLoanAvailable { get; set; }
    public bool WeekendAvailable { get; set; }
    public bool NotVisitingStore { get; set; }
    public bool WebOnlyRequest { get; set; }
    public bool NoPhysicallyMail { get; set; }
    public bool PhoneNumberSpecification { get; set; }
    public ConvenienceStoreSwitchesData AvailableConvenienceStore { get; set; }
    public string OfficialSiteUrl { get; set; }
    public string SalesPoint { get; set; }
    public string Disclaimer { get; set; }
    public IEnumerable<CardLoanRecommendationData> Recommendations { get; set; }
    public IEnumerable<CardLoanNotRecommendationData> NotRecommendations { get; set; }
    public bool Hidden { get; set; }
    public Ulid? OfficialImageFileKey { get; set; }
    public int? ScreeningSecondsAssessmentPoint { get; set; }
    public int? FinancingSecondsAssessmentPoint { get; set; }
    public int? IncomeCertificateAssessmentPoint { get; set; }
    public int? AnnualInterestAssessmentPoint { get; set; }

    public bool? NoEnrollmentConfirmation { get; set; }

    public int? DailyApplyEndTime { get; set; }

    public int? LoanExistsAttributeScore { get; set; }

    public int? NoLoanExistsAttributeScore { get; set; }

    public int? Age10sAttributeScore { get; set; }

    public int? Age20sAttributeScore { get; set; }

    public int? Age30sAttributeScore { get; set; }

    public int? Age40sAttributeScore { get; set; }

    public int? Age50sAttributeScore { get; set; }

    public int? Age60sAttributeScore { get; set; }

    public int? Age70sAttributeScore { get; set; }

    public int? IncomeB0_A500AttributeScore { get; set; }

    public int? IncomeB500_A800AttributeScore { get; set; }

    public int? IncomeB800_A1000AttributeScore { get; set; }

    public int? IncomeB1000_A1500AttributeScore { get; set; }

    public int? IncomeB1500_A2000AttributeScore { get; set; }

    public int? IncomeB2000_A3000AttributeScore { get; set; }

    public int? IncomeB3000_A4000AttributeScore { get; set; }

    public int? IncomeB4000AttributeScore { get; set; }

    public int? OccupationOfficeWorkerAttributeScore { get; set; }

    public int? OccupationUnemployedAttributeScore { get; set; }

    public int? OccupationProprietorAttributeScore { get; set; }

    public int? OccupationExecutiveAttributeScore { get; set; }

    public int? OccupationGovernmentAttributeScore { get; set; }

    public int? OccupationProfessionAttributeScore { get; set; }

    public int? OccupationDoctorAttributeScore { get; set; }

    public int? OccupationOtherAttributeScore { get; set; }

    public CardLoan To(CardLoan to, AdnaviDomainContext context)
    {
        to.Id = Ulid.NewUlid();
        to.Name = Name;
        to.CardLoanOrganizationType = context.CardLoanOrganizationTypes.Single(
            c => c.Id == CardLoanOrganizationType
        );
        to.PayBackMethods?.Clear();
        to.PayBackMethods = PayBackMethods
            .Select(p => context.PayBackMethods.Single(q => q.Id == p))
            .ToList();
        to.BorrowMethods?.Clear();
        to.BorrowMethods = BorrowMethods
            .Select(b => context.BorrowMethods.Single(q => q.Id == b))
            .ToList();
        to.Point = Point;
        to.MaximumAnnualInterest = MaximumAnnualInterest;
        to.MinimumAnnualInterest = MinimumAnnualInterest;
        to.AnnualInterestText = AnnualInterestText;
        to.MaximumScreeningSeconds = MaximumScreeningSeconds;
        to.MinimumScreeningSeconds = MinimumScreeningSeconds;
        to.ScreeningTimeText = ScreeningTimeText;
        to.MaximumFinancingSeconds = MaximumFinancingSeconds;
        to.MinimumFinancingSeconds = MinimumFinancingSeconds;
        to.FinancingTimeText = FinancingTimeText;
        to.MaximumLoanAmount = MaximumLoanAmount;
        to.MaximumLoanAmountText = MaximumLoanAmountText;
        to.NoIncomeCertificate = NoIncomeCertificate;
        to.IncomeCertificateRequirements = IncomeCertificateRequirements;
        to.OtherLoanAvailable = OtherLoanAvailable;
        to.WeekendAvailable = WeekendAvailable;
        to.NotVisitingStore = NotVisitingStore;
        to.WebOnlyRequest = WebOnlyRequest;
        to.NoPhysicallyMail = NoPhysicallyMail;
        to.PhoneNumberSpecification = PhoneNumberSpecification;
        to.AvailableConvenienceStore = AvailableConvenienceStore.To(
            new ConvenienceStoreSwitches()
        );
        to.OfficialSiteUrl = OfficialSiteUrl;
        to.SalesPoint = SalesPoint;
        to.Disclaimer = Disclaimer;
        to.Recommendations = Recommendations
            .Select(
                x =>
                    new CardLoanRecommendation()
                    {
                        Id = Ulid.NewUlid(),
                        Content = x.Content,
                        CardLoanId = to.Id
                    }
            )
            .ToList();
        to.NotRecommendations = NotRecommendations
            .Select(
                x =>
                    new CardLoanNotRecommendation()
                    {
                        Id = Ulid.NewUlid(),
                        Content = x.Content,
                        CardLoanId = to.Id
                    }
            )
            .ToList();
        to.Hidden = Hidden;
        to.OfficialImageFileKey = OfficialImageFileKey;
        to.ScreeningSecondsAssessmentPoint = ScreeningSecondsAssessmentPoint;
        to.FinancingSecondsAssessmentPoint = FinancingSecondsAssessmentPoint;
        to.IncomeCertificateAssessmentPoint = IncomeCertificateAssessmentPoint;
        to.AnnualInterestAssessmentPoint = AnnualInterestAssessmentPoint;
        to.NoEnrollmentConfirmation = NoEnrollmentConfirmation;
        to.DailyApplyEndTime = DailyApplyEndTime;
        to.NoLoanExistsAttributeScore = NoLoanExistsAttributeScore;
        to.Age10sAttributeScore = Age10sAttributeScore;
        to.Age20sAttributeScore = Age20sAttributeScore;
        to.Age30sAttributeScore = Age30sAttributeScore;
        to.LoanExistsAttributeScore = LoanExistsAttributeScore;
        to.Age40sAttributeScore = Age40sAttributeScore;
        to.Age50sAttributeScore = Age50sAttributeScore;
        to.Age60sAttributeScore = Age60sAttributeScore;
        to.Age70sAttributeScore = Age70sAttributeScore;
        to.IncomeB0_A500AttributeScore = IncomeB0_A500AttributeScore;
        to.IncomeB500_A800AttributeScore = IncomeB500_A800AttributeScore;
        to.IncomeB800_A1000AttributeScore = IncomeB800_A1000AttributeScore;
        to.IncomeB1000_A1500AttributeScore = IncomeB1000_A1500AttributeScore;
        to.IncomeB1500_A2000AttributeScore = IncomeB1500_A2000AttributeScore;
        to.IncomeB2000_A3000AttributeScore = IncomeB2000_A3000AttributeScore;
        to.IncomeB3000_A4000AttributeScore = IncomeB3000_A4000AttributeScore;
        to.IncomeB4000AttributeScore = IncomeB4000AttributeScore;
        to.OccupationOfficeWorkerAttributeScore =
            OccupationOfficeWorkerAttributeScore;
        to.OccupationUnemployedAttributeScore =
            OccupationUnemployedAttributeScore;
        to.OccupationProprietorAttributeScore =
            OccupationProprietorAttributeScore;
        to.OccupationExecutiveAttributeScore =
            OccupationExecutiveAttributeScore;
        to.OccupationGovernmentAttributeScore =
            OccupationGovernmentAttributeScore;
        to.OccupationProfessionAttributeScore =
            OccupationProfessionAttributeScore;
        to.OccupationDoctorAttributeScore = OccupationDoctorAttributeScore;
        to.OccupationOtherAttributeScore = OccupationOtherAttributeScore;

        return to;
    }
}
