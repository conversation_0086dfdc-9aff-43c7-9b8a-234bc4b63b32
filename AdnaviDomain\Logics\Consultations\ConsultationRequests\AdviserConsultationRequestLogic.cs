using Adnavi.Domain.DTOs.Consultations.ConsultationRequests;
using Adnavi.Domain.Logics.Profiles.AdviserProfiles;
using Adnavi.Domain.Models;
using Adnavi.Domain.Models.Profiles.InvestorProfiles;
using CsvHelper.Configuration;
using CsvHelper;
using Microsoft.EntityFrameworkCore;
using NUlid;
using System.Globalization;
using System.Text;
using Adnavi.Domain.Models.Consultations.ConsultationRequests;
using Adnavi.Domain.Models.Consultations.BatchIntroductions;
using Adnavi.Domain.Models.Profiles.AdviserProfiles;
using Adnavi.Domain.Logics.Consultations.BatchIntroductions;
using Adnavi.Domain.DTOs.Profiles.AdviserProfiles;
using Adnavi.Domain.Models.Accounts;
using Adnavi.Utils.Exceptions;
using Adnavi.Domain.Logics.Common.EMails;
using Microsoft.Extensions.Options;
using Adnavi.Domain.Logics.Common;

namespace Adnavi.Domain.Logics.Consultations.ConsultationRequests
{
    public class AdviserConsultationRequestLogic
    {
        private readonly AdnaviDomainContext _context;
        private readonly ModelUtils _model;
        private readonly EMailService _emailService;
        private readonly InvestorsSettings _settings;
        private readonly AdviserProfileCommonLogic _common;
        private readonly BatchIntroductionLogic _batchIntroductionLogic;
        private readonly LogDictionary _logDictionary;
        private readonly string _privacyInformationDeletionSalt;

        public AdviserConsultationRequestLogic(
            AdnaviDomainContext context,
            AdviserProfileCommonLogic common,
            BatchIntroductionLogic batchIntroductionLogic,
            ModelUtils model,
            EMailService emailService,
            LogDictionary logDictionary,
            IOptions<AdnaviDomainSettings> domainSettings
        )
        {
            _settings = domainSettings.Value.Investors;
            _context = context;
            _common = common;
            _batchIntroductionLogic = batchIntroductionLogic;
            _model = model;
            _emailService = emailService;
            _logDictionary = logDictionary;
            _privacyInformationDeletionSalt = domainSettings
                .Value
                .PrivacyInformationDeletionSalt;
        }

        public async Task<
            List<AdviserConsultationRequestData>
        > RequestConsultation(
            string? searchText,
            uint offset = 0,
            uint count = 20
        )
        {
            var query = _context.AdviserConsultationRequests.AsQueryable();
            if (!string.IsNullOrEmpty(searchText))
            {
                if (Ulid.TryParse(searchText, out var id))
                {
                    query = query.Where(c => c.Id == id);
                }
                else
                {
                    query = query.Where(c => c.Name.Contains(searchText));
                }
            }
            var adviserConsultationRequestList = await query
                .OrderByDescending(x => x.CreatedTime)
                .Skip((int)offset)
                .Take((int)count)
                .Select(
                    x =>
                        new AdviserConsultationRequestData
                        {
                            Id = x.Id,
                            Name = x.Name,
                            Age = x.Age,
                            Prefecture = x.Prefecture,
                            TelephoneNumber = x.TelephoneNumber,
                            MailAddress = x.MailAddress,
                            AssetRange = x.AssetRange,
                            OriginName = x.OriginName,
                            OriginUrl = x.OriginUrl,
                            RemoteHost = x.RemoteHost,
                            ChargeStaff = x.ChargeStaff,
                            AdviserCount = x.AdviserCount,
                            SiteName = x.SiteName,
                            PointStatus = x.PointStatus,
                            PointAmount = x.PointAmount,
                            Probability = x.Probability,
                            StaffRemarks = x.StaffRemarks,
                            CreatedTime = x.CreatedTime,
                            SiteRequestReason = x.SiteRequestReason,
                            InterviewDate = x.InterviewDate,
                            Status = x.Status
                        }
                )
                .ToListAsync();

            return adviserConsultationRequestList;
        }

        public async Task<int> Count(string? searchText)
        {
            var query = _context.AdviserConsultationRequests.AsQueryable();
            if (!string.IsNullOrEmpty(searchText))
            {
                if (Ulid.TryParse(searchText, out var id))
                {
                    query = query.Where(c => c.Id == id);
                }
                else
                {
                    query = query.Where(c => c.Name.Contains(searchText));
                }
            }
            var groupedQuery = query.GroupBy(
                b => new { b.Name, b.MailAddress }
            );
            var countAdviserConsultationRequests =
                await groupedQuery.CountAsync();

            return countAdviserConsultationRequests;
        }

        public async Task<AdviserConsultationRequestData> Get(Ulid id)
        {
            var result = await _model.FindAsync(
                _context.AdviserConsultationRequests,
                id
            );

            var adviserConsultationRequestData =
                new AdviserConsultationRequestData
                {
                    Id = result.Id,
                    Name = result.Name,
                    Age = result.Age,
                    Prefecture = result.Prefecture,
                    TelephoneNumber = result.TelephoneNumber,
                    MailAddress = result.MailAddress,
                    AssetRange = result.AssetRange,
                    OriginName = result.OriginName,
                    OriginUrl = result.OriginUrl,
                    RemoteHost = result.RemoteHost,
                    ChargeStaff = result.ChargeStaff,
                    AdviserCount = result.AdviserCount,
                    SiteName = result.SiteName,
                    PointStatus = result.PointStatus,
                    PointAmount = result.PointAmount,
                    Probability = result.Probability,
                    StaffRemarks = result.StaffRemarks,
                    CreatedTime = result.CreatedTime,
                    SiteRequestReason = result.SiteRequestReason,
                    InterviewDate = result.InterviewDate,
                    CsEvaluationRequestTime = result.CsEvaluationRequestTime,
                    CustomerQuality = result.CustomerQuality,
                };
            return adviserConsultationRequestData;
        }

        public async Task<CSInterviewConsultationResponse> GetForInterview(
            Ulid id
        )
        {
            var result = await _model.FindAsync(
                _context.AdviserConsultationRequests,
                id
            );

            var adviserWorkTypes = result.AdviserWorkTypes
                .Select(x => x.Id)
                .ToList();

            return new CSInterviewConsultationResponse
            {
                Id = result.Id,
                Name = result.Name,
                Age = result.Age,
                TelephoneNumber = result.TelephoneNumber,
                MailAddress = result.MailAddress,
                AssetRange = result.AssetRange,
                Prefecture = result.Prefecture,
                AcceptPrivacy = result.AcceptPrivacy ?? false,
                ContactMethod = result.ContactMethod,
                ChargeStaff = result.ChargeStaff,
                StaffRemarks = result.StaffRemarks,
                InvestorOccupationType = result.InvestorOccupationType,
                AdviserWorkTypes = adviserWorkTypes,
                GenderType = result.Gender,
                FirstContactMethodType = result.FirstContactMethodType
            };
        }

        public async Task UpdateWithInterview(
            Ulid id,
            CSInterviewSearchRequest request,
            string? RegisteredEmail
        )
        {
            var result = await _model.FindAsync(
                _context.AdviserConsultationRequests,
                id
            );
            var adviserWorkTypes = await _context.AdviserWorkTypes
                .Where(x => request.AdviserWorkTypes.Contains(x.Id))
                .ToListAsync();

            result.Name = request.Name;
            result.Age = request.Age;
            result.TelephoneNumber = request.TelephoneNumber;
            result.MailAddress = request.MailAddress;
            result.AssetRange = request.AssetRange;
            result.Prefecture = request.Prefecture;
            result.ContactMethod = request.ContactMethod;
            result.ChargeStaff = request.ChargeStaff;
            result.InvestorOccupationType = request.InvestorOccupationType;
            result.AcceptPrivacy = request.AcceptPrivacy;
            result.AcceptMailMagazine = request.AcceptMailMagazine;
            result.InterviewDate = DateTime.UtcNow;
            result.RegisterEmail = RegisteredEmail;
            result.StaffRemarks = request.StaffRemarks ?? "";
            result.AdviserWorkTypes.Clear();
            result.AdviserWorkTypes = adviserWorkTypes;
            result.Gender = request.GenderType;
            result.FirstContactMethodType = request.FirstContactMethodType;

            await _model.SaveChangesAsync();
        }

        public async Task AddAdvisers(
            Ulid id,
            IEnumerable<Ulid> adviserIds,
            DateTimeOffset? consultationStartTime = null,
            DateTimeOffset? consultationEndTime = null
        )
        {
            await ValidateCSIntroductionSingleOnly(adviserIds);

            var result = await _model.FindAsync(
                _context.AdviserConsultationRequests,
                id
            );

            var advisers = await _context.Advisers
                .Where(x => adviserIds.Contains(x.Id))
                .ToListAsync();

            result.AppointedAdvisers.Clear();

            result.AppointedAdvisers = advisers;
            result.ConsultationStartTime = consultationStartTime;
            result.ConsultationEndTime = consultationEndTime;

            await _model.SaveChangesAsync();
        }

        private async Task ValidateCSIntroductionSingleOnly(
            IEnumerable<Ulid> adviserIds
        )
        {
            var enumerable = adviserIds as Ulid[] ?? adviserIds.ToArray();
            if (!enumerable.Any())
                return;

            var advisersWithCompanies = await _context.Advisers
                .Include(a => a.IfaCompany)
                .Where(a => enumerable.Contains(a.Id))
                .ToListAsync();

            var companiesWithCounts = advisersWithCompanies
                .Where(a => a.IfaCompany != null)
                .GroupBy(a => a.IfaCompany!);

            foreach (var companyGroup in companiesWithCounts)
            {
                if (
                    companyGroup.Key.CSIntroductionSingleOnly
                    && companyGroup.Count() > 1
                )
                {
                    throw new CSIntroductionSingleOnlyError(
                        companyGroup.Key.Name,
                        companyGroup.Count()
                    );
                }
            }
        }

        public async Task<CSSendingInformationResponse> GetSendingInformation(
            Ulid id
        )
        {
            var result = await _model.FindAsync(
                _context.AdviserConsultationRequests,
                id
            );

            var adviserProfiles = new List<AdviserPublicResponse>();

            foreach (var adviser in result.AppointedAdvisers)
            {
                var adviserProfile = new AdviserPublicResponse(
                    adviser,
                    _context,
                    await _common.GetPhotoUrls(adviser),
                    _common.GetPhotoCollectionUrls(adviser)
                );
                adviserProfiles.Add(adviserProfile);
            }

            return new CSSendingInformationResponse
            {
                Id = result.Id,
                Name = result.Name,
                Age = result.Age,
                TelephoneNumber = result.TelephoneNumber,
                MailAddress = result.MailAddress,
                AssetRange = result.AssetRange,
                Prefecture = result.Prefecture,
                ContactMethod = result.ContactMethod,
                ChargeStaff = result.ChargeStaff,
                AppointedAdvisers = adviserProfiles,
                StaffRemarks = result.StaffRemarks
            };
        }

        public async Task RequestBatchIntroduction(
            Ulid id,
            string? staffRemarks,
            List<AdviserIntroductionData> adviserIntroductionData,
            User? registeredUser
        )
        {
            var result = await _model.FindAsync(
                _context.AdviserConsultationRequests,
                id
            );

            // Double-check CSIntroductionSingleOnly constraint before sending
            await ValidateCSIntroductionSingleOnly(
                result.AppointedAdvisers.Select(a => a.Id)
            );

            var contactMethod = await _model.FindAsync(
                _context.ContactMethods,
                result.ContactMethod
            );

            if (!result.AppointedAdvisers.Any())
                throw new AdviserIsNotSelectedError();
            if (result.AppointedAdvisers.Count() > 3)
                throw new AdviserExceededLimitError();

            var entity = _model.Create(
                new BatchIntroduction
                {
                    Status = BatchIntroductionStatus.Request,
                    Age = result.Age,
                    AssetRange = result.AssetRange,
                    ConsultationRequestType = ConsultationRequestTypes.Other,
                    Name = result.Name,
                    Furigana = "",
                    Gender = result.Gender,
                    TelephoneNumber = result.TelephoneNumber ?? "",
                    EMail = result.MailAddress,
                    Prefecture =
                        result.Prefecture ?? throw new InternalException(),
                    PostalCode = "",
                    Address = "",
                    AdviserWorkTypes = result.AdviserWorkTypes,
                    ContactMethods = new List<ContactMethod>()
                    {
                        contactMethod
                    },
                    Remarks = result.Contents ?? "",
                    InvestorOccupationType =
                        result.InvestorOccupationType
                        ?? InvestorOccupationTypes.Other,
                    ConsultationTypes = new List<ConsultationType>(),
                    AdviserPreferences = new List<AdviserPreference>(),
                    AcceptPrivacy = result.AcceptPrivacy ?? false,
                    AcceptMailMagazine = result.AcceptMailMagazine ?? false,
                    Advisers = new List<Adviser>(result.AppointedAdvisers),
                    RemoteHost = result.RemoteHost,
                    OriginUrl = result.OriginUrl,
                    OriginName = "一括紹介(スタッフ)",
                    CreatedTime = DateTime.UtcNow,
                    SendRemindMail = true,
                    StaffContacted = true,
                    RegisterEmail = result.RegisterEmail,
                    SortOption = AdviserSortOptionForBatchIntroduction.None,
                    FavorableAdviserExperiences =
                        new List<FavorableAdviserExperience>(),
                    AsSecondOpinion = false,
                    InvestorRemarks = staffRemarks,
                    FirstContactMethodType = result.FirstContactMethodType,
                    ConsultationStartTime = result.ConsultationStartTime,
                    ConsultationEndTime = result.ConsultationEndTime
                }
            );

            foreach (var adviser in entity.Advisers)
            {
                _model.Create(
                    new AdviserIntroduction
                    {
                        Adviser = adviser,
                        BatchIntroduction = entity,
                        StaffRemarksToAdvisers = adviserIntroductionData
                            .Find(x => x.AdviserId == adviser.Id)
                            ?.StaffRemarksToAdvisers
                    }
                );
            }

            result.Status = ConsultationRequestStatuses.Requested;
            result.AdviserCount = result.AppointedAdvisers.Count();

            await _batchIntroductionLogic.StaffRequest(entity, registeredUser);
            await _model.SaveChangesAsync();
        }

        public async Task Update(Ulid id, AdviserConsultationRequestData data)
        {
            var result = await _model.FindAsync(
                _context.AdviserConsultationRequests,
                id
            );

            result.Name = data.Name;
            result.Age = data.Age;
            result.TelephoneNumber = data.TelephoneNumber;
            result.MailAddress = data.MailAddress;
            result.AssetRange = data.AssetRange;
            result.OriginName = data.OriginName;
            result.OriginUrl = data.OriginUrl;
            result.RemoteHost = data.RemoteHost;
            result.ChargeStaff = data.ChargeStaff;
            result.AdviserCount = data.AdviserCount;
            result.SiteName = data.SiteName;
            result.StaffRemarks = data.StaffRemarks;
            result.PointStatus = data.PointStatus;
            result.PointAmount = data.PointAmount;
            result.Probability = data.Probability;
            result.InterviewDate = data.InterviewDate;
            result.SiteRequestReason = data.SiteRequestReason;
            result.CustomerQuality = data.CustomerQuality;

            await _model.SaveChangesAsync();
        }

        public async Task<MemoryStream> OutputCsv()
        {
            var config = new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                HasHeaderRecord = true
            };

            var dataExport = AdviserConsultationRequestCsvRecord.FromQuery(
                _context,
                _context.AdviserConsultationRequests.AsNoTracking()
            );

            var stream = new MemoryStream();
            using (
                var writeFile = new StreamWriter(
                    stream,
                    Encoding.UTF8,
                    -1,
                    leaveOpen: true
                )
            )
            {
                var csv = new CsvWriter(writeFile, config);
                csv.WriteHeader<AdviserConsultationRequestCsvRecord>();
                await csv.NextRecordAsync();
                await foreach (var record in dataExport)
                {
                    csv.WriteRecord(record);
                    await csv.NextRecordAsync();
                }
            }
            stream.Position = 0; // reset stream

            return stream;
        }

        public async Task SendCsEvaluationRequest(Ulid id)
        {
            var result = await _model.FindAsync(
                _context.AdviserConsultationRequests,
                id
            );
            result.CsEvaluationRequestTime = DateTime.Now;
            await _model.SaveChangesAsync();

            var template = new CsEvaluationRequestMailTemplate(_settings);

            await _emailService.TrySendMail(
                template.GetSubject(),
                template.GetFromMailAddress(),
                result.MailAddress,
                template.GetContents(),
                result.OriginUrl
            );
        }

        /// <summary>
        /// nameとeMailの組み合わせでAdviserConsultationRequestの個人情報を検索する。
        /// </summary>
        /// <param name="keyword"></param>
        private IQueryable<RequestKeyResponse> SearchQuery(string? keyword)
        {
            IQueryable<AdviserConsultationRequest> query =
                _context.AdviserConsultationRequests.AsNoTracking();
            if (!string.IsNullOrWhiteSpace(keyword))
            {
                query = query.Where(
                    b =>
                        b.Name.Contains(keyword)
                        || b.MailAddress.Contains(keyword)
                );
            }

            var groupedQuery = query
                .GroupBy(b => new { b.Name, b.MailAddress })
                .Select(
                    g =>
                        new RequestKeyResponse
                        {
                            Name = g.Key.Name,
                            MailAddress = g.Key.MailAddress,
                            // Assumes CreatedTime is the introduction time.
                            FirstIntroductionTime = g.Min(b => b.CreatedTime)
                        }
                )
                .OrderByDescending(b => b.FirstIntroductionTime);
            return groupedQuery;
        }

        /// <summary>
        /// 検索された個人情報を一覧で取得する。
        /// </summary>
        /// <param name="request"></param>
        public async Task<IEnumerable<RequestKeyResponse>> SearchKeys(
            RequestKeyRequest request
        )
        {
            return await SearchQuery(request.Keyword)
                .Skip((int)request.Offset)
                .Take((int)request.Count)
                .ToListAsync();
        }

        /// <summary>
        /// 名前とメールアドレスが両方が一致する全てのAdviserConsultationRequestの個人情報を削除する。
        /// </summary>
        /// <param name="name"></param>
        /// <param name="eMail"></param>
        public async Task DeletePrivacyInformation(string name, string eMail)
        {
            var adviserConsultationRequests =
                await _context.AdviserConsultationRequests
                    .Where(b => b.Name == name && b.MailAddress == eMail)
                    .ToListAsync();

            foreach (
                var adviserConsultationRequest in adviserConsultationRequests
            )
            {
                _logDictionary.DeletePrivacyInformation(name, eMail);
                adviserConsultationRequest.DeleteRequestPrivacyInformation(
                    _privacyInformationDeletionSalt
                );
            }
            await _model.SaveChangesAsync();
        }
    }
}
