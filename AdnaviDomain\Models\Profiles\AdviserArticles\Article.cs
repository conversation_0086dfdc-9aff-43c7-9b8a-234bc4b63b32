﻿using System.ComponentModel.DataAnnotations;
using Adnavi.Domain.Models.Profiles.AdviserProfiles;
using Adnavi.Utils.Models;
using NUlid;

namespace Adnavi.Domain.Models.Profiles.AdviserArticles;

public class Article : IHasId
{
    /// <summary>ID</summary>
    [Required]
    public Ulid Id { get; set; }

    /// <summary>アドバイザーID</summary>
    [Required]
    public Ulid AdviserId { get; set; }

    /// <summary>アドバイザー</summary>
    [Required]
    public virtual Adviser Adviser { get; set; }

    /// <summary>タイトル</summary>
    [Required]
    public string Title { get; set; }

    /// <summary>本文</summary>
    [Required]
    public string Text { get; set; }

    /// <summary>下書きタイトル</summary>
    [Required]
    public string DraftTitle { get; set; }

    /// <summary>下書き</summary>
    [Required]
    public string Draft { get; set; }

    /// <summary>初回公開日</summary>
    public DateTime? FirstPublishedDate { get; set; }

    /// <summary>更新日</summary>
    public DateTime? LastPublishedDate { get; set; }

    /// <summary>カテゴリー</summary>
    [Required]
    public virtual ICollection<ArticleCategory> Categories { get; set; }

    /// <summary>イメージ</summary>
    [Required]
    public virtual ICollection<ArticleImage> Images { get; set; }

    /// <summary>トップイメージキー</summary>
    public string? TopImagePrefix { get; set; }

    /// <summary>作成時間</summary>
    [Required]
    public DateTime CreatedTime { get; set; }

    /// <summary>修正時間</summary>
    [Required]
    public DateTime ModifiedTime { get; set; }
}
