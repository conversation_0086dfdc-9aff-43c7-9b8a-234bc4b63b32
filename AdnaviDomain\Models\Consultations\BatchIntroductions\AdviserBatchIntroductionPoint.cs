﻿using Adnavi.Domain.Models.Profiles.AdviserProfiles;
using Adnavi.Utils.Models;
using NUlid;
using Microsoft.EntityFrameworkCore;

namespace Adnavi.Domain.Models.Consultations.BatchIntroductions;

[Index(nameof(AdviserId), nameof(BatchIntroductionId), IsUnique = true)]
public class AdviserBatchIntroductionPoint : IHasId
{
    public Ulid Id { get; set; }
    public virtual Adviser Adviser { get; set; }
    public Ulid AdviserId { get; set; }
    public virtual BatchIntroduction BatchIntroduction { get; set; }
    public Ulid BatchIntroductionId { get; set; }

    public double PredictedSatisfactionScore { get; set; }
    public double WorkTypesCoverScore { get; set; }
}
