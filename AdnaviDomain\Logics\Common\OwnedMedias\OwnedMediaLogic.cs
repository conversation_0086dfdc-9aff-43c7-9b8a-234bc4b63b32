using Adnavi.Domain.DTOs.Common.OwnedMedias;
using Microsoft.EntityFrameworkCore;

namespace Adnavi.Domain.Logics.Common.OwnedMedias;

public class OwnedMediaLogic
{
    private readonly AdnaviDomainContext _context;

    public OwnedMediaLogic(AdnaviDomainContext context)
    {
        _context = context;
    }

    public IEnumerable<OwnedColumnSummaryData> GetOwnedColumnData(
        DateOnly? start,
        DateOnly? end
    )
    {
        var query = _context.OwnedColumns
            .Include(a => a.TransitionStatistics)
            .Where(a => a.TransitionStatistics.Any())
            .AsQueryable();

        return OwnedColumnSummaryData.FromQuery(query, start, end);
    }
}
