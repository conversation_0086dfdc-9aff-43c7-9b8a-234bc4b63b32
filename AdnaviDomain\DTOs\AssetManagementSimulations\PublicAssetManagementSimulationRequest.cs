using Adnavi.Domain.Logics.AssetManagementSimulations;
using Adnavi.Utils.Models;

namespace Adnavi.Domain.DTOs.AssetManagementSimulations;

public class PublicAssetManagementSimulationRequest : IDataTransferObject
{
    /// 基本情報
    /// <summary> 年齢 </summary>
    public int Age { get; set; }

    /// 資産状況
    /// <summary> 現金保有額 </summary>
    public long InitialCashAmount { get; set; } = 0;

    /// <summary> 日本株式保有額 </summary>
    public long InitialJapaneseStockAmount { get; set; } = 0;

    /// <summary> アメリカ株式保有額 </summary>
    public long InitialUsStockAmount { get; set; } = 0;

    /// <summary> その他株式保有額 </summary>
    public long InitialOtherStockAmount { get; set; } = 0;

    /// <summary> 債券保有額 </summary>
    public long InitialBondAmount { get; set; } = 0;

    /// <summary> 保険保有額 </summary>
    public long InitialInsuranceAmount { get; set; } = 0;

    /// <summary> その他金融資産保有額 </summary>
    public long InitialOtherFinancialAssetAmount { get; set; } = 0;

    /// 目標金融資産
    /// <summary> 目標金融資産額 </summary>
    public long TargetFinancialAssetAmount { get; set; }

    /// <summary> 目標達成時の年齢 </summary>
    public int TargetAge { get; set; }

    /// 投資性向
    public RiskTolerances RiskTolerance { get; set; }
}
