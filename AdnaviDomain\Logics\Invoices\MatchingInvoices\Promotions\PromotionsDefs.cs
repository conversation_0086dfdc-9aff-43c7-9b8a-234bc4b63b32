using Adnavi.Domain.Common;

namespace Adnavi.Domain.Logics.Invoices.MatchingInvoices.Promotions;

public class PromotionsDefs : IDefinitionExport
{
    public string Name => GetType().Name.Replace("Defs", "");

    public IEnumerable<string> DefinitionNames =>
        new List<string> { $"dict/ja-jp/{Name}", $"list/{Name}" };

    public bool DictMatched(string name) => name == $"dict/ja-jp/{Name}";

    public bool ListMatched(string name) => name == $"list/{Name}";

    public object? GetDict()
    {
        var dict = new Dictionary<string, string>
        {
            { nameof(InitialRegistrationPromotion), "初回登録キャンペーン" },
        };

        return dict;
    }

    public object? GetList()
    {
        var list = new List<string> { nameof(InitialRegistrationPromotion), };
        return list;
    }
}
