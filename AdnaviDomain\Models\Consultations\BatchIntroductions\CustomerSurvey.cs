using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Adnavi.Domain.Models.Consultations.InsuranceBatchIntroductions;
using Adnavi.Domain.Models.Profiles;
using Adnavi.Domain.Models.Profiles.PersonalityTraits;
using Adnavi.Utils.Models;
using NUlid;

namespace Adnavi.Domain.Models.Consultations.BatchIntroductions
{
    /// <summary>
    /// CustomerSurvey
    /// /// </summary>
    /// <seealso cref="CustomerSurveyRequest" />
    public class CustomerSurvey : IHasId
    {
        [Required]
        public Ulid Id { get; set; }
        public string? Name { get; set; }
        public int? Age { get; set; }
        public OriginServices OriginServices { get; set; }
        public string? CampaignName { get; set; }
        public string? CampaignCode { get; set; }
        public int MatchingSatisfaction { get; set; }
        public string MatchingSatisfactionReason { get; set; }
        public int SiteSatisfaction { get; set; }
        public string SiteSatisfactionReason { get; set; }
        public Ulid? BatchIntroductionId { get; set; }
        public virtual BatchIntroduction? BatchIntroduction { get; set; }
        public Ulid? InsuranceBatchIntroductionId { get; set; }
        public virtual InsuranceBatchIntroduction? InsuranceBatchIntroduction { get; set; }
        public virtual ICollection<CustomerEvaluation>? CustomerEvaluation { get; set; }
        public virtual ICollection<CustomerBranchEvaluation>? CustomerBranchEvaluation { get; set; }

        public virtual PersonalitySurvey? PersonalitySurvey { get; set; }

        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public DateTime CreatedTime { get; set; }
    }
}
