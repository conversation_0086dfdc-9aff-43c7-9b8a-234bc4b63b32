using Adnavi.Domain.Common;

namespace Adnavi.Domain.Models.Invoices.MatchingInvoices;

public class InvoiceStatusesDefs : IDefinitionExport
{
    public string Name => GetType().Name.Replace("Defs", "");

    public IEnumerable<string> DefinitionNames =>
        new List<string> { $"dict/ja-jp/{Name}", $"list/{Name}" };

    public bool DictMatched(string name) => name == $"dict/ja-jp/{Name}";

    public bool ListMatched(string name) => name == $"list/{Name}";

    public object? GetDict()
    {
        var dict = new Dictionary<string, string>
        {
            { nameof(InvoiceStatuses.Hold), "保留" },
            { nameof(InvoiceStatuses.Approved), "確定" },
            { nameof(InvoiceStatuses.Canceled), "取消" },
        };

        return dict;
    }

    public object? GetList()
    {
        var list = new List<string>
        {
            nameof(InvoiceStatuses.Hold),
            nameof(InvoiceStatuses.Approved),
            nameof(InvoiceStatuses.Canceled),
        };
        return list;
    }
}
