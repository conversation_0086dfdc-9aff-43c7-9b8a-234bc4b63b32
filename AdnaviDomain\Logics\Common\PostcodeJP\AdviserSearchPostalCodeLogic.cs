using Adnavi.Domain.Common.PostcodeJP;
using Adnavi.Domain.DTOs.Common.PostalCodeJP.PostalCodeInformation;
using Newtonsoft.Json;

namespace Adnavi.Domain.Logics.Common.PostcodeJP;

public class AdviserSearchPostalCodeLogic
{
    private readonly AdnaviDomainContext _context;
    private string town;

    private object pref;

    public AdviserSearchPostalCodeLogic(AdnaviDomainContext context)
    {
        _context = context;
    }

    public async Task<PostalCodeInformationData> SearchPostalCode(
        string postCode,
        string PostcodeJPAPIKey
    )
    {
        var PostalCodeData = new PostalCodeInformationData();

        string baseUrl = "https://apis.postcode-jp.com/api/v5/postcodes/";

        string url = $"{baseUrl}{postCode}?apiKey={PostcodeJPAPIKey}";

        var client = new HttpClient();

        var response = await client.GetAsync(url);

        var responseJson = await response.Content.ReadAsStringAsync();

        var result =
            JsonConvert.DeserializeObject<PostcodeJPPostcodeResponse[]>(
                responseJson
            );

        if (result != null)
        {
            town = result[0].City + result[0].Town;

            pref = _context.Prefectures
                .Where(p => p.DisplayName == result[0].Pref)
                .Select(p => p.Id)
                .FirstOrDefault();
        }

        PostalCodeData.Town = town ?? "";

        PostalCodeData.Prefecture = pref.ToString() ?? "";

        return PostalCodeData;
    }
}
