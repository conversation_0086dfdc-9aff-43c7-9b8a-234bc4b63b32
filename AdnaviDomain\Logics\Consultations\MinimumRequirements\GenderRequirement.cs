using Adnavi.Domain.Logics.Consultations.BatchIntroductions;
using Adnavi.Domain.Models.Commons;

namespace Adnavi.Domain.Logics.Consultations.MinimumRequirements;

public class GenderRequirement : IRequirement
{
    private readonly GenderTypes _gender;

    public GenderRequirement(GenderTypes gender)
    {
        _gender = gender;
    }

    public bool CheckAdviser(AdviserRequirementsCheck data)
    {
        if (data.Gender == null)
            return false;
        if (_gender == data.Gender)
            return true;
        return false;
    }
}
