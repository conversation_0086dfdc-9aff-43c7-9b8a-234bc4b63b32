﻿using Azure.Core;

namespace Adnavi.Domain.Logics.Profiles.AdviserProfiles;

/// <summary>
/// Azure.Core.TokenCredential を継承し、アクセストークンを直接返すカスタムクラス
/// </summary>
public class AccessTokenCredential : TokenCredential
{
    private readonly string _accessToken;

    public AccessTokenCredential(string accessToken)
    {
        _accessToken = accessToken;
    }

    public override AccessToken GetToken(
        TokenRequestContext requestContext,
        CancellationToken cancellationToken
    )
    {
        return new AccessToken(_accessToken, DateTimeOffset.UtcNow.AddHours(1));
    }

    public override ValueTask<AccessToken> GetTokenAsync(
        TokenRequestContext requestContext,
        CancellationToken cancellationToken
    )
    {
        return new ValueTask<AccessToken>(
            new AccessToken(_accessToken, DateTimeOffset.UtcNow.AddHours(1))
        );
    }
}
