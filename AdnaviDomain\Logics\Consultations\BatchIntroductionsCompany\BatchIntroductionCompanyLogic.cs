using Adnavi.Domain.DTOs.Consultations.BatchIntroductionsCompany;
using Adnavi.Domain.Logics.Common.EMails;
using Adnavi.Domain.Logics.Profiles.IfaCompanyProfiles;
using Adnavi.Domain.Models;
using Adnavi.Domain.Models.Consultations.BatchIntroductions;
using Adnavi.Domain.Models.Consultations.BatchIntroductionsCompany;
using Adnavi.Domain.Models.Profiles.AdviserProfiles;
using Adnavi.Domain.Models.Profiles.IfaCompanyProfiles;
using Adnavi.Utils;
using Adnavi.Utils.Exceptions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using MimeKit;
using NUlid;

namespace Adnavi.Domain.Logics.Consultations.BatchIntroductionsCompany;

public class BatchIntroductionCompanyLogic
{
    private readonly AdnaviDomainContext _context;
    private readonly ModelUtils _model;
    private readonly AdnaviDomainSettings _settings;
    private readonly CommonMatchingLogic _commonMatchingLogic;
    private readonly bool _redirectAdviserTestMail = false;
    private readonly EMailService _emailService;
    private readonly ChatWorkUtils _chatWork;
    private readonly IfaCompanyLogic _ifaCompanyLogic;
    private readonly IfaCompanyEvaluationLogic _evaluationLogic;

    public BatchIntroductionCompanyLogic(
        IOptions<AdnaviDomainSettings> settings,
        ChatWorkUtils chatWork,
        ModelUtils model,
        CommonMatchingLogic commonMatchingLogic,
        EMailService emailService,
        AdnaviDomainContext context,
        IfaCompanyLogic ifaCompanyLogic,
        IfaCompanyEvaluationLogic evaluationLogic
    )
    {
        _context = context;
        _model = model;
        _chatWork = chatWork;
        _emailService = emailService;
        _commonMatchingLogic = commonMatchingLogic;
        _settings = settings.Value;
        _emailService = emailService;
        _ifaCompanyLogic = ifaCompanyLogic;
        _evaluationLogic = evaluationLogic;
    }

    public async Task<PublicBatchIntroductionCompanyResponse> GetBatchIntroductionCompanyByBusinessNumber(
        ulong businessNumber
    )
    {
        var companyId = await _context.IfaCompanies
            .Where(c => c.BusinessNumber == businessNumber)
            .Select(c => c.Id)
            .FirstOrDefaultAsync();
        return await GetBatchIntroductionCompany(companyId);
    }

    public async Task<PublicBatchIntroductionCompanyResponse> GetBatchIntroductionCompany(
        Ulid id
    )
    {
        var entity = await _model.SingleAsync(
            QueryAvailableCompanies()
                .AsNoTracking()
                .Include(c => c.BasePrefectures)
                .Include(c => c.CooperativeSecurities)
                .Include(c => c.ContractTypes)
                .Include(c => c.FormerEmployers)
                .Include(c => c.Licenses)
                .Include(c => c.BatchIntroductionIfaCompanySetting)
                .ThenInclude(s => s == null ? null : s.AcceptAssetRanges)
                .Where(c => c.Id == id)
        );

        var result = new PublicBatchIntroductionCompanyResponse(
            entity,
            _ifaCompanyLogic.GetPhotoUrls(entity),
            // TODO: ここで評価情報を取得する
            null, //evaluationAverages.GetValueOrDefault(c.Id)?.Average,
            null //adviserEvaluations.GetValueOrDefault(c.Id)?.Items
        );

        return result;
    }

    public async Task<BatchIntroductionCompanySearchResponse> SearchForBatchIntroductionCompany(
        BatchIntroductionCompanyRequest data,
        string remoteHost
    )
    {
        const int maxCompanyEvaluations = 3;

        var entity = await GenerateBatchIntroductionForIntroduction(
            data,
            remoteHost
        );

        await _context.SaveChangesAsync();

        // 許可された投資家ならば社内に通知を送る
        if (
            IsValidAndNotStaffEMail(entity.EMail)
            && IsSentNotMailRecently(
                entity.EMail,
                entity.CreatedTime,
                entity.Id
            )
        )
            await SendNotificationOnSearch(entity);

        var ifaCompanyIds = entity.IfaCompanies.Select(c => c.Id);

        var adviserEvaluations = _evaluationLogic
            .GetCompanyAdviserEvaluationsQuery(
                ifaCompanyIds,
                maxCompanyEvaluations
            )
            .AsNoTracking()
            .Select(e => new { e.IfaCompanyId, e.Items })
            .ToDictionary(e => e.IfaCompanyId ?? Ulid.Empty, e => e);

        var evaluationAverages = _evaluationLogic
            .GetCompanyAdviserEvaluationAverageQuery(ifaCompanyIds)
            .AsNoTracking()
            .ToDictionary(e => e.IfaCompanyId ?? Ulid.Empty, e => e);

        var result = new BatchIntroductionCompanySearchResponse(
            entity.Id,
            entity.IfaCompanies.Select(
                c =>
                    new PublicBatchIntroductionCompanyResponse(
                        c,
                        _ifaCompanyLogic.GetPhotoUrls(c),
                        evaluationAverages.GetValueOrDefault(c.Id)?.Average,
                        adviserEvaluations.GetValueOrDefault(c.Id)?.Items
                    )
            )
        );
        return result;
    }

    public async Task<Ulid> RequestBatchIntroduction(
        BatchIntroductionCompanyRequestRequest data,
        bool isStaff,
        string remoteHost
    )
    {
        // _context.ChangeTracker.LazyLoadingEnabled = false;

        var possibleCompanyIdList = (
            await QueryCompanies(data.Requester, isStaff)
        ).Select(c => c.Id);
        data.CompanyIds = data.CompanyIds
            .Intersect(possibleCompanyIdList)
            .ToList();

        if (data.CompanyIds.Count == 0)
            throw new CompanyIsNotSelectedError();
        if (data.CompanyIds.Count > 3)
            throw new CompanyExceededLimitError();

        var entity = _model.Create(
            data.To(
                new BatchIntroductionCompany(),
                _context,
                remoteHost,
                BatchIntroductionStatus.Request,
                data.IsTestMode ?? false
            )
        );

        CheckValidInvestor(entity);

        await SendMail(entity, data.IsTestMode ?? false);

        await _context.SaveChangesAsync();
        return entity.Id;
    }

    private bool IsSentNotMailRecently(
        string EMail,
        DateTime CreatedTime,
        Ulid Id
    )
    {
        var isExist = _context.BatchIntroductions
            .Where(
                b =>
                    b.EMail == EMail
                    && b.Id != Id
                    && b.CreatedTime > CreatedTime.AddDays(-1)
            )
            .Any();
        return !isExist;
    }

    private bool IsValidAndNotStaffEMail(string mail)
    {
        if (mail == "")
            return false;

        if (mail.EndsWith("@" + _settings.StaffMailDomain))
            return false;

        return true;
    }

    private async Task<BatchIntroductionCompany> GenerateBatchIntroductionForIntroduction(
        BatchIntroductionCompanyRequest data,
        string remoteHost
    )
    {
        var entity = _model.Create(
            data.To(
                new BatchIntroductionCompany(),
                _context,
                remoteHost,
                BatchIntroductionStatus.Introduction
            )
        );

        var query = await QueryCompanies(data, false);
        query = query.OrderBy(a => EF.Functions.Random());

        //ここで紹介した法人の情報を保存する CompanyIntroduction
        entity.IfaCompanies = query.ToArray();

        return entity;
    }

    private IQueryable<IfaCompany> QueryAvailableCompanies() =>
        // 法人一括紹介を有効にしていてメールが登録されている法人を取得
        _context.IfaCompanies.Where(
            c =>
                c.EnableBatchIntroductionCompany
                && c.NotificationEmail != ""
                && c.NotificationEmail != null
        );

    private async Task<IQueryable<IfaCompany>> QueryCompanies(
        BatchIntroductionCompanyRequest request,
        bool staffIntroduction
    )
    {
        var query = QueryAvailableCompanies();

        if (request.TargetCompanies != null && request.TargetCompanies.Any())
            query = query.Where(c => request.TargetCompanies.Contains(c.Id));

        // スタッフ紹介ではない通常の検索の場合
        if (!staffIntroduction && request.ParentIntroductionId != null)
        {
            // 連続で検索した場合は前回の結果を取得する。
            var parentIntroduction = await _model.SingleAsync(
                _context.BatchIntroductionCompanies.Where(
                    i => i.Id == request.ParentIntroductionId
                )
            );
            query = query.Where(
                c => parentIntroduction.IfaCompanies.Contains(c)
            );

            //同一人物からの同じ法人への問い合わせを除外
            var requestedCompanyIdList = _context.BatchIntroductionCompanies
                .Where(
                    b =>
                        (
                            request.AdnParentId != null
                            && b.AdnParentId == request.AdnParentId
                        )
                        || b.EMail == request.EMail
                )
                .SelectMany(b => b.IfaCompanies.Select(c => c.Id))
                .Distinct();

            query = query.Where(c => !requestedCompanyIdList.Contains(c.Id));
        }

        // 申し込み都道府県で絞り込み
        if (
            request.ContactMethods.Contains(ContactMethods.Visit)
            || request.ContactMethods.Contains(ContactMethods.Office)
        )
            query = query.Where(
                c =>
                    c.VisitPrefectures
                        .Select(p => p.Prefectures)
                        .Contains(request.Prefecture)
            );

        if (request.ContactMethods.Contains(ContactMethods.WebMeeting))
            query = query.Where(
                c =>
                    c.WebMeetingPrefectures
                        .Select(p => p.Prefectures)
                        .Contains(request.Prefecture)
            );

        return query;
    }

    private async Task SendNotificationOnSearch(BatchIntroductionCompany data)
    {
        var template = new BatchIntroductionCompanyMailTemplate(
            data,
            _context,
            _settings
        );
        await _chatWork.TrySend(
            _settings.Investors.ChatWorkRoomId,
            template.GetContentsForNotificationOnSearch()
        );
    }

    private async Task SendMail(BatchIntroductionCompany data, bool isTestMode)
    {
        var template = new BatchIntroductionCompanyMailTemplate(
            data,
            _context,
            _settings
        );
        var mailFrom = new MailboxAddress(
            _settings.Investors.ConsultationRequestMail.FromName,
            _settings.Investors.ConsultationRequestMail.FromAddress
        );
        var staffNotificationMails = _settings
            .Investors
            .ConsultationRequestMail
            .NotificationMailAddresses;
        var errorCompanies = new List<IfaCompany>();
        var error = false;

        foreach (var company in data.IfaCompanies)
        {
            var companyEmails = StringUtils.SplitCsvColum(
                company.NotificationEmail ?? ""
            );

            bool mailError = true;

            foreach (var companyEmail in companyEmails)
            {
                var subject = isTestMode
                    ? "テストモード " + template.GetCompanySubject()
                    : template.GetCompanySubject();
                var sendToEmail = isTestMode
                    ? _settings.Investors.AdviserTestMailAddress
                    : companyEmail;

                bool success = await _emailService.TrySendMail(
                    subject,
                    mailFrom,
                    sendToEmail,
                    template.GetContentsForCompany(company),
                    data.OriginUrl
                );
                if (success)
                    mailError = false;
            }

            //　一件もメールが送れない場合はエラー
            if (mailError)
                errorCompanies.Add(company);
        }

        var notificationContents = template.GetContentsForNotification(
            errorCompanies,
            _settings.WatashiIfaUrl
        );

        foreach (var mail in staffNotificationMails)
        {
            var subject = isTestMode
                ? "テストモード " + template.GetNotificationSubject(data.OriginName)
                : template.GetNotificationSubject(data.OriginName);

            error =
                !await _emailService.TrySendMail(
                    subject,
                    mailFrom,
                    mail,
                    notificationContents,
                    data.OriginUrl
                ) || error;
            ;
        }

        // Send mail to the investor. (ignore error)
        var investorSubject = isTestMode
            ? "テストモード " + template.GetInvestorSubject()
            : template.GetInvestorSubject();

        await _emailService.TrySendMail(
            investorSubject,
            mailFrom,
            data.EMail,
            template.GetContentsForInvestor(errorCompanies),
            data.OriginUrl
        );

        if (error)
            throw new NotificationMailError(data.OriginUrl, data.EMail);

        data.SendRemindMail = true;
        _context.SaveChanges();
    }

    private string GetSendToEmail(string email) =>
        _redirectAdviserTestMail
            ? _settings.Investors.AdviserTestMailAddress
            : email;

    private void CheckValidInvestor(BatchIntroductionCompany entity) =>
        _commonMatchingLogic.CheckValidInvestor(entity);

    public IEnumerable<BatchIntroductionCompanyCsvData> GetCsvData()
    {
        var query = _context.BatchIntroductionCompanies
            .Where(b => b.CreatedTime > DateTime.UtcNow.AddMonths(-1))
            .AsNoTracking()
            .Include(b => b.IfaCompanies)
            .ThenInclude(
                c => c == null ? null : c.BatchIntroductionIfaCompanySetting
            )
            .Include(b => b.AdviserWorkTypes)
            .Include(b => b.ConsultationTypes)
            .Include(b => b.ContactMethods);

        foreach (var b in query.ToArray())
        {
            var record = new BatchIntroductionCompanyCsvData
            {
                Id = b.Id.ToString(),
                Status = b.Status,
                Age = b.Age,
                AssetRange = b.AssetRange.ToString(),
                ConsultationRequestType = b.ConsultationRequestType,
                Name = b.Name,
                Furigana = b.Furigana,
                Gender = b.Gender,
                TelephoneNumber = b.TelephoneNumber,
                EMail = b.EMail,
                Prefecture = b.Prefecture,
                PostalCode = b.PostalCode,
                Address = b.Address,
                AdviserWorkTypes = string.Join(
                    ";",
                    b.AdviserWorkTypes.Select(w => w.DisplayName)
                ),
                InvestmentPurpose = b.InvestmentPurpose,
                ContactMethods = string.Join(
                    ";",
                    b.ContactMethods.Select(c => c.DisplayName)
                ),
                Remarks = b.Remarks,
                AnnualIncome = b.AnnualIncome,
                InvestorOccupationType = ModelUtils.GetDisplayName(
                    _context.InvestorOccupationTypes,
                    b.InvestorOccupationType
                ),
                ConsultationTypes = string.Join(
                    ";",
                    b.ConsultationTypes.Select(c => c.DisplayName)
                ),
                AcceptPrivacy = b.AcceptPrivacy,
                AcceptMailMagazine = b.AcceptMailMagazine,
                Companies = string.Join(
                    ";",
                    b.IfaCompanies.Select(a => $"{a.Name}")
                ),
                RemoteHost = b.RemoteHost,
                OriginUrl = b.OriginUrl,
                OriginName = b.OriginName,
                CreatedTime = b.CreatedTime.ToString("yyyy-MM-dd HH:mm:ss"),
                CompaniesCount = b.IfaCompanies.Count,
                // Advsn = urlQuery.Get("advsn"),
                // UtmSource = urlQuery.Get("utm_source"),
                // UtmMedium = urlQuery.Get("utm_medium"),
                // UtmCampaign = urlQuery.Get("utm_campaign"),
                // UtmContent = urlQuery.Get("utm_content"),
                // UtmTerm = urlQuery.Get("utm_term"),
                RegisterEmail = b.RegisterEmail
            };
            foreach (var a in b.IfaCompanies)
            {
                if (string.IsNullOrEmpty(record.Company0Name))
                {
                    record.Company0Name = $"{a.Name}";
                }
                else if (string.IsNullOrEmpty(record.Company1Name))
                {
                    record.Company1Name = $"{a.Name}";
                }
                else if (string.IsNullOrEmpty(record.Company2Name))
                {
                    record.Company2Name = $"{a.Name}";
                }
                else if (string.IsNullOrEmpty(record.Company3Name))
                {
                    record.Company3Name = $"{a.Name}";
                }
            }
            yield return record;
        }
    }
}
