using Adnavi.Domain.Models.Commons;
using Adnavi.Domain.Models.Consultations.BatchIntroductions;
using Adnavi.Domain.Models.Consultations.ConsultationRequests;
using Adnavi.Domain.Models.Profiles.InvestorProfiles;
using CsvHelper.Configuration.Attributes;

namespace Adnavi.Domain.Logics.Consultations.BatchIntroductionsCompany;

public class BatchIntroductionCompanyCsvData
{
    [Name("#")]
    public string Id { get; set; }

    [Name("リード獲得日")]
    public string CreatedTime { get; set; }

    [Name("氏名")]
    public string Name { get; set; }

    [Name("年齢")]
    public int Age { get; set; }

    [Name("年収")]
    public AnnualIncomes? AnnualIncome { get; set; }

    [Name("電話番号")]
    public string TelephoneNumber { get; set; }

    [Name("メールアドレス")]
    public string EMail { get; set; }

    [Name("区分")]
    public string OriginName { get; set; }

    [Name("流入詳細（記入：URL貼り付け）")]
    public string OriginUrl { get; set; }
    public string? Advsn { get; set; }
    public string? UtmSource { get; set; }
    public string? UtmMedium { get; set; }
    public string? UtmCampaign { get; set; }
    public string? UtmContent { get; set; }

    [Name("IFA会社申し込み")]
    public BatchIntroductionStatus Status { get; set; }

    [Name("金融資産範囲")]
    public string AssetRange { get; set; }
    public ConsultationRequestTypes ConsultationRequestType { get; set; }
    public string Furigana { get; set; }
    public GenderTypes? Gender { get; set; }
    public Prefectures Prefecture { get; set; }
    public string PostalCode { get; set; }
    public string Address { get; set; }
    public string AdviserWorkTypes { get; set; }
    public InvestmentPurposes? InvestmentPurpose { get; set; }
    public string ContactMethods { get; set; }
    public string Remarks { get; set; }

    [Name("職業")]
    public string InvestorOccupationType { get; set; }

    public string ConsultationTypes { get; set; }
    public bool AcceptPrivacy { get; set; }
    public bool AcceptMailMagazine { get; set; }
    public string Companies { get; set; }
    public string RemoteHost { get; set; }

    [Name("申し込み数")]
    public int CompaniesCount { get; set; }

    [Name("送客会社名①")]
    public string Company0Name { get; set; }

    [Name("送客会社名②")]
    public string Company1Name { get; set; }

    [Name("送客会社名③")]
    public string Company2Name { get; set; }

    [Name("送客会社名④")]
    public string Company3Name { get; set; }

    public string? UtmTerm { get; set; }

    public string? RegisterEmail { get; set; }
}
