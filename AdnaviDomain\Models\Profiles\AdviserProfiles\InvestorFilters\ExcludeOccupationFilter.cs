using Adnavi.Domain.Models.Consultations.BatchIntroductions;
using Adnavi.Domain.Models.Profiles.InvestorProfiles;

namespace Adnavi.Domain.Models.Profiles.AdviserProfiles.InvestorFilters;

public class ExcludeOccupationsFilter : IInvestorFilter
{
    private readonly IEnumerable<InvestorOccupationTypes> _excludeOccupationTypes;

    public ExcludeOccupationsFilter(
        IEnumerable<InvestorOccupationTypes> excludeOccupationTypes
    )
    {
        _excludeOccupationTypes = excludeOccupationTypes;
    }

    public bool IsMatched(IInvestor investor) =>
        !_excludeOccupationTypes.Contains(investor.InvestorOccupationType);
}
