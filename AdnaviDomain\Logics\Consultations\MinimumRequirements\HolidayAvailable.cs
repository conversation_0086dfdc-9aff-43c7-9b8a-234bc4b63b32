using Adnavi.Domain.Logics.Consultations.BatchIntroductions;
using Adnavi.Domain.Models.Commons;

namespace Adnavi.Domain.Logics.Consultations.MinimumRequirements;

public class HolidayAvailable : IRequirement
{
    public bool CheckAdviser(AdviserRequirementsCheck data)
    {
        if (data == null || data.WorkDays == null)
            return false;
        foreach (var day in data.WorkDays)
        {
            if (
                day.Id == Days.Saturday
                || day.Id == Days.Sunday
                || day.Id == Days.PublicHoliday
            )
                return true;
        }
        return false;
    }
}
