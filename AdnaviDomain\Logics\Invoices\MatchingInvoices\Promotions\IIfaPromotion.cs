using Adnavi.Domain.Models.Consultations.BatchIntroductions;
using Adnavi.Domain.Models.Profiles.AdviserProfiles;

namespace Adnavi.Domain.Logics.Invoices.MatchingInvoices.Promotions;

public interface IIfaPromotion : IPromotion
{
    /// <summary>
    /// プロモーションの対象か確認する
    /// </summary>
    /// <param name="adviser">対象のアドバイザー</param>
    /// <param name="introduction">紹介情報</param>
    /// <returns>対象者であればtrue</returns>
    /// ///
    public bool IsEligible(Adviser adviser, BatchIntroduction introduction);

    /// <summary>
    /// 送客金額の取得
    /// </summary>
    public int? GetMatchingFee(Adviser adviser, BatchIntroduction introduction);

    /// <summary>
    /// 面談金額の取得
    /// </summary>
    public int? GetInterviewFee(
        Adviser adviser,
        BatchIntroduction introduction
    );
}
