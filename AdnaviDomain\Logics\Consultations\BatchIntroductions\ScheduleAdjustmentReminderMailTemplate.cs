using Adnavi.Domain.Models.Consultations.BatchIntroductions;

namespace Adnavi.Domain.Logics.Consultations.BatchIntroductions;

public class ScheduleAdjustmentReminderMailTemplate
{
    public ScheduleAdjustmentReminderMailTemplate() { }

    public string GetSubject() => "御礼/ご確認";

    public string GetNotificationSubject(bool success) =>
        $"【サービス】投資家へメール送信: 御礼/ご確認" + (success ? "（成功）" : "（失敗）");

    public string GetReminderSubject() => "面談予約のリマインダー";

    public string GetReminderNotificationSubject(bool success) =>
        $"【サービス】投資家へリマインダーメール送信" + (success ? "（成功）" : "（失敗）");

    public string GetReminderContent(BatchIntroduction data)
    {
        var consultationTime = data.ConsultationStartTime.HasValue
            ? Adnavi.Utils.DateUtils
                .UtcToJst(data.ConsultationStartTime.Value.DateTime)
                .ToString("yyyy年MM月dd日 HH:mm")
            : "未定";

        return @$"<!DOCTYPE html>
<html lang='ja'>

<head>
    <meta charset=""UTF-8"">
    <meta name=""viewport"" content=""width=device-width, initial-scale=1.0"">
    <title>面談予約のリマインダー</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
        }}

        a {{
            color: #007BFF;
            text-decoration: none;
        }}

        a:hover {{
            text-decoration: underline;
        }}
    </style>
</head>

<body>
    <p>{data.Name} 様</p>
    
    <p>お世話になっております。<br>
    アドバイザーナビ株式会社のカスタマーサポートチームでございます。</p>
    
    <p>ご予約いただいておりますアドバイザーとの面談についてリマインダーをお送りいたします。</p>
    
    <p><strong>面談予定日時：{consultationTime}</strong></p>
    
    <p>面談は予定通り実施させていただきますので、お時間になりましたらご準備をお願いいたします。</p>
    
    <p>面談に関してご不明な点やご変更のご希望がございましたら、お気軽にカスタマーサポートチームまでご連絡ください。</p>
    
    <p>どうぞよろしくお願いいたします。</p>
    
    <hr>
    
    <p>アドバイザーナビ株式会社<br>
    カスタマーサポートチーム<br>
    東京都中央区日本橋兜町8-1　FinGATE TERRACE<br>
    <a href=""https://adviser-navi.co.jp"">https://adviser-navi.co.jp</a></p>
</body>
</html>";
    }
}
