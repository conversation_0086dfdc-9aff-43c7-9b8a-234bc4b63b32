using Adnavi.Utils;
using Adnavi.Utils.Exceptions;
using Adnavi.Utils.Images;
using NUlid;
using Microsoft.Extensions.Options;
using System.ComponentModel.DataAnnotations;
using Adnavi.Domain.Models.Profiles.AdviserProfiles;
using System.Net;
using Serilog;

namespace Adnavi.Domain.Logics.Common.Images;

public class UploadData
{
    public UploadData(
        string assignedUrl,
        Ulid contentId,
        ImageFileTypes contentType
    )
    {
        AssignedUrl = assignedUrl;
        ContentId = contentId;
        ContentType = contentType;
    }

    [Required]
    public string AssignedUrl { get; set; }

    [Required]
    public Ulid ContentId { get; set; }

    [Required]
    public ImageFileTypes ContentType { get; set; }
};

public class ImageService
{
    private readonly S3Utils _s3;
    private const string _imageRootPath = "images";
    private const string _adviserRootPath = "advisers";
    private readonly string _bucketName;
    private readonly string _image_processor_url;
    private readonly string _adviser_eye_catch_image_url;
    private readonly string _mediaUrl;
    private readonly Dictionary<
        ImageFileTypes,
        string
    > _imageFileTypeToExtensionMap;

    private readonly Dictionary<
        ImageFileTypes,
        string
    > _imageFileTypeToContentTypeMap;

    private readonly LogDictionary _logDictionary;

    public ImageService(
        S3Utils s3Utils,
        IOptions<AdnaviS3Settings> settings,
        LogDictionary logDictionary
    )
    {
        _s3 = s3Utils;
        _mediaUrl = settings.Value.AdviserNaviMediaUrl;
        _bucketName = settings.Value.AdviserNaviMediaBucketName;
        _image_processor_url = settings.Value.ImageProcessorUrl;
        _adviser_eye_catch_image_url = settings.Value.AdviserEyeCatchImageUrl;
        _imageFileTypeToExtensionMap = new()
        {
            { ImageFileTypes.Jpeg, ".jpeg" },
            { ImageFileTypes.Png, ".png" },
            { ImageFileTypes.Webp, ".webp" }
        };
        _imageFileTypeToContentTypeMap = new()
        {
            { ImageFileTypes.Jpeg, "image/jpeg" },
            { ImageFileTypes.Png, "image/png" },
            { ImageFileTypes.Webp, "image/webp" }
        };
        _logDictionary = logDictionary;
    }

    /// <summary>
    /// アップロード用のURLとコンテンツIDを取得する。
    /// </summary>
    /// <param name="imageFileType"></param>
    /// <returns>UploadData</returns>
    public UploadData GenerateUploadPreSignedUrl(ImageFileTypes imageFileType)
    {
        string extension = GetExtensionFromImageFileType(imageFileType);
        Ulid contentId = Ulid.NewUlid();
        string objectKey =
            $"{_imageRootPath}/{contentId}/original_{DateTime.UtcNow.ToString("s").Replace(":", "-")}{extension}";
        string url = _s3.CreateUploadUrl(
            objectKey,
            GetContentTypeFromImageFileType(imageFileType)
        );
        return new UploadData(url, contentId, imageFileType);
    }

    /// <summary>
    /// 画像の型とサイズを指定して、リサイズされた画像のURLを取得する。
    /// </summary>
    /// <param name="contentId"></param>
    /// <param name="imageFileType"></param>
    /// <param name="maximumHeight"></param>
    /// <param name="maximumWidth"></param>
    /// <returns></returns>
    /// <exception cref="NotFoundException"></exception>
    /// <exception cref="InternalException"></exception> <summary>
    public async Task<string?> GetConvertedImageUrl(
        Ulid contentId,
        ImageFileTypes imageFileType,
        int maximumHeight,
        int maximumWidth
    )
    {
        string extension = GetExtensionFromImageFileType(imageFileType);
        string RequestedObjectKey =
            $"{_imageRootPath}/{contentId}/{maximumHeight}x{maximumWidth}";
        string objectKey;

        var listObjectsResponse = await _s3.ListObjectsAsync(
            $"{_imageRootPath}/{contentId}"
        );

        if ( // no such content
            !listObjectsResponse.S3Objects.Any(
                obj => obj.Key.Contains($"{_imageRootPath}/{contentId}/")
            )
        )
            return null;

        if ( // already exists
            listObjectsResponse.S3Objects.Any(
                obj =>
                    obj.Key.StartsWith(RequestedObjectKey)
                    && obj.Key.EndsWith(extension)
            )
        )
        {
            objectKey = listObjectsResponse.S3Objects
                .First(
                    obj =>
                        obj.Key.StartsWith($"{RequestedObjectKey}_")
                        && obj.Key.EndsWith(extension)
                )
                .Key;
        }
        else
        // create requested object
        {
            string resizedObjectKey = listObjectsResponse.S3Objects
                .First(
                    obj =>
                        obj.Key.StartsWith(
                            $"{_imageRootPath}/{contentId}/original_"
                        )
                )
                .Key;
            objectKey =
                $"{RequestedObjectKey}_{DateTime.UtcNow.ToString("s").Replace(":", "-")}{extension}";

            try
            {
                await ResizeAndUploadToS3Lambda(
                    resizedObjectKey,
                    objectKey,
                    imageFileType,
                    maximumHeight,
                    maximumWidth
                );
            }
            catch (Exception ex)
            {
                throw new InternalException(
                    "Failed to resize and upload to S3 Lambda.",
                    ex
                );
            }
        }
        string url = _mediaUrl + "/" + objectKey;
        return url;
    }

    public async Task<IEnumerable<ImageSpecAndUrl>> GetConvertedImageUrls(
        Ulid contentId,
        List<ImageFileSpec> fileSpecs,
        Ulid? photoId = null
    )
    {
        var results = new List<ImageSpecAndUrl>();

        foreach (var spec in fileSpecs)
        {
            var url = await GetConvertedImageUrl(
                contentId,
                spec.Type,
                spec.MaxHeight ?? 300,
                spec.MaxWidth ?? 300
            );
            if (url == null)
                continue;

            results.Add(
                new ImageSpecAndUrl
                {
                    Id = photoId,
                    MaxWidth = spec.MaxWidth,
                    MaxHeight = spec.MaxHeight,
                    MediaType = spec.MediaType,
                    Url = url,
                    Fallback = spec.Fallback,
                }
            );
        }

        return results;
    }

    /// <summary>
    /// API Gateway経由でLambda画像をリサイズしてS3にアップロードする。
    /// </summary>
    /// <returns></returns>
    /// <exception cref="InternalException"></exception>
    private async Task ResizeAndUploadToS3Lambda(
        string key,
        string targetKey,
        ImageFileTypes imageFileTypes,
        int height,
        int width
    )
    {
        var size = $"{height}x{width}";
        string format = GetExtensionFromImageFileType(imageFileTypes)
            .Replace(".", "");
        using var httpClient = new HttpClient();
        var apiUrl =
            $"{_image_processor_url}/resize?bucket={Uri.EscapeDataString(_bucketName)}&resized_key={Uri.EscapeDataString(key)}&target_key={Uri.EscapeDataString(targetKey)}&size={Uri.EscapeDataString(size)}&format={Uri.EscapeDataString(format)}";
        var response = await httpClient.GetAsync(apiUrl);

        if (response.StatusCode == HttpStatusCode.NotAcceptable)
        {
            _logDictionary.ImageResizeSourceSizeExceeded(
                key,
                imageFileTypes,
                width,
                height
            );
            return;
        }
        else if (!response.IsSuccessStatusCode)
        {
            _logDictionary.ImageResizeFailed(
                key,
                imageFileTypes,
                width,
                height,
                response.Content.ReadAsStringAsync().Result
            );
            return;
        }
    }

    /// <summary>
    /// アドバイザーの情報に基づいて、アイキャッチ画像を作成する。
    /// </summary>
    /// <param name="adviser"></param>
    /// <param name="contentId"></param>
    /// <returns></returns>
    /// <exception cref="NotFoundException"></exception>
    /// <exception cref="InternalException"></exception>
    public async Task<string> GenerateAdviserEyeCatchImage(
        Adviser adviser,
        Ulid? contentId
    )
    {
        var listObjectsResponse = await _s3.ListObjectsAsync(
            $"{_imageRootPath}/{contentId}"
        );

        var profileImageKey = listObjectsResponse.S3Objects
            .First(
                obj =>
                    obj.Key.StartsWith(
                        $"{_imageRootPath}/{contentId}/original_"
                    )
            )
            .Key;
        var targetKey =
            $"{_adviserRootPath}/{adviser.Id}/profile/eyeCatch/Eye_Catch_Image.jpeg";

        var first_name = adviser.FirstName;
        var family_name = adviser.FamilyName;
        var company_name = adviser.IfaCompany?.Name ?? "";
        var prefecture = adviser.Prefecture?.ToString() ?? "";
        var age = adviser.Birthday.HasValue
            ? (
                DateTime.UtcNow.Date
                > adviser.Birthday.Value.Date.AddYears(
                    DateTime.UtcNow.Year - adviser.Birthday.Value.Year
                )
                    ? (
                        DateTime.UtcNow.Year - adviser.Birthday.Value.Year
                    ).ToString()
                    : (
                        DateTime.UtcNow.Year - adviser.Birthday.Value.Year - 1
                    ).ToString()
            )
            : "";

        try
        {
            await GenerateAdviserEyecatchAndUploadToS3Lambda(
                profileImageKey,
                targetKey,
                first_name,
                family_name,
                company_name,
                prefecture,
                age
            );
        }
        catch (Exception ex)
        {
            throw new InternalException(
                "Failed to generate adviser eye catch image.",
                ex
            );
        }
        string url = _mediaUrl + "/" + targetKey;
        return url;
    }

    /// <summary>
    /// API Gateway経由で、アドバイザーの情報を基にアイキャッチを作成する。
    /// </summary>
    /// <returns></returns>
    /// <exception cref="InternalException"></exception>
    public async Task GenerateAdviserEyecatchAndUploadToS3Lambda(
        string profileImageKey,
        string targetKey,
        string first_name,
        string family_name,
        string company_name,
        string prefecture,
        string age
    )
    {
        try
        {
            using var httpClient = new HttpClient();
            var apiUrl =
                $"{_adviser_eye_catch_image_url}/eye_catch?bucket={Uri.EscapeDataString(_bucketName)}&profile_image_key={Uri.EscapeDataString(profileImageKey)}&target_key={Uri.EscapeDataString(targetKey)}&first_name={Uri.EscapeDataString(first_name)}&family_name={Uri.EscapeDataString(family_name)}&company_name={Uri.EscapeDataString(company_name)}&prefecture={Uri.EscapeDataString(prefecture)}&age={Uri.EscapeDataString(age)}";
            var response = await httpClient.GetAsync(apiUrl);
        }
        catch (Exception ex)
        {
            _logDictionary.AdviserEyeCatchImageGenerationFailed(
                profileImageKey,
                $"{ex.Message}\n{ex.StackTrace}"
            );
            throw new InternalException(
                "Failed to generate adviser eye catch image.",
                ex
            );
        }
    }

    /// <summary>
    /// コンテンツIDに紐づく画像ファイルのメタデータの一覧を取得する。
    /// </summary>
    /// <param name="contentId"></param>
    /// <returns></returns>
    public async Task<List<string>> ListImageFilesWith(Ulid contentId)
    {
        var response = await _s3.ListObjectsAsync(contentId.ToString());

        var list =
            response.S3Objects
                .Where(
                    obj => obj.Key.StartsWith($"{_imageRootPath}/{contentId}/")
                )
                .Select(objectKey => objectKey.Key)
                .ToList() ?? new List<string>();
        return list;
    }

    public async Task DeleteImage(Ulid contentId)
    {
        var response = await _s3.ListObjectsAsync(contentId.ToString());
        var objectsToDelete = response.S3Objects
            .Where(obj => obj.Key.StartsWith($"{_imageRootPath}/{contentId}/"))
            .ToList();

        foreach (var obj in objectsToDelete)
        {
            await _s3.Delete(obj.Key);
        }
        await _s3.Delete(contentId.ToString());
    }

    private string GetExtensionFromImageFileType(ImageFileTypes imageFileType)
    {
        if (
            _imageFileTypeToExtensionMap.TryGetValue(
                imageFileType,
                out var extension
            )
        )
        {
            return extension;
        }
        throw new ArgumentException("Unsupported image file type");
    }

    public string GetContentTypeFromImageFileType(ImageFileTypes imageFileType)
    {
        if (
            _imageFileTypeToContentTypeMap.TryGetValue(
                imageFileType,
                out var contentType
            )
        )
        {
            return contentType;
        }
        throw new ArgumentException("Unsupported image file type");
    }
}
