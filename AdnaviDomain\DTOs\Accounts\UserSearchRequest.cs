using Adnavi.Domain.Logics.Accounts.OrganizationManagement;
using Adnavi.Utils.Models;

namespace Adnavi.Domain.DTOs.Accounts;

public class UserSearchRequest : IDataTransferObject
{
    public string? Text { get; set; }
    public bool NotMemberOfOrganization { get; set; } = false;
    public UserOrderColumn OrderColumn { get; set; } = UserOrderColumn.Id;
    public bool Descendence { get; set; } = false;
    public uint? Offset { get; set; } = 0;
    public uint? Count { get; set; } = 10;
}
