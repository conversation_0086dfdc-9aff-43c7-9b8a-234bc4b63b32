using Adnavi.Domain.DTOs.Common.NotificationMessages;
using Adnavi.Domain.Models;
using Adnavi.Domain.Models.Accounts;
using Adnavi.Domain.Models.Commons.NotificationMessages;
using Adnavi.Domain.Models.Invoices.MatchingInvoices;
using Adnavi.Utils;
using Microsoft.EntityFrameworkCore;
using NUlid;

namespace Adnavi.Domain.Logics.Common.NotificationMessages;

public class NotificationMessageLogic
{
    private readonly ModelUtils _model;

    public NotificationMessageLogic(ModelUtils model)
    {
        _model = model;
    }

    /// <summary>
    /// Get All notification messages
    /// </summary>
    public async Task<
        IEnumerable<StaffNotificationMessageData>
    > GetAllNotificationMessages()
    {
        return await _model.Context.NotificationMessages
            .Include(m => m.Creator)
            .Include(m => m.UpdatedBy)
            .AsNoTracking()
            .Select(m => new StaffNotificationMessageData(m))
            .ToListAsync();
    }

    public async Task<StaffNotificationMessageData> GetNotificationMessage(
        Ulid messageId
    )
    {
        var message = await _model.SingleAsync(
            _model.Context.NotificationMessages.Where(m => m.Id == messageId)
        );
        return new StaffNotificationMessageData(message);
    }

    public async Task<
        IEnumerable<StaffNotificationMessageData>
    > GetNotificationMessagesCreatedBy(Ulid userId)
    {
        return await _model.Context.NotificationMessages
            .Include(m => m.Creator)
            .Include(m => m.UpdatedBy)
            .AsNoTracking()
            .Where(m => m.CreatorId == userId)
            .Select(m => new StaffNotificationMessageData(m))
            .ToListAsync();
    }

    /// <summary>
    /// Create a notification message
    /// </summary>
    /// <param name="creator"></param>
    /// <param name="recipients"></param>
    /// <param name="data"></param>
    /// <returns></returns> <summary>
    public async Task CreateNotificationMessage(
        User creator,
        IEnumerable<User> recipients,
        NotificationMessageData data
    )
    {
        var CreatedMessage = data.To(
            _model.Create(
                new NotificationMessage
                {
                    Creator = creator,
                    CreatedTime = DateTime.UtcNow
                }
            )
        );
        CreatedMessage.Recipients = recipients
            .Select(
                r =>
                    _model.Create(
                        new Recipient
                        {
                            UserId = r.Id,
                            User = r,
                            NotificationMessage = CreatedMessage,
                            NotificationMessageId = CreatedMessage.Id,
                            IsRead = false,
                        }
                    )
            )
            .ToList();

        await _model.SaveChangesAsync();
    }

    /// <summary>
    /// Update the notification message
    /// </summary>
    /// <param name="updater"></param>
    /// <param name="Id"></param>
    /// <param name="data"></param>
    public async Task UpdateNotificationMessage(
        User updater,
        Ulid Id,
        StaffNotificationMessageData data
    )
    {
        var message = await _model.SingleAsync(
            _model.Context.NotificationMessages.Where(m => m.Id == Id)
        );
        message.UpdatedBy = updater;
        message.ModifiedTime = DateTime.UtcNow;
        _model.Update(data.To(message));
        var recipients = _model.Context.Recipients.Where(
            r => r.NotificationMessageId == Id
        );
        foreach (var recipient in await recipients.ToListAsync())
        {
            recipient.IsRead = false;
            recipient.ReadTime = null;
            _model.Update(recipient);
        }
        await _model.SaveChangesAsync();
    }

    public async Task DeleteNotificationMessage(Ulid id)
    {
        foreach (
            var recipient in await _model.Context.Recipients
                .Where(r => r.NotificationMessageId == id)
                .ToListAsync()
        )
        {
            _model.Delete(recipient);
        }
        _model.Delete(
            await _model.FindAsync(_model.Context.NotificationMessages, id)
        );
        await _model.SaveChangesAsync();
    }

    /// <summary>
    /// Get the notification messages for the user
    /// </summary>
    /// <param name="UserId"></param>
    /// <returns></returns>
    public async Task<
        IEnumerable<NotificationMessageData>
    > GetNotificationMessages(Ulid UserId)
    {
        // Get Messages created manually
        var result = await _model.Context.Recipients
            .Where(r => r.UserId == UserId && r.IsRead == false)
            .Select(r => new NotificationMessageData(r.NotificationMessage))
            .ToListAsync();

        var user = _model.Single(
            _model.Context.Users.Where(u => u.Id == UserId)
        );
        if (user != null && user.AdviserId != null)
        {
            var adviserId = user.AdviserId ?? Ulid.Empty;
            result = AddProgressStatusReminder(result, adviserId);
        }

        return result;
    }

    /// <summary>
    /// Add a remind message if the user has not updated the progress status in the previous month
    /// </summary>
    /// <param name="messages"></param>
    /// <param name="adviserId"></param>
    private List<NotificationMessageData> AddProgressStatusReminder(
        List<NotificationMessageData> messages,
        Ulid adviserId
    )
    {
        var notCompletedCount = _model.Context.MatchingInvoiceManagements
            .Where(
                m =>
                    m.AdviserId == adviserId
                    && (
                        m.ProgressStatus == ProgressStatuses.Hold
                        || m.ProgressStatus == ProgressStatuses.WaitingReply
                        || m.ProgressStatus
                            == ProgressStatuses.ScheduledInterview
                    )
            )
            .Count();
        if (notCompletedCount > 0)
        {
            messages.Add(
                new NotificationMessageData()
                {
                    Title = "送客進捗未完了",
                    Content = "送客進捗が未完了です。進捗を更新してください。",
                    Importance = Importance.High,
                    ModifiedTime = DateTime.UtcNow
                }
            );
        }
        return messages;
    }
}
