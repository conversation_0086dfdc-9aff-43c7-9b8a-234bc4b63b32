using Adnavi.Domain.Models.Consultations.BatchIntroductions;

namespace Adnavi.Domain.Logics.Consultations.BatchIntroductions;

public class BatchIntroductionReminderMailTemplate
{
    public BatchIntroductionReminderMailTemplate() { }

    public string GetSubject() => "御礼/ご確認";

    public string GetNotificationSubject(bool success) =>
        $"【サービス】投資家へメール送信: 御礼/ご確認" + (success ? "（成功）" : "（失敗）");

    public string GetContent(BatchIntroduction data) =>
        data.AssetRange == Models.Profiles.InvestorProfiles.AssetRanges.B0_A500
            ? GetContentB0A500(data)
            : GetNormalContent(data);

    private string GetNormalContent(BatchIntroduction data) =>
        @$"<!DOCTYPE html>
<html lang='ja'>

<head>
    <meta charset=""UTF-8"">
    <meta name=""viewport"" content=""width=device-width, initial-scale=1.0"">
    <title>メールテンプレート本文</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
        }}

        a {{
            color: #007BFF;
            text-decoration: none;
        }}

        a:hover {{
            text-decoration: underline;
        }}
    </style>
</head>

<body>
    <p align=left style='text-align:left'>この度は、資産運⽤アドバイザー紹介サービスにお申し込みいただきありがとうございます。<br>アドバイザーナビ株式会社で、カスタマーサポートチームの責任者をしております佐藤と申します。
    <br>弊社、カスタマーサポートチームでアドバイザー選びのサポートさせていただきたく、ご連絡いたしました。<br>弊社サービスをご利用いただいたお客様から</p>
    <p align=left style='text-align:left'>・アドバイザーを選ぶポイントが分からない<br>・どの観点でアドバイザーを決めたら良いかアドバイスが欲しい</p>
    <p align=left style='text-align:left'>というお声を多く頂戴しております。<br>カスタマーサポートで、その観点を面談を通じ簡単にご説明させていただいております。<br>
    よろしければ、15〜30 分程度、zoom 等にてご⾯談のお時間をいただけませんでしょうか。</p>
    <p align=left style='text-align:left'><span style='color:red'>※弊社は資産運⽤アドバイザー選びをお⼿伝いするサービスのみを⾏なっており、商品の販売はいたしません。無理な営業等もございませんのでご安⼼くださいませ。</span></p>

<p align=left style='text-align:left'>⼤変お⼿数ではございますが、よろしければ下記<span
lang=EN-US> URL </span>からご都合のよろしい⽇程を選択いただけますでしょうか。 </p>

<p align=left style='text-align:left'><span lang=EN-US><o:p>&nbsp;</o:p></span></p>

<p align=center style='text-align:center'>ご多忙の中、恐れ⼊りますがご検討よろしくお願いいたします。</p>

<div style='text-align:center'> <a href=""https://timerex.net/s/0529/125c9058"">
<img src='https://adviser-navi.co.jp/img/mail/customer-support-button01.png'></a>
</div>

<p align=center style='text-align:center'><span lang=EN-US><o:p>&nbsp;</o:p></span></p>

<div style='text-align:center'>
<img src='https://adviser-navi.co.jp/img/mail/customer-support01.jpg' alt='写真' width='120' ;padding:0px 20px 50px;'>
</div>


<p align=left style='text-align:left;margin:22px'><b>アドバイザーナビ株式会社 佐藤⽴樹 <span
lang=EN-US><o:p></o:p></span></b>
<br>証券会社に新卒⼊社。営業店で資産運⽤アドバイザーとして業務に従事。<br>証券会社退職後、アドバイザーナビ株式会社でカスタマーサポートとして、投資家の資産運⽤アドバイザー選びのサポートを担当。<br>趣味：サッカー、グルメ。<span
lang=EN-US style='font-size:11.0pt'><o:p></o:p></span></p>

<p><span lang=EN-US><o:p>&nbsp;</o:p></span></p>

</div>

</body>
</html>
";

    private string GetContentB0A500(BatchIntroduction data) =>
        @$"{data.Name} 様

お世話になります。
アドバイザーナビ株式会社のカスタマーサポートチームでございます。
 
この度は、資産運用アドバイザー紹介にお申し込みいただきまして誠にありがとうございました。
（お申し込みいただきましたサービス： {data.GetOriginLpUrl()}?advsn=38）
 
アドバイザー選びのお手伝いをさせていただきたくご連絡をさせていただきました。
お申し込み画面で投資に関しますご意向や情報のご入力はいただいたのですが、アドバイザー情報の選択が完了しておらず、ご面談の設定ができておりません。
 
アドバイザー選びにつきましてご不明な点がありましたら、お気軽にカスタマーサポートチームまでお問い合わせくださいませ。ご意向にあった担当者選びをお手伝いさせていただきます。
 
微力ではございますが、お力になりたく存じます。 ご検討よろしくお願いいたします。   

------------------------------------------------------ 
アドバイザーナビ株式会社
カスタマーサポートチーム
東京都中央区日本橋兜町8-1　FinGATE TERRACE 
https://adviser-navi.co.jp
------------------------------------------------------
";
}
