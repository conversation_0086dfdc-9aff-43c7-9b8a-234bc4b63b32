﻿using Adnavi.Domain.Models.Consultations.ConsultationRequests;
using Adnavi.Domain.Models.Profiles.InvestorProfiles;
using Adnavi.Utils;
using CsvHelper.Configuration.Attributes;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnavi.Domain.Logics.Consultations.ConsultationRequests
{
    public sealed class AdviserConsultationRequestCsvRecord
    {
        [Name("ID")]
        public string Id { get; set; }

        [Name("氏名")]
        public string Name { get; set; }

        [Name("年齢")]
        public int Age { get; set; }

        [Name("電話番号")]
        public string? PhoneNumber { get; set; }

        [Name("メール")]
        public string Email { get; set; }

        [Name("資産")]
        public AssetRanges AssetRange { get; set; }

        [Name("OriginUrl")]
        public string OriginUrl { get; set; }

        [Name("OriginName")]
        public string OriginName { get; set; }

        [Name("RemoteHost")]
        public string RemoteHost { get; set; }

        [Name("面談日")]
        public string? InterviewDate { get; set; }

        [Name("サイト申し込み理由")]
        public string? SiteRequestReason { get; set; }

        [Name("担当スタッフ")]
        public string ChargeStaff { get; set; }

        [Name("送客人数")]
        public int? AdviserCount { get; set; }

        [Name("サイト名")]
        public string SiteName { get; set; }

        [Name("Pステータス")]
        public PointStatuses PointStatus { get; set; }

        [Name("顧客クオリティ")]
        public byte? CustomerQuality { get; set; }

        [Name("約定確率")]
        public int? Probability { get; set; }

        [Name("スタッフ備考")]
        public string StaffRemarks { get; set; }

        [Name("申込日")]
        public string CreatedTime { get; set; }

        private AdviserConsultationRequestCsvRecord() { }

        public static async IAsyncEnumerable<AdviserConsultationRequestCsvRecord> FromQuery(
            AdnaviDomainContext context,
            IQueryable<AdviserConsultationRequest> query
        )
        {
            var query2 = query.Select(
                a =>
                    new AdviserConsultationRequestCsvRecord
                    {
                        Id = a.Id.ToString(),
                        Name = a.Name,
                        Age = a.Age,
                        PhoneNumber = a.TelephoneNumber,
                        Email = a.MailAddress,
                        AssetRange = a.AssetRange,
                        OriginUrl = a.OriginUrl,
                        OriginName = a.OriginName,
                        RemoteHost = a.RemoteHost,
                        ChargeStaff = a.ChargeStaff,
                        AdviserCount = a.AdviserCount,
                        SiteName = a.SiteName,
                        PointStatus = a.PointStatus,
                        CustomerQuality = a.CustomerQuality,
                        Probability = a.Probability,
                        CreatedTime = a.CreatedTime.ToString(
                            "yyyy/MM/dd HH:mm:ss"
                        ),
                        StaffRemarks = a.StaffRemarks,
                        InterviewDate =
                            a.InterviewDate != null
                                ? DateUtils
                                    .UtcToJst(a.InterviewDate.Value)
                                    .ToString("yyyy/MM/dd")
                                : string.Empty,
                        SiteRequestReason = a.SiteRequestReason
                    }
            );

            foreach (var result in await query2.ToArrayAsync())
            {
                yield return result;
            }
        }

        //private static async Task<string> JoinList(
        //    IQueryable<IHasDisplayName> query
        //)
        //{
        //    return string.Join(
        //        ";",
        //        await query.Select(b => b.DisplayName).ToListAsync()
        //    );
        //}

        private static string GetCompanyIntroductionDescription(
            bool? companyByCompanyIntroduction
        )
        {
            if (companyByCompanyIntroduction == null)
                return "";
            if (companyByCompanyIntroduction == true)
                return "企業で1人紹介";
            else
                return "企業で複数紹介";
        }
    }
}
