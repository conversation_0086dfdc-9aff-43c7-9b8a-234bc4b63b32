using Adnavi.Domain.Models.Profiles.InvestorProfiles;
using NUlid;
using Adnavi.Domain.Models.Consultations.BatchIntroductions;

namespace Adnavi.Domain.Logics.Consultations.BatchIntroductions
{
    /// <summary>
    /// IntroductionSettingHistory DTO
    /// /// </summary>
    /// <seealso cref="IntroductionSettingHistory" />
    public class IntroductionSettingHistoryData
    {
        public Ulid Id { get; set; }
        public Ulid AdviserId { get; set; }
        public string AdviserName { get; set; }
        public bool? AdviserCompanyByCompanyIntroduction { get; set; }
        public bool BatchIntroductionsEnabled { get; set; }
        public IEnumerable<AssetRanges>? AdviserAcceptAssetRanges { get; set; }
        public IEnumerable<AnnualIncomes>? AdviserAcceptAnnualIncomes { get; set; }
        public Ulid? IfaCompanyId { get; set; }
        public string? IfaCompanyName { get; set; }

        public Ulid? InsuranceCompanyId { get; set; }
        public string? InsuranceCompanyName { get; set; }

        public IEnumerable<AssetRanges>? IfaCompanyAcceptAssetRanges { get; set; }
        public IEnumerable<AnnualIncomes>? InsuranceCompanyAnnualIncomes { get; set; }
        public Ulid CreatedBy { get; set; }
        public DateTime CreatedTime { get; set; }
        public string CreatedUserEMail { get; set; }

        public IntroductionSettingHistoryData(IntroductionSettingHistory f)
        {
            Id = f.Id;
            AdviserId = f.AdviserId;
            AdviserName = f.AdviserName;
            AdviserCompanyByCompanyIntroduction =
                f.AdviserCompanyByCompanyIntroduction;
            BatchIntroductionsEnabled = f.BatchIntroductionsEnabled;
            AdviserAcceptAssetRanges = f.AdviserAcceptAssetRanges
                ?.Select(x => x.Id)
                .ToList();
            IfaCompanyId = f.IfaCompanyId;
            IfaCompanyName = f.IfaCompanyName;
            IfaCompanyAcceptAssetRanges = f.IfaCompanyAcceptAssetRanges
                ?.Select(x => x.Id)
                .ToList();
            InsuranceCompanyId = f.InsuranceCompanyId;
            InsuranceCompanyName = f.InsuranceCompanyName;
            InsuranceCompanyAnnualIncomes =
                f.InsuranceCompanyAcceptAnnualIncomes
                    ?.Select(x => x.Id)
                    .ToList();
            CreatedBy = f.CreatedBy;
            CreatedUserEMail = f.CreatedUserEMail;
            CreatedTime = f.CreatedTime;
        }
    }
}
