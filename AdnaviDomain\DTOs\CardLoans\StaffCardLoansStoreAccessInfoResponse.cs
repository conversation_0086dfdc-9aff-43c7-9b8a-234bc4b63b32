using Adnavi.Domain.Models.CardLoans;
using Adnavi.Utils.Models;
using Microsoft.EntityFrameworkCore;
using NUlid;

namespace Adnavi.Domain.DTOs.CardLoans;

public class StaffCardLoansStoreAccessInfoResponse : IDataTransferObject
{
    public Ulid Id { get; set; }
    public string CardLoanName { get; set; }
    public string StoreName { get; set; }
    public string StationName { get; set; }
    public int MinutesOnFoot { get; set; }

    public static async IAsyncEnumerable<StaffCardLoansStoreAccessInfoResponse> FromQuery(
        IQueryable<CardLoanStoreAccessInfo> query
    )
    {
        var data = await query
            .AsNoTracking()
            .Select(
                s =>
                    new StaffCardLoansStoreAccessInfoResponse
                    {
                        Id = s.Id,
                        CardLoanName = s.CardLoanStore.CardLoan.Name,
                        StoreName = s.CardLoanStore.Name,
                        StationName = s.Station.Name,
                        MinutesOnFoot = s.MinutesOnFoot,
                    }
            )
            .ToArrayAsync();

        foreach (var d in data)
        {
            yield return d;
        }
    }
}
