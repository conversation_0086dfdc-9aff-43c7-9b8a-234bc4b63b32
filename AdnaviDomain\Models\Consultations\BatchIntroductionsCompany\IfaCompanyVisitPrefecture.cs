using Adnavi.Domain.Models.Commons;
using Adnavi.Domain.Models.Profiles.IfaCompanyProfiles;
using Adnavi.Utils.Models;
using Microsoft.EntityFrameworkCore;
using NUlid;

namespace Adnavi.Domain.Models.Consultations.BatchIntroductionsCompany;

[Index(nameof(Prefectures), nameof(IfaCompanyId), IsUnique = true)]
public class IfaCompanyVisitPrefecture : IHasId
{
    public Ulid Id { get; set; }

    public Prefectures Prefectures { get; set; }

    public Ulid IfaCompanyId { get; set; }

    public virtual IfaCompany IfaCompany { get; set; }
}
