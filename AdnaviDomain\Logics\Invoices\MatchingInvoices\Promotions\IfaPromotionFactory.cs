using Adnavi.Domain.Models.Consultations.BatchIntroductions;
using Adnavi.Domain.Models.Profiles.AdviserProfiles;

namespace Adnavi.Domain.Logics.Invoices.MatchingInvoices.Promotions;

public class IfaPromotionFactory
{
    private readonly List<IIfaPromotion> _promotions = new();

    public IfaPromotionFactory(
        InitialRegistrationPromotion initialRegistrationPromotion
    )
    {
        _promotions.Add(initialRegistrationPromotion);
    }

    /// <summary>
    /// Copy and return all promotions.
    /// </summary>
    public List<IIfaPromotion> GetAllPromotion() => _promotions.ToList();

    private IEnumerable<IIfaPromotion> GetEligiblePromotions(
        Adviser adviser,
        BatchIntroduction introduction
    ) => _promotions.Where(p => p.Is<PERSON>ligible(adviser, introduction));

    internal PromotionAndAmount? GetMinimumMatchingFee(
        Adviser adviser,
        BatchIntroduction introduction
    )
    {
        var promotionAmount = GetEligiblePromotions(adviser, introduction)
            .Select(
                p =>
                    new PromotionAndAmount(
                        p,
                        p.GetMatching<PERSON>ee(adviser, introduction)
                    )
            )
            .MinBy(x => x.Amount);

        return promotionAmount;
    }

    internal PromotionAndAmount? GetMinimumInterviewFee(
        Adviser adviser,
        BatchIntroduction introduction
    )
    {
        var promotionAmount = GetEligiblePromotions(adviser, introduction)
            .Select(
                p =>
                    new PromotionAndAmount(
                        p,
                        p.GetInterviewFee(adviser, introduction)
                    )
            )
            .MinBy(x => x.Amount);

        return promotionAmount;
    }
}

/// <summary>
/// 金額と適用したキャンペーン
/// </summary>
/// <param name="Promotion">適用キャンペーン</param>
/// <param name="Amount">金額</param>
internal record PromotionAndAmount(IIfaPromotion? Promotion, int? Amount);
