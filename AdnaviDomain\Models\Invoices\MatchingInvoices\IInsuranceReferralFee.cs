using Adnavi.Domain.Models.Profiles.InvestorProfiles;

namespace Adnavi.Domain.Models.Invoices.MatchingInvoices;

/// <summary>
/// 保険紹介手数料インターフェース
/// </summary>
public interface IInsuranceReferralFee
{
    /// <summary>
    /// 紹介手数料取得
    /// </summary>
    /// <param name="annualIncome">紹介した資産家の金融資産範囲</param>
    /// <returns>紹介手数料</returns>
    int GetIntroductionFee(AnnualIncomes annualIncome);

    /// <summary>
    /// 面談手数料
    /// </summary>
    /// <param name="annualIncome">紹介した資産家の金融資産範囲</param>
    /// <returns></returns> <summary>
    int GetInterviewFee(AnnualIncomes annualIncome);
}
