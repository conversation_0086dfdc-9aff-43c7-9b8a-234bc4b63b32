using Adnavi.Domain.DTOs.Profiles.AdviserProfiles;
using Adnavi.Domain.DTOs.Profiles.CustomerEvaluations;
using Adnavi.Domain.Logics.Accounts.OrganizationManagement;
using Adnavi.Domain.Logics.Common.SequenceNumber;
using Adnavi.Domain.Logics.Consultations.InsuranceBatchIntroductions;
using Adnavi.Domain.Models;
using Adnavi.Domain.Models.Accounts;
using Adnavi.Domain.Models.Commons;
using Adnavi.Domain.Models.Commons.SequenceNumber;
using Adnavi.Domain.Models.Consultations.InsuranceBatchIntroductions;
using Adnavi.Domain.Models.Profiles;
using Adnavi.Domain.Models.Profiles.AdviserProfiles;
using Adnavi.Domain.Models.Profiles.InvestorProfiles;
using Microsoft.EntityFrameworkCore;
using NUlid;

namespace Adnavi.Domain.Logics.Profiles.AdviserProfiles;

public class InsuranceAdviserProfileLogic
{
    private readonly SequenceNumberService _seq;
    private readonly AdnaviDomainContext _context;
    private readonly ModelUtils _model;
    private readonly AdviserProfileCommonLogic _commonLogic;

    public InsuranceAdviserProfileLogic(
        ModelUtils model,
        AdnaviDomainContext context,
        SequenceNumberService seq,
        AdviserProfileCommonLogic commonLogic
    )
    {
        _context = context;
        _model = model;
        _seq = seq;
        _commonLogic = commonLogic;
    }

    private IQueryable<InsuranceAdviser> PublishableAdviserQuery() =>
        _context.InsuranceAdvisers.Where(
            adviser =>
                adviser.Published
                && adviser.AgreementsAccepted
                && adviser.FamilyName != ""
        );

    // public Task<Adviser?> GetAdviserWithEMail(string eMail)
    // {
    //     return _context.Advisers
    //         .Where(a => a.EMail == eMail)
    //         .SingleOrDefaultAsync();
    // }

    private IQueryable<InsuranceAdviser> GetQuery(
        List<Prefectures>? areas = null,
        List<CustomerTypes>? mainCustomerTypes = null,
        List<AssetRanges>? assetRanges = null,
        List<AdviserWorkTypes>? adviserWorkTypes = null,
        List<Days>? workDays = null,
        List<ContactMethods>? availableContactMethods = null,
        string? text = null,
        bool publishedOnly = true,
        bool isBatchIntroductionSetting = false,
        Ulid? insuranceCompanyId = null
    )
    {
        areas ??= new();
        mainCustomerTypes ??= new();
        adviserWorkTypes ??= new();
        workDays ??= new();
        availableContactMethods ??= new();

        var stringAreas = areas.Select(p => p.ToString());
        var stringWorkDays = workDays.Select(p => p.ToString());
        var stringAvailableContactMethods = availableContactMethods.Select(
            p => p.ToString()
        );

        var query = publishedOnly
            ? PublishableAdviserQuery()
            : _context.InsuranceAdvisers;

        if (isBatchIntroductionSetting)
            query = InsuranceMatchingLogic.AddWhereAccepted(query);

        if (areas.Any())
        {
            query = query.Where(
                a => a.VisitPrefectures.Any(p => stringAreas.Contains(p.Name))
            );
        }

        if (mainCustomerTypes.Any())
        {
            query = query.Where(
                a =>
                    a.AdditionalProfileItems != null
                    && a.AdditionalProfileItems.MainCustomerType != null
                    && mainCustomerTypes.Contains(
                        a.AdditionalProfileItems.MainCustomerType.Value
                    )
            );
        }

        if (adviserWorkTypes.Any())
        {
            foreach (var adviser in query.AsNoTracking())
            {
                var workTypes = adviser.GetWorkTypes();
                if (
                    workTypes == null
                    || !workTypes.Any()
                    || !adviserWorkTypes.Any(w => workTypes.Contains(w))
                )
                    query = query.Where(a => a.Id != adviser.Id);
            }
        }

        if (workDays.Any())
        {
            query = query.Where(
                a =>
                    a.AdditionalProfileItems != null
                    && a.AdditionalProfileItems.WorkDays.Any(
                        d => stringWorkDays.Contains(d.Name)
                    )
            );
        }

        if (availableContactMethods.Any())
        {
            query = query.Where(
                a =>
                    a.AdditionalProfileItems != null
                    && a.AdditionalProfileItems.AvailableContactMethods.Any(
                        m => stringAvailableContactMethods.Contains(m.Name)
                    )
            );
        }

        if (!string.IsNullOrEmpty(text))
        {
            text = text.Replace(" ", "").Replace("　", "");
            query = query.Where(
                a => (a.FamilyName + a.FirstName).Contains(text)
            );
        }

        if (assetRanges != null && assetRanges.Any())
        {
            // 一括紹介設定の資産範囲が設定されている場合資産範囲内であることを確認する
            query = query.Where(
                a =>
                    a.InsuranceBatchIntroductionSetting == null
                    || !a.InsuranceBatchIntroductionSetting.AcceptAssetRanges.Any()
                    || a.InsuranceBatchIntroductionSetting.AcceptAssetRanges.Any(
                        r => assetRanges.Contains(r.Id)
                    )
            );

            // 保険法人が設定されていること、
            // 一括紹介設定のIFA法人の資産範囲が設定されている場合資産範囲内であることを確認する
            query = query.Where(
                a =>
                    a.InsuranceCompany != null
                    && (
                        a.InsuranceCompany.BatchIntroductionInsuranceCompanySetting
                            == null
                        || !a.InsuranceCompany.BatchIntroductionInsuranceCompanySetting.AcceptAssetRanges.Any()
                        || a.InsuranceCompany.BatchIntroductionInsuranceCompanySetting.AcceptAssetRanges.Any(
                            r => assetRanges.Contains(r.Id)
                        )
                    )
            );
        }
        if (insuranceCompanyId != null)
        {
            query = query.Where(
                a => a.InsuranceCompanyId == insuranceCompanyId
            );
        }

        return query;
    }

    public ICollection<InsuranceAdviser> GetRandomList(byte n)
    {
        List<Ulid> ids = PublishableAdviserQuery().Select(a => a.Id).ToList();
        List<Ulid> selectedIds = new();

        for (byte i = 0; i < n; i++)
        {
            if (ids.Count == 0)
            {
                break;
            }

            Random random = new();
            int selected = random.Next(ids.Count);
            selectedIds.Add(ids[selected]);
            _ = ids.Remove(ids[selected]);
        }

        return PublishableAdviserQuery()
            .Where(a => selectedIds.Contains(a.Id))
            .ToArray();
    }

    public Task<int> GetCount(
        List<Prefectures>? areas,
        string? text,
        bool publishedOnly = true
    )
    {
        return GetQuery(areas: areas, text: text, publishedOnly: publishedOnly)
            .CountAsync();
    }

    public async Task<ICollection<InsuranceAdviser>> Search(
        List<Prefectures>? areas = null,
        List<CustomerTypes>? customerTypes = null,
        List<AssetRanges>? assetRanges = null,
        List<AdviserWorkTypes>? adviserWorkTypes = null,
        List<Days>? workDays = null,
        List<ContactMethods>? availableContactMethods = null,
        string? text = null,
        uint offset = 0,
        uint count = 20,
        AdviserOrderColumn orderColumn = AdviserOrderColumn.ModifiedTime,
        bool descendence = false,
        bool publishedOnly = true,
        bool isBatchIntroductionSetting = false,
        Ulid? insuranceCompanyId = null
    )
    {
        var query = GetQuery(
            areas,
            customerTypes,
            assetRanges,
            adviserWorkTypes,
            workDays,
            availableContactMethods,
            text,
            publishedOnly,
            isBatchIntroductionSetting,
            insuranceCompanyId
        );

        switch (orderColumn)
        {
            case AdviserOrderColumn.Id:
                query = descendence
                    ? query.OrderByDescending(a => a.Id)
                    : query.OrderBy(a => a.Id);
                break;
            case AdviserOrderColumn.Name:
                query = descendence
                    ? query
                        .OrderByDescending(a => a.FamilyName)
                        .ThenByDescending(a => a.FirstName)
                    : query.OrderBy(a => a.FamilyName).ThenBy(a => a.FirstName);
                break;
            case AdviserOrderColumn.CompanyName:
                query = descendence
                    ? query.OrderByDescending(
                        a =>
                            a.InsuranceCompany == null
                                ? null
                                : a.InsuranceCompany.Name
                    )
                    : query.OrderBy(
                        a =>
                            a.InsuranceCompany == null
                                ? null
                                : a.InsuranceCompany.Name
                    );
                break;
            case AdviserOrderColumn.BatchIntroductionStatus:
                query = descendence
                    ? query.OrderByDescending(
                        a => a.CompanyByCompanyIntroduction
                    )
                    : query.OrderBy(a => a.CompanyByCompanyIntroduction);
                break;
            case AdviserOrderColumn.BatchIntroductionSettingEnabled:
                query = descendence
                    ? query.OrderByDescending(
                        a =>
                            a.InsuranceBatchIntroductionSetting != null
                            && a.InsuranceBatchIntroductionSetting.Enabled
                    )
                    : query.OrderBy(
                        a =>
                            a.InsuranceBatchIntroductionSetting != null
                            && a.InsuranceBatchIntroductionSetting.Enabled
                    );
                break;
            case AdviserOrderColumn.ModifiedTime:
                query = descendence
                    ? query.OrderByDescending(a => a.ModifiedTime)
                    : query.OrderBy(a => a.ModifiedTime);
                break;
        }

        return await query.Skip((int)offset).Take((int)count).ToArrayAsync();
    }

    public void Update(InsuranceAdviser adviser, IInsuranceAdviserRequest data)
    {
        _ = data.To(adviser, _context);
    }

    public async Task AcceptAgreements(
        Ulid adviserId,
        IEnumerable<Agreements> agreementIds
    )
    {
        foreach (
            var agreementId in agreementIds.Where(
                ag =>
                    !_context.AdviserAgreements
                        .Where(
                            ad =>
                                ad.InsuranceAdviserId == adviserId
                                && ad.Agreement == ag
                        )
                        .Any()
            )
        )
        {
            _model.Create(
                new AdviserAgreement
                {
                    InsuranceAdviserId = adviserId,
                    Agreement = agreementId,
                    ConfirmDate = DateTime.UtcNow
                }
            );
        }

        await _context.SaveChangesAsync();

        var adviser = await _model.SingleAsync(
            _context.InsuranceAdvisers
                .Include(a => a.Agreements)
                .Where(a => a.Id == adviserId)
        );

        adviser.AgreementsAccepted =
            AdviserProfileCommonLogic.IsAcceptedRequiredAgreements(
                adviser.Agreements
            );
        await _context.SaveChangesAsync();
    }

    public async Task RemoveAgreements(
        Ulid adviserId,
        IEnumerable<Agreements> agreementIds
    )
    {
        foreach (
            var agreement in _context.AdviserAgreements.Where(
                ag => ag.AdviserId == adviserId
            )
        )
        {
            if (agreementIds.Contains(agreement.Agreement))
                _context.Remove(agreement);
        }
        await _context.SaveChangesAsync();

        var adviser = await _model.SingleAsync(
            _context.InsuranceAdvisers
                .Include(a => a.Agreements)
                .Where(a => a.Id == adviserId)
        );

        adviser.AgreementsAccepted =
            AdviserProfileCommonLogic.IsAcceptedRequiredAgreements(
                adviser.Agreements
            );
        await _context.SaveChangesAsync();
    }

    public static void UpdateEmployerHistories(
        InsuranceAdviser adviser,
        IEnumerable<InsuranceAdviserEmployerRequest> data
    )
    {
        if (data.Where(item => item.IsPrimaryCompany).Count() > 1)
            throw new AdviserLimitPrimaryCompanyError();

        adviser.Employers.Clear();
        foreach (InsuranceAdviserEmployerRequest? item in data)
        {
            adviser.Employers.Add(
                item.To(
                    new InsuranceAdviserEmployer
                    {
                        Id = Ulid.NewUlid(),
                        Adviser = adviser,
                        CompanyName = item.CompanyName,
                        EmploymentYear = item.EmploymentYear,
                        RetirementYear = item.EmploymentYear,
                        IsPrimaryCompany = item.IsPrimaryCompany
                    }
                )
            );
        }
    }

    public async Task<
        IEnumerable<InsuranceAdviserEmployerResponse>
    > GetEmployers(InsuranceAdviser adviser) =>
        await _context.InsuranceAdviserEmployers
            .Where(f => f.Adviser.Id == adviser.Id)
            .OrderBy(f => f.RetirementYear)
            .Select(f => new InsuranceAdviserEmployerResponse(f))
            .ToListAsync();

    public async Task UpdateCustomerStatics(
        Ulid adviserId,
        CustomerStaticsRequest data
    )
    {
        var adviser = await _context.InsuranceAdvisers
            .Include(a => a.CustomerStatics)
            .Where(c => c.Id == adviserId)
            .SingleAsync();

        if (adviser.CustomerStatics == null)
        {
            adviser.CustomerStatics = new CustomerStatics();
            adviser.CustomerStatics = _model.Create(
                data.To(adviser.CustomerStatics)
            );
            return;
        }
        _model.Update(data.To(adviser.CustomerStatics));
    }

    public Task<InsuranceAdviser> GetPublicAdviser(Ulid adviserId)
    {
        return _model.SingleAsync(
            PublishableAdviserQuery().Where(a => a.Id == adviserId)
        );
    }

    public Task<InsuranceAdviser> GetByStaff(Ulid adviserId)
    {
        return _model.FindAsync(_context.InsuranceAdvisers, adviserId);
    }

    public async Task UpdateAccessCount(Ulid adviserId)
    {
        var adviser = await _context.InsuranceAdvisers.FindAsync(adviserId);
        if (adviser != null)
        {
            adviser.ProfileAccessCount += 1;
            await _context.SaveChangesAsync();
        }
    }

    public async Task<CustomerStaticsResponse> GetCustomerStatics(
        Ulid adviserId
    )
    {
        var adviser = await _model.SingleAsync(
            _context.InsuranceAdvisers.Where(a => a.Id == adviserId)
        );

        return new CustomerStaticsResponse(adviser?.CustomerStatics);
    }

    private IQueryable<InsuranceCustomerEvaluation> PublishDateCustomerEvaluationsQuery(
        Ulid adviserId
    ) =>
        _context.InsuranceCustomerEvaluations
            .Where(c => c.AdviserId == adviserId)
            .Where(c => c.PublishDate <= DateTime.UtcNow);

    private IQueryable<InsuranceCustomerEvaluation> CustomerEvaluationsQuery(
        Ulid adviserId,
        string? text
    )
    {
        IQueryable<InsuranceCustomerEvaluation> query =
            PublishDateCustomerEvaluationsQuery(adviserId)
                .Where(c => c.AdviserId == adviserId);
        if (!string.IsNullOrEmpty(text))
            query = query.Where(
                a =>
                    a.CreatedUserEmail.Contains(text)
                    || a.Description.Contains(text)
                    || a.ReviewRate.ToString().Contains(text)
            );
        return query;
    }

    public List<CustomerEvaluationResponse> GetPublishCustomerEvaluations(
        Ulid adviserId,
        string? text,
        uint offset = 0,
        uint count = 20
    ) =>
        CustomerEvaluationsQuery(adviserId, text)
            .OrderByDescending(a => a.ModifiedTime)
            .Skip((int)offset)
            .Take((int)count)
            .Select(c => new CustomerEvaluationResponse(c))
            .ToList();

    public int CountPublishCustomerEvaluations(
        Ulid adviserId,
        string? text,
        uint offset = 0,
        uint count = 20
    ) =>
        CustomerEvaluationsQuery(adviserId, text)
            .Skip((int)offset)
            .Take((int)count)
            .Count();

    public async Task<AdviserCustomerEvaluationResponse> GetAdviserCustomerEvaluationsStatics(
        Ulid adviserId
    )
    {
        var customerEvaluations = await PublishDateCustomerEvaluationsQuery(
                adviserId
            )
            .ToArrayAsync();
        return new AdviserCustomerEvaluationResponse(customerEvaluations);
    }

    public async Task<InsuranceAdviser> GetOrCreateAdviser(User user)
    {
        if (user.InsuranceAdviser == null)
        {
            user.InsuranceAdviser = _model.Create(
                new InsuranceAdviser
                {
                    SequenceId = await _seq.GetValue(
                        SequenceNumberId.InsuranceAdviser
                    ),
                }.InitializeRequired(_context)
            );
            await _context.SaveChangesAsync();
        }

        return user.InsuranceAdviser;
    }

    public async Task<InsuranceAdviserResponse> ToAdviserResponse(
        InsuranceAdviser adviser
    )
    {
        return new InsuranceAdviserResponse(
            adviser,
            await _commonLogic.GetPhotoUrls(adviser),
            query: _context.VisitorRecords.Where(v => v.AdviserId == adviser.Id)
        // _context.ProfileItemInformations.ToArray()
        );
    }

    public async Task UpdateLocation(
        InsuranceAdviser a,
        AdviserLocationRequest data
    )
    {
        _model.Update(data.To(a));
        await _context.SaveChangesAsync();
    }

    public async Task UpdateManagement(
        Ulid adviserId,
        InsuranceAdviserManagementRequest data
    )
    {
        _context.ChangeTracker.LazyLoadingEnabled = false;
        var adviser = await _model.SingleAsync(
            _context.InsuranceAdvisers
                .Include(a => a.User)
                .Where(a => a.Id == adviserId)
        );

        var batch = _context.InsuranceBatchIntroductionSetting
            .Where(a => a.InsuranceAdviserId == adviser.Id)
            .FirstOrDefault();
        if (batch == null)
        {
            batch = InsuranceBatchIntroductionSetting.GetDefault(adviser);
            _model.Create(batch);
            _context.SaveChanges();
        }

        data.To(adviser, batch, _context);
        adviser.ModifiedTime = DateTime.UtcNow;

        await _context.SaveChangesAsync();
    }

    public IQueryable<InsuranceAdviser> AdviserRelatedQuery(
        InsuranceAdviser adviser
    ) =>
        _context.InsuranceAdvisers
            .Where(a => a.Id != adviser.Id)
            .Where(a => a.InsuranceCompanyId == adviser.InsuranceCompanyId);

    public int CountAdviserRelated(InsuranceAdviser adviser) =>
        AdviserRelatedQuery(adviser).Count();

    public async IAsyncEnumerable<InsuranceAdviserPublicResponse> GetAdviserRelated(
        InsuranceAdviser adviser,
        uint offset = 0,
        uint count = 20
    )
    {
        foreach (
            var related in await AdviserRelatedQuery(adviser)
                .OrderByDescending(a => a.CreatedTime)
                .Skip((int)offset)
                .Take((int)count)
                .ToArrayAsync()
        )
        {
            yield return new InsuranceAdviserPublicResponse(
                related,
                await _commonLogic.GetPhotoUrls(related),
                _commonLogic.GetPhotoCollectionUrls(related)
            );
        }
    }

    public async Task<InsuranceAdviserPublicResponse> GetPublicAdviserBySequenceId(
        int sequenceId
    )
    {
        var adviser = await _model.SingleAsync(
            PublishableAdviserQuery().Where(a => a.SequenceId == sequenceId)
        );
        return new InsuranceAdviserPublicResponse(
            adviser,
            await _commonLogic.GetPhotoUrls(adviser),
            _commonLogic.GetPhotoCollectionUrls(adviser)
        );
    }

    public async Task<int> GetSequenceIdFromId(Ulid adviserId)
    {
        var adviser = await _model.SingleAsync(
            PublishableAdviserQuery().Where(a => a.Id == adviserId)
        );
        return adviser.SequenceId;
    }

    public async Task<Ulid> GetUserId(Ulid adviserId)
    {
        var user = await _context.Users
            .Where(u => u.InsuranceAdviserId == adviserId)
            .SingleAsync();
        return user.Id;
    }

    public async Task<InsuranceAdviser> GetBySequenceId(int sequenceId)
    {
        return await _model.SingleAsync(
            _context.InsuranceAdvisers.Where(a => a.SequenceId == sequenceId)
        );
    }

    public async Task<InsuranceAdviser> Get(Ulid Id)
    {
        return await _model.SingleAsync(
            _context.InsuranceAdvisers.Where(a => a.Id == Id)
        );
    }
}
