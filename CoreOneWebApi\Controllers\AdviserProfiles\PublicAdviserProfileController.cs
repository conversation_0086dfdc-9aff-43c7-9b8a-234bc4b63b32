// file deepcode ignore AntiforgeryTokenDisabled: <PERSON><PERSON> Page is not Used

using Adnavi.Domain;
using Adnavi.Domain.DTOs.Profiles.AdviserProfiles;
using Adnavi.Domain.DTOs.Profiles.CustomerEvaluations;
using Adnavi.Domain.DTOs.Profiles.IfaCompanyProfiles;
using Adnavi.Domain.Logics.Profiles.AdviserProfiles;
using Adnavi.Domain.Models;
using Adnavi.Domain.Models.Commons;
using Adnavi.Domain.Models.Profiles.AdviserProfiles;
using Adnavi.Domain.Models.Profiles.IfaCompanyProfiles;
using Adnavi.Domain.Models.Profiles.InvestorProfiles;
using Adnavi.Utils;
using AdNavi.CoreOneWebApi.Commons;
using AngleSharp.Common;
using ConfigCat.Client;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using NUlid;

namespace AdNavi.CoreOneWebApi.Controllers.AdviserProfiles;

[Route("[controller]")]
public class PublicAdviserProfileController
    : PublicBaseController<PublicAdviserProfileController>
{
    private readonly AdviserProfileLogic _profile;
    private readonly AdviserProfileCommonLogic _common;

    // private readonly ProfileItemInformation[] _profileItemInformations;

    public PublicAdviserProfileController(
        ILogger<PublicAdviserProfileController> logger,
        IOptions<CoreOneWebApiSettings> settings,
        IConfigCatClient configCatClient,
        ModelUtils model,
        S3Utils s3,
        AdnaviDomainContext context,
        AdviserProfileLogic profile,
        AdviserProfileCommonLogic commonLogic
    )
        : base(logger, settings, configCatClient, model, s3, context)
    {
        _profile = profile;
        _common = commonLogic;

        // _profileItemInformations = _context.ProfileItemInformations.ToArray();
    }

    [HttpGet("count")]
    public Task<int> GetCount(
        [FromQuery] List<Prefectures> areas,
        [FromQuery] List<ConsultationTypes> consultations,
        [FromQuery] List<CustomerTypes> mainCustomerTypes,
        [FromQuery] List<AssetRanges> assetRanges,
        [FromQuery] List<AdviserWorkTypes> adviserWorkTypes,
        [FromQuery] List<Days> workDays,
        [FromQuery] List<ContactMethods> availableContactMethods,
        [FromQuery] List<int> cooperativeSecurityIds,
        [FromQuery] string? text
    ) =>
        _profile.GetCount(
            areas: areas,
            consultations: consultations,
            text: text,
            publishedOnly: false,
            mainCustomerTypes: mainCustomerTypes,
            assetRanges: assetRanges,
            adviserWorkTypes: adviserWorkTypes,
            workDays: workDays,
            availableContactMethods: availableContactMethods,
            cooperativeSecurityIds: cooperativeSecurityIds,
            isBatchIntroductionSetting: false,
            isStaffIntroductionOnly: false,
            ifaCompanyId: null
        );

    [HttpPost("count2")]
    public Task<int> GetCount2(
        [FromBody] AdviserProfileSearchRequest request
    ) =>
        _profile.GetCount2(
            areas: request.Areas?.ToList(),
            consultations: request.Consultations?.ToList(),
            customerTypes: request.MainCustomerTypes?.ToList(),
            assetRanges: request.AssetRanges?.ToList(),
            adviserWorkTypes: request.AdviserWorkTypes?.ToList(),
            workDays: request.WorkDays?.ToList(),
            availableContactMethods: request.AvailableContactMethods?.ToList(),
            text: request.Text,
            offset: request.Offset ?? 0,
            count: request.Count ?? 20,
            orderColumn: request.OrderColumn ?? AdviserOrderColumn.ModifiedTime,
            descendence: request.Descendence ?? false,
            publishedOnly: true,
            cooperativeSecurityIds: request.CooperativeSecurityIds?.ToList(),
            nightAvailable: request.NightAvailable ?? false,
            fromSecurities: request.FromSecurities ?? false,
            fromBank: request.FromBank ?? false,
            ifaCompanyId: request.IfaCompanyId
        );

    [HttpGet]
    public async IAsyncEnumerable<AdviserPublicResponse> Search(
        [FromQuery] List<Prefectures> areas,
        [FromQuery] List<ConsultationTypes> consultations,
        [FromQuery] List<CustomerTypes> mainCustomerTypes,
        [FromQuery] List<AssetRanges> assetRanges,
        [FromQuery] List<AdviserWorkTypes> adviserWorkTypes,
        [FromQuery] List<Days> workDays,
        [FromQuery] List<ContactMethods> availableContactMethods,
        [FromQuery] List<int> cooperativeSecurityIds,
        [FromQuery] string? text,
        [FromQuery] uint offset = 0,
        [FromQuery] uint count = 20,
        [FromQuery]
            AdviserOrderColumn orderColumn = AdviserOrderColumn.ModifiedTime,
        [FromQuery] bool descendence = false,
        [FromQuery] bool nightAvailable = false,
        [FromQuery] bool fromSecurities = false,
        [FromQuery] bool fromBank = false
    )
    {
        var query = _profile.Search(
            areas: areas,
            consultations: consultations,
            customerTypes: mainCustomerTypes,
            assetRanges: assetRanges,
            adviserWorkTypes: adviserWorkTypes,
            workDays: workDays,
            availableContactMethods: availableContactMethods,
            text: text,
            offset: offset,
            count: count,
            orderColumn: orderColumn,
            descendence: descendence,
            publishedOnly: true,
            cooperativeSecurityIds: cooperativeSecurityIds,
            nightAvailable: nightAvailable,
            fromSecurities: fromSecurities,
            fromBank: fromBank
        );

        foreach (var a in query.ToArray())
        {
            yield return new AdviserPublicResponse(
                a,
                _context,
                await _common.GetPhotoUrls(a),
                _common.GetPhotoCollectionUrls(a)
            // _profileItemInformations
            );
        }
    }

    [HttpPost("search2")]
    public async Task<IEnumerable<AdviserPublicResponse>> Search2(
        [FromBody] AdviserProfileSearchRequest request
    )
    {
        _context.ChangeTracker.LazyLoadingEnabled = false;
        _context.ChangeTracker.QueryTrackingBehavior =
            QueryTrackingBehavior.NoTracking;

        var query = _profile.Search(
            areas: request.Areas?.ToList(),
            consultations: request.Consultations?.ToList(),
            customerTypes: request.MainCustomerTypes?.ToList(),
            assetRanges: request.AssetRanges?.ToList(),
            adviserWorkTypes: request.AdviserWorkTypes?.ToList(),
            workDays: request.WorkDays?.ToList(),
            availableContactMethods: request.AvailableContactMethods?.ToList(),
            text: request.Text,
            offset: request.Offset ?? 0,
            count: request.Count ?? 20,
            orderColumn: request.OrderColumn ?? AdviserOrderColumn.ModifiedTime,
            descendence: request.Descendence ?? false,
            publishedOnly: true,
            cooperativeSecurityIds: request.CooperativeSecurityIds?.ToList(),
            nightAvailable: request.NightAvailable ?? false,
            fromSecurities: request.FromSecurities ?? false,
            fromBank: request.FromBank ?? false,
            ifaCompanyId: request.IfaCompanyId,
            isBatchIntroductionSetting: request.IsEnabledBatchIntroductionSetting
        );

        var advisers = await AdviserPublicResponse.FromQuery(
            query,
            _common,
            _context
        );

        return advisers;
    }

    [HttpGet("{adviserId}")]
    public async Task<AdviserPublicResponse> Get(Ulid adviserId)
    {
        var adviser = await _profile.GetPublicAdviser(adviserId);
        return new AdviserPublicResponse(
            adviser,
            _context,
            await _common.GetPhotoUrls(adviser),
            _common.GetPhotoCollectionUrls(adviser)
        // _profileItemInformations
        );
    }

    [HttpPost("batch")]
    public async Task<IEnumerable<AdviserPublicResponse>> GetBatch(
        List<Ulid> adviserIds
    )
    {
        var advisers = await _profile.GetPublicAdvisersBatch(adviserIds);
        var responses = new List<AdviserPublicResponse>();

        foreach (var adviser in advisers)
        {
            var response = new AdviserPublicResponse(
                adviser,
                _context,
                await _common.GetPhotoUrls(adviser),
                _common.GetPhotoCollectionUrls(adviser)
            );
            responses.Add(response);
        }

        return responses;
    }

    [HttpGet("sequence/{sequenceId}")]
    public async Task<AdviserPublicResponse> GetBySequenceId(int sequenceId)
    {
        return await _profile.GetPublicAdviserBySequenceId(sequenceId);
    }

    [HttpGet("{adviserId}/sequence-id")]
    public async Task<int> GetSequenceIdFromId(Ulid adviserId)
    {
        return await _profile.GetSequenceIdFromId(adviserId);
    }

    [HttpGet("old/{oldId}")]
    public async Task<Ulid> GetIdFromOld(int oldId)
    {
        var adviser = await _profile.GetAdviserFromOldId(oldId);
        return adviser.Id;
    }

    // [HttpPost("count")]
    // public Task UpdateAccessCount(Ulid adviserId) =>
    //     _profile.UpdateAccessCount(adviserId);
    [HttpPost("count")]
    public void UpdateAccessCount(Ulid adviserId) { }

    [HttpGet("{adviserId}/customer-statics")]
    public Task<CustomerStaticsResponse> GetCustomerStatics(Ulid adviserId) =>
        _profile.GetCustomerStatics(adviserId);

    [HttpGet("{adviserId}/customer-evaluations")]
    public List<CustomerEvaluationResponse> GetCustomerEvaluations(
        Ulid adviserId,
        string? text,
        uint offset = 0,
        uint count = 20
    ) => _profile.GetPublishCustomerEvaluations(adviserId, text, offset, count);

    [HttpGet("{adviserId}/customer-evaluations/count")]
    public int CountCustomerEvaluations(
        Ulid adviserId,
        string? text,
        uint offset = 0,
        uint count = 20
    ) =>
        _profile.CountPublishCustomerEvaluations(
            adviserId,
            text,
            offset,
            count
        );

    [HttpGet("{adviserId}/customer-evaluations/statics")]
    public async Task<AdviserCustomerEvaluationResponse> CustomerEvaluationsStatics(
        Ulid adviserId
    ) => await _profile.GetAdviserCustomerEvaluationsStatics(adviserId);

    [HttpGet("{adviserId}/employer-histories")]
    public IEnumerable<AdviserEmployerResponse> GetEmployerHistories(
        Ulid adviserId
    )
    {
        var formerEmploys = _context.AdviserEmployers
            .Where(f => f.Adviser.Id == adviserId)
            .OrderBy(f => f.RetirementYear)
            .Select(f => new AdviserEmployerResponse(f));
        return formerEmploys;
    }

    [HttpGet("{adviserId}/Related")]
    public async Task<
        IAsyncEnumerable<AdviserPublicResponse>
    > GetAdviserRelated(Ulid adviserId, uint offset = 0, uint count = 20)
    {
        var adviser = await _profile.GetPublicAdviser(adviserId);
        return _profile.GetAdviserRelated(adviser, offset, count);
    }

    [HttpGet("{adviserId}/Related/Count")]
    public async Task<int> CountAdviserRelated(Ulid adviserId)
    {
        var adviser = await _profile.GetPublicAdviser(adviserId);
        return _profile.CountAdviserRelated(adviser);
    }

    [HttpPost("OrderColumn/Count")]
    public async Task<int> CountAdviserOrderColumn(
        [FromBody] AdviserProfileOrderColumnCountRequest request
    )
    {
        var adviser = await _profile.GetPublicAdviser(request.AdviserId);
        return _profile.CountAdviserOrderColumn(adviser, request.OrderColumn);
    }

    [HttpGet("{adviserId}/access-request-rank")]
    public async Task<int> GetAccessRank(Ulid adviserId)
    {
        var adviser = await _profile.GetPublicAdviser(adviserId);
        return _profile.GetAdviserRankByAccessCount(adviser);
    }
}
