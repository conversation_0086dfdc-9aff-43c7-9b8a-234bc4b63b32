using Adnavi.Domain.Models;
using Adnavi.Domain.Models.Invoices.MatchingInvoices.FeeTables;
using Adnavi.Domain.DTOs.Invoices.MatchingInvoices;
using NUlid;
using Adnavi.Utils;

namespace Adnavi.Domain.Logics.Invoices.MatchingInvoices
{
    /// <summary>
    /// 紹介手数料ロジック
    /// </summary>
    public class ReferralFeeLogic
    {
        private readonly AdnaviDomainContext _context;
        private readonly ModelUtils _model;

        public ReferralFeeLogic(AdnaviDomainContext context, ModelUtils model)
        {
            _context = context;
            _model = model;
        }

        public ICollection<IfaReferralFee> Search(Ulid ifaCompanyId)
        {
            return _context.IfaReferralFees
                .Where(a => a.IfaCompanyId == ifaCompanyId)
                .OrderByDescending(a => a.StartDate)
                .ToArray();
        }

        public async Task<IfaReferralFee> Get(Ulid referralFeeId)
        {
            return await _model.SingleAsync(
                _context.IfaReferralFees.Where(a => a.Id == referralFeeId)
            );
        }

        public async Task Update(Ulid Id, IfaReferralFeeData data)
        {
            var referralFee = await _model.SingleAsync(
                _context.IfaReferralFees.Where(a => a.Id == Id)
            );
            referralFee = data.To(referralFee);
        }

        public IfaReferralFee GetReferralFee(Ulid ifaCompanyId)
        {
            var nowDate = DateOnly.FromDateTime(
                DateUtils.UtcToJst(DateTime.UtcNow)
            );
            return _context.IfaReferralFees
                    .Where(
                        a =>
                            a.IfaCompanyId == ifaCompanyId
                            && a.StartDate <= nowDate
                    )
                    .OrderByDescending(a => a.StartDate)
                    .FirstOrDefault() ?? new IfaReferralFee();
        }
    }
}
