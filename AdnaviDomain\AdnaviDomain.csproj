﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <RootNamespace>Adnavi.Domain</RootNamespace>
    <SuppressTfmSupportBuildWarnings>true</SuppressTfmSupportBuildWarnings>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <WarningLevel>4</WarningLevel>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <None Remove="Logics\" />
    <None Remove="VerificationCodeData\" />
    <None Remove="NUlid" />
    <None Remove="Microsoft.EntityFrameworkCore" />
    <None Remove="Microsoft.EntityFrameworkCore.Relational" />
    <None Remove="Microsoft.EntityFrameworkCore.Tools" />
    <None Remove="Pomelo.EntityFrameworkCore.MySql" />
    <None Remove="BCrypt.Net-Next" />
    <None Remove="Serilog" />
    <None Remove="Serilog.Sinks.Console" />
    <None Remove="Serilog.Exceptions" />
    <None Remove="Serilog.Enrichers.Thread" />
    <None Remove="Serilog.Enrichers.Memory" />
    <None Remove="MimeKit" />
    <None Remove="Amazon.S3" />
    <None Remove="AWSSDK.S3" />
    <None Remove="AWSSDK.CognitoIdentityProvider" />
    <None Remove="MailKit" />
    <None Remove="CsvHelper" />
    <None Remove="Scriban" />
    <None Remove="resources\Domain\" />
    <None Remove="resources\Domain\Profiles\" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="VerificationCodeData\" />
    <Folder Include="resources\Domain\" />
    <Folder Include="resources\Domain\Profiles\" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Auth0.ManagementApi" Version="7.17.4" />
    <PackageReference Include="AWSSDK.Athena" Version="3.7.303.14" />
    <PackageReference Include="AWSSDK.CloudFront" Version="3.7.404.43" />
    <PackageReference Include="MathNet.Numerics" Version="5.0.0" />
    <PackageReference Include="Google.Apis.Calendar.v3" Version="1.69.0.3746" />
    <PackageReference Include="Google.Apis.Oauth2.v2" Version="1.68.0.1869" />
    <PackageReference Include="Microsoft.AspNetCore.DataProtection" Version="6.0.36" />
    <PackageReference Include="Microsoft.AspNetCore.DataProtection.EntityFrameworkCore" Version="6.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.2.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Proxies" Version="6.0.6" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="6.0.1" />
    <PackageReference Include="Microsoft.Graph" Version="5.78.0" />
    <PackageReference Include="NUlid" Version="1.7.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="6.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="6.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.HttpOverrides" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore" Version="2.2.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.6">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="6.0.1" />
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="Serilog" Version="3.0.1" />
    <PackageReference Include="MimeKit" Version="4.7.1" />
    <PackageReference Include="AWSSDK.S3" Version="3.7.9.18" />
    <PackageReference Include="AWSSDK.CognitoIdentityProvider" Version="3.7.4.7" />
    <PackageReference Include="MailKit" Version="3.3.0" />
    <PackageReference Include="CsvHelper" Version="28.0.1" />
    <PackageReference Include="Scriban" Version="5.5.2" />
    <PackageReference Include="AWSSDK.CloudWatch" Version="**********" />
    <PackageReference Include="ConfigCat.Client" Version="8.2.0" />
    <PackageReference Include="Twilio" Version="7.8.4" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\AdnaviUtils\AdnaviUtils.csproj" />
  </ItemGroup>
  <ItemGroup>
    <None Update="resources\Domain\Accounts\OrganizationMembershipScopes.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Consultations\ConsultationRequests\PointStatuses.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\CardProfiles\InternationalCardBrands.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\CardProfiles\CardPaymentMethods.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\CardProfiles\CardInsurances.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\CardProfiles\CardRankTypes.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\CardProfiles\ComprehensiveAssessmentPoints.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\CardProfiles\Recommends.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\PersonalityTraits\levelingAdviser.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\PersonalityTraits\levelingInvestor.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\PersonalityTraits\levelingInvestorShort.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\PersonalityTraits\PersonalityTypes.enum.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\PersonalityTraits\questionsAdviser.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\PersonalityTraits\questionsInvestor.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\PersonalityTraits\questionsInvestorShort.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\PersonalityTraits\standardizationAdviser.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\PersonalityTraits\standardizationInvestor.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\PersonalityTraits\standardizationInvestorShort.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\PersonalityTraits\TraitText.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Invoices\RecruitInvoices\RecruiterInvoiceStatuses.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\AdviserProfiles\AcademicBackgrounds.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\AdviserProfiles\AdviserLicenses.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\AdviserProfiles\AdviserTypes.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\AdviserProfiles\AdviserWorkTypes.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\AdviserProfiles\Agreements.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\AdviserProfiles\AnnualIncomes.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\AdviserArticles\ArticleCategories.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\AdviserProfiles\AwardTypes.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\AdviserProfiles\CareerTypes.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\AdviserProfiles\ConsultationTypes.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\AdviserProfiles\ContactMethods.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\AdviserProfiles\CustomerTypes.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\AdviserProfiles\Hobbies.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\AdviserProfiles\ProfileItemTypes.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\AdviserProfiles\WorkTimes.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\IfaCompanyProfiles\BusinessTypes.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\IfaCompanyProfiles\CompanyLicenses.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\IfaCompanyProfiles\ContractTypes.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\IfaCompanyProfiles\OccupationTypes.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\IfaCompanyProfiles\SecuritiesCompanies.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\InvestorProfiles\AnnualIncomes.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\InvestorProfiles\AssetRanges.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\InvestorProfiles\ConsultationRequestTypes.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\InvestorProfiles\InvestorOccupationTypes.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Consultations\BatchIntroductions\FirstContactMethodTypes.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Consultations\ConsultationRequests\InvestmentPurposes.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Consultations\ConsultationRequests\FavorableAdviserExperiences.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Commons\Agreements.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Commons\Days.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Commons\Prefectures.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Commons\Regions.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Commons\WorkTimes.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Consultations\ConsultationRequests\ConsultationRequestTypes.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Commons\GenderTypes.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Commons\WarekiList.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\InvestorProfiles\AdviserPreferences.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\AdviserProfiles\GeneralEmployer.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\Profiles\AdviserProfiles\AdviserPhotoCollections.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\CardLoans\CardLoanOrganizationTypes.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\CardLoans\BorrowMethods.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="resources\Domain\CardLoans\PayBackMethods.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>
