using System;

using System.Linq;
using System.Threading.Tasks;
using Adnavi.Domain.Models.Common;
using Adnavi.Utils.Exceptions;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;

namespace Adnavi.Domain.Logics.Common.GeneralSettings;

public class GeneralSettingsLogic
{
    private readonly AdnaviDomainContext _context;

    public GeneralSettingsLogic(AdnaviDomainContext context)
    {
        _context = context;
    }

    public async Task<object?> GetGeneralSetting(GeneralSettingTypes id)
    {
        var jsonContents = await _context.GeneralSettings
            .Where(x => x.Id == id)
            .Select(x => x.JsonContents)
            .FirstOrDefaultAsync();

        if (jsonContents != null)
        {
            try
            {
                jsonContents = JToken.Parse(jsonContents).ToString();
            }
            catch
            {
                throw new NotJsonTypeException();
            }
        }
        return jsonContents;
    }

    public async Task UpdateGeneralSetting(
        GeneralSettingTypes id,
        object jsonContents
    )
    {
        var jsonContentsString = jsonContents.ToString();
        try
        {
            jsonContentsString = JToken.Parse(jsonContentsString!).ToString();
        }
        catch
        {
            throw new NotJsonTypeException();
        }

        var settings = await _context.GeneralSettings
            .Where(x => x.Id == id)
            .FirstOrDefaultAsync();

        if (settings == null)
        {
            settings = new GeneralSetting
            {
                Id = id,
                JsonContents = jsonContentsString
            };
            _context.GeneralSettings.Add(settings);
        }
        else
        {
            settings.JsonContents = jsonContentsString;
        }

        await _context.SaveChangesAsync();
    }
}
