using Adnavi.Domain.Logics.AssetManagementSimulations;
using Adnavi.Utils.Models;

namespace Adnavi.Domain.DTOs.AssetManagementSimulations;

public class PublicProbabilityResponse : IDataTransferObject
{
    /// <summary> 目標金融資産額 </summary>
    public long TargetFinancialAssetAmount { get; set; }

    /// <summary> 目標達成確率 </summary>
    public double Probability { get; set; }

    /// <summary> 目標達成時の年齢 </summary>
    public int TargetAge { get; set; }

    /// <summary>目標達成確率のコメント</summary>
    public ProbabilityComments ProbabilityComment { get; set; }

    /// <summary> リスク許容度 </summary>
    public RiskTolerances RiskTolerance { get; set; }
}
