﻿using Adnavi.Domain.Models.Invoices.MatchingInvoices;
using Adnavi.Utils;
using Adnavi.Utils.Models;
using CsvHelper.Configuration.Attributes;
using Microsoft.EntityFrameworkCore;
using NUlid;

namespace Adnavi.Domain.Logics.Invoices.MatchingInvoices;

public sealed class MatchingInvoiceCsvRecordOnInvoiceMonth
{
    [Name("送客送信日時")]
    public string CreatedTime { get; set; }

    [Name("氏名")]
    public string BatchName { get; set; }

    [Name("担当者")]
    public string AdviserName { get; set; }

    [Name("種別")]
    public string? InvoiceType { get; set; }

    [Name("請求費用")]
    public long? InvoiceAmount { get; set; }

    [Name("面談日")]
    public string? InterviewDate { get; set; }

    private MatchingInvoiceCsvRecordOnInvoiceMonth() { }

    public static async Task<
        IEnumerable<MatchingInvoiceCsvRecordOnInvoiceMonth>
    > FromQuery(
        AdnaviDomainContext context,
        YearAndMonth invoiceMonth,
        Ulid? IfaCompanyId
    )
    {
        var timeZone = DateUtils.GetJapanTimeZoneInfo();
        var query = context.IntroductionInvoices.Where(
            invoice => invoice.InvoiceMonth == invoiceMonth
        );
        if (IfaCompanyId != null)
        {
            query = query.Where(
                invoice =>
                    invoice.MatchingInvoiceManagement.IfaCompanyId
                        == IfaCompanyId
                    && invoice.InvoiceStatus != InvoiceStatuses.Hold
            );
        }

        var result = query.Select(
            invoice =>
                new MatchingInvoiceCsvRecordOnInvoiceMonth
                {
                    CreatedTime = TimeZoneInfo
                        .ConvertTimeFromUtc(
                            invoice.MatchingInvoiceManagement.CreatedTime
                                ?? DateTime.MinValue,
                            timeZone
                        )
                        .ToString("yyyy/MM/dd"),
                    AdviserName = invoice
                        .MatchingInvoiceManagement
                        .Adviser!
                        .FullName,
                    BatchName = invoice
                        .MatchingInvoiceManagement
                        .BatchIntroduction
                        .Name,
                    InvoiceType = getInvoiceType(
                        invoice.InvoiceType.ToString()
                    ),
                    InvoiceAmount = invoice.InvoiceAmount,
                    InterviewDate =
                        invoice.MatchingInvoiceManagement.InterviewDate == null
                            ? ""
                            : TimeZoneInfo
                                .ConvertTimeFromUtc(
                                    invoice
                                        .MatchingInvoiceManagement
                                        .InterviewDate
                                        .Value,
                                    timeZone
                                )
                                .ToString("yyyy/MM/dd"),
                }
        );

        return await result.ToListAsync();
    }

    public static String getInvoiceType(String invoiceType)
    {
        if (invoiceType == "Refund")
        {
            return "返金";
        }
        else if (invoiceType == "Matching")
        {
            return "送客";
        }
        else if (invoiceType == "Interview")
        {
            return "面談";
        }
        else
        {
            return "";
        }
    }
}
