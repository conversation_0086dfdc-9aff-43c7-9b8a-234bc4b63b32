﻿using System.ComponentModel.DataAnnotations;
using Adnavi.Utils.Models;
using NUlid;

namespace Adnavi.Domain.Models.Profiles.AdviserArticles;

public class ArticleImage : IHasId
{
    /// <summary>ID</summary>
    [Required]
    public Ulid Id { get; set; }

    /// <summary>記事ID</summary>
    [Required]
    public Ulid ArticleId { get; set; }

    /// <summary>記事</summary>
    public virtual Article Article { get; set; }

    /// <summary>URL</summary>
    [Required]
    public string Url { get; set; }

    /// <summary>作成時間</summary>
    [Required]
    public DateTime CreatedTime { get; set; }
}
