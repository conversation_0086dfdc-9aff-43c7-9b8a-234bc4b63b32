﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Adnavi.Domain.Models.Accounts;
using Adnavi.Utils;
using Adnavi.Utils.Models;
using Microsoft.EntityFrameworkCore;
using NUlid;

namespace Adnavi.Domain.Models.Invoices.RecruitInvoices;

[Index(nameof(OrganizationId), nameof(YearMonth), IsUnique = true)]
public class RecruiterInvoice : IHasId
{
    /// <summary>ID</summary>
    [Required]
    public Ulid Id { get; set; }

    /// <summary>バージョン</summary>
    [Timestamp]
    public byte[]? RowVersion { get; set; }

    /// <summary>企業ID</summary>
    [Required]
    public Ulid OrganizationId { get; set; }

    /// <summary>企業</summary>
    public virtual Organization Organization { get; set; }

    /// <summary>年月</summary>
    [Required]
    public YearAndMonth YearMonth { get; set; }

    /// <summary>状態</summary>
    [Required]
    public RecruiterInvoiceStatuses Status { get; set; }

    /// <summary>金額</summary>
    public uint? Amount { get; set; }

    /// <summary>その他金額</summary>
    public uint? OtherAmount { get; set; }

    /// <summary>消費税</summary>
    public uint? Vat { get; set; }

    /// <summary>消費税率</summary>
    [Required]
    public byte VatPercent { get; set; }

    /// <summary>利益リスト</summary>
    public virtual ICollection<EmployeeProfit> Profits { get; set; }

    /// <summary>提出日</summary>
    public DateTime? SubmittedTime { get; set; }

    /// <summary>備考</summary>
    [Required]
    public string Remarks { get; set; }

    /// <summary>作成日</summary>
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public DateTime? CreatedTime { get; set; }

    /// <summary>修正日</summary>
    [DatabaseGenerated(DatabaseGeneratedOption.Computed)]
    public DateTime? ModifiedTime { get; set; }
}
