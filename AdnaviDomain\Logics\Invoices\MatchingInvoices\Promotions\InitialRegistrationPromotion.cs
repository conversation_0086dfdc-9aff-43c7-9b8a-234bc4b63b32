using Adnavi.Domain.Models.Consultations.BatchIntroductions;
using Adnavi.Domain.Models.Invoices.MatchingInvoices.FeeTables;
using Adnavi.Domain.Models.Profiles.AdviserProfiles;
using Adnavi.Utils;

namespace Adnavi.Domain.Logics.Invoices.MatchingInvoices.Promotions;

/// <summary>
/// 初期登録プロモーションロジック
/// </summary>
public class InitialRegistrationPromotion : IIfaPromotion
{
    private readonly AdnaviDomainContext _context;

    public string DisplayName => "初回登録キャンペーン";

    public InitialRegistrationPromotion(AdnaviDomainContext context)
    {
        _context = context;
    }

    /// <summary>
    /// プロモーションの対象か確認する
    /// </summary>
    /// <param name="adviser">対象のアドバイザー</param>
    /// <param name="introduction">紹介情報</param>
    /// <returns>対象者であればtrue</returns>
    public bool IsEligible(Adviser adviser, BatchIntroduction introduction)
    {
        var eventTime = introduction.CreatedTime;

        // 2023-12-1以降の紹介のみ対象
        if (DateUtils.ConvertJstDateOnly(eventTime) < new DateOnly(2023, 12, 1))
            return false;

        var firstEnabledTime = GetInitialRegistrationDate(
            _context,
            adviser,
            eventTime
        );

        if (firstEnabledTime == null)
            return false;

        var eligible = IsInPromotionPeriod(firstEnabledTime.Value, eventTime);
        return eligible;
    }

    public static DateTime? GetInitialRegistrationDate(
        AdnaviDomainContext context,
        Adviser adviser,
        DateTime eventTime
    )
    {
        // 紹介履歴から初回有効化日を確認する
        var firstEnabledTime = IntroductionSettingHistory
            .IntroductionEnabledQuery(
                context.IntroductionSettingHistories,
                adviser
            )
            .OrderBy(h => h.CreatedTime)
            .Select(h => (DateTime?)h.CreatedTime)
            .FirstOrDefault();

        {
            // 2023-12以前は初回有効日がわからないため一括紹介から最初に紹介された時を取得する
            var firstIntroductionTime = context.BatchIntroductions
                .Where(
                    i =>
                        i.Status == BatchIntroductionStatus.Introduction
                        && i.Advisers.Select(a => a.Id).Contains(adviser.Id)
                )
                .OrderBy(h => h.CreatedTime)
                .Select(h => (DateTime?)h.CreatedTime)
                .FirstOrDefault();

            // 古い方を採用
            if (
                firstIntroductionTime != null
                && (
                    firstEnabledTime == null
                    || firstEnabledTime > firstIntroductionTime
                )
            )
                firstEnabledTime = firstIntroductionTime;
        }

        return firstEnabledTime;
    }

    /// <summary>
    /// キャンペーン開始時刻(UTC)を取得する
    /// </summary>
    /// <param name="firstEnabledLocalDate"></param>
    /// <returns></returns>
    public static DateTime GetPromotionStartDateUtc(
        DateTime firstEnabledTime
    ) => DateUtils.ConvertJstStartTimeUtc(firstEnabledTime);

    /// <summary>
    /// キャンペーン終了日(UTC)を取得する
    /// </summary>
    /// <param name="firstEnabledLocalDate"></param>
    /// <returns></returns>
    public static DateTime GetPromotionEndDateUtc(DateTime firstEnabledTime) =>
        GetPromotionStartDateUtc(firstEnabledTime).AddDays(90).AddSeconds(-1);

    private bool IsInPromotionPeriod(
        DateTime firstEnabledTime,
        DateTime eventTime
    )
    {
        var startTime = GetPromotionStartDateUtc(firstEnabledTime);
        var endTime = GetPromotionEndDateUtc(firstEnabledTime);

        var eligible = eventTime >= startTime && eventTime <= endTime;
        return eligible;
    }

    /// <summary>
    /// 送客金額の取得
    /// </summary>
    public int? GetMatchingFee(Adviser adviser, BatchIntroduction introduction)
    {
        var fee = IfaReferralFee.PromotionMatchingFeeTable.GetFee(
            introduction.AssetRange
        );
        return fee;
    }

    /// <summary>
    /// 面談金額の取得
    /// </summary>
    public int? GetInterviewFee(Adviser adviser, BatchIntroduction introduction)
    {
        var fee = IfaReferralFee.PromotionInterviewFeeTable.GetFee(
            introduction.AssetRange
        );
        return fee;
    }
}
