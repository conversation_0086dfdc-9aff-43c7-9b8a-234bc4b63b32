using Adnavi.Domain.Models.Profiles.AdviserProfiles;
using Adnavi.Domain.Models.Profiles.InvestorProfiles;
using Adnavi.Utils.Models;
using Microsoft.EntityFrameworkCore;
using NUlid;

namespace Adnavi.Domain.Models.Consultations.BatchIntroductions;

/// <summary>
/// BatchIntroductionSetting
/// </summary>
/// <seealso cref="BatchIntroductionSettingData"/>
/// <seealso cref="StaffBatchIntroductionSettingData"/>
public class BatchIntroductionSetting : IHasId, IInvestorFilterFields
{
    public Ulid Id { get; set; }

    /// <summary>一括紹介の有効化</summary>
    public bool Enabled { get; set; }

    /// <summary>対象アドバイザー</summary>
    public Ulid AdviserId { get; set; }
    public virtual Adviser Adviser { get; set; }

    /// <summary>受付金融資産のリスト</summary>
    public virtual ICollection<AssetRange> AcceptAssetRanges { get; set; } =
        new List<AssetRange>();

    public uint? OlderLimit { get; set; }

    public uint? YoungerLimit { get; set; }

    public virtual ICollection<InvestorOccupationType> ExcludeOccupationTypes { get; set; } =
        new List<InvestorOccupationType>();

    public virtual ICollection<AdviserWorkType> ExcludeWorkTypes { get; set; } =
        new List<AdviserWorkType>();

    /// <summary>セカンドオピニオンを除く(未実装)</summary>
    public bool ExcludeSecondOpinion { get; set; }

    /// <summary>カスタマーサービスのみから紹介</summary>
    public bool StaffIntroductionOnly { get; set; }

    /// <summary>バイアス設定</summary>
    public int IntroductionRate { get; set; }

    public static BatchIntroductionSetting GetDefault(Adviser adviser)
    {
        return new BatchIntroductionSetting
        {
            Id = Ulid.NewUlid(),
            Adviser = adviser,
            AdviserId = adviser.Id,
            Enabled = false,
            AcceptAssetRanges = new List<AssetRange> { },
            StaffIntroductionOnly = false,
            IntroductionRate = 100,
        };
    }

    public static void SetupModel(ModelBuilder modelBuilder)
    {
        modelBuilder
            .Entity<BatchIntroductionSetting>()
            .HasMany(e => e.AcceptAssetRanges)
            .WithMany(c => c.BatchIntroductionSettings)
            .UsingEntity(
                j => j.ToTable("BatchIntroductionSettingAcceptAssetRanges")
            );
        modelBuilder
            .Entity<BatchIntroductionSetting>()
            .HasMany(e => e.ExcludeOccupationTypes)
            .WithMany(c => c.BatchIntroductionSettings)
            .UsingEntity(
                j => j.ToTable("BatchIntroductionSettingExcludeOccupationTypes")
            );
        modelBuilder
            .Entity<BatchIntroductionSetting>()
            .HasMany(e => e.ExcludeWorkTypes)
            .WithMany(c => c.BatchIntroductionSettings)
            .UsingEntity(
                j => j.ToTable("BatchIntroductionSettingExcludeWorkTypes")
            );
    }
}
