﻿using System.Web;
using Adnavi.Domain.Models;
using Adnavi.Domain.Models.Profiles.AdviserProfiles;
using Adnavi.Utils;
using Adnavi.Utils.Exceptions;
using Microsoft.EntityFrameworkCore;
using MimeKit;
using NUlid;
using Serilog;
using Adnavi.Domain.Logics.Consultations.BatchIntroductions;
using Adnavi.Domain.Models.Consultations.InsuranceBatchIntroductions;
using Adnavi.Domain.Models.Profiles.InsuranceCompanyProfiles;
using Adnavi.Domain.Models.Consultations.BatchIntroductions;
using Adnavi.Domain.Models.Accounts;
using Adnavi.Domain.DTOs.Consultations.BatchIntroductions;
using Adnavi.Domain.DTOs.Consultations.InsuranceBatchIntroductions;
using Adnavi.Domain.Logics.Common.EMails;
using CsvHelper.Configuration;
using System.Globalization;
using System.Text;
using CsvHelper;
using Microsoft.Extensions.Options;

namespace Adnavi.Domain.Logics.Consultations.InsuranceBatchIntroductions;

public class InsuranceBatchIntroductionManagementLogic
{
    private readonly InvestorsSettings _settings;
    private readonly AdnaviDomainContext _context;
    private readonly ModelUtils _model;
    private readonly EMailService _emailService;

    public InsuranceBatchIntroductionManagementLogic(
        IOptions<AdnaviDomainSettings> domainSettings,
        ModelUtils model,
        EMailService emailService,
        AdnaviDomainContext context
    )
    {
        _settings = domainSettings.Value.Investors;
        _context = context;
        _model = model;
        _emailService = emailService;
    }

    public async Task<MemoryStream> GetCsvData()
    {
        var config = new CsvConfiguration(CultureInfo.InvariantCulture)
        {
            HasHeaderRecord = true
        };

        // 紹介については、同じ日に同じメールアドレスからの紹介は1件として扱う
        var targetIds = InsuranceBatchIntroduction
            .QueryNotTest(_context.InsuranceBatchIntroductions)
            .Where(
                b => b.Status == InsuranceBatchIntroductionStatus.Introduction
            )
            .Select(
                b =>
                    new
                    {
                        b.Id,
                        b.EMail,
                        b.CreatedTime
                    }
            )
            .ToArray()
            .GroupBy(b => new { b.EMail, b.CreatedTime.Date })
            .Select(g => g.First().Id)
            .ToArray();

        var dataExport = InsuranceBatchIntroductionCsvData.FromQuery(
            _context.InsuranceBatchIntroductions.Where(
                b =>
                    targetIds.Contains(b.Id)
                    || b.Status == InsuranceBatchIntroductionStatus.Request
            )
        );

        var stream = new MemoryStream();
        using (
            var writeFile = new StreamWriter(
                stream,
                Encoding.UTF8,
                -1,
                leaveOpen: true
            )
        )
        {
            var csv = new CsvWriter(writeFile, config);
            csv.WriteHeader<InsuranceBatchIntroductionCsvData>();
            await csv.NextRecordAsync();
            await foreach (var b in dataExport)
            {
                csv.WriteRecord(b);
                await csv.NextRecordAsync();
            }
        }
        stream.Position = 0; // reset stream

        return stream;
    }

    public static bool CanUse(InsuranceAdviser adviser)
    {
        return adviser.InsuranceCompanyId != null
            && adviser.CompanyByCompanyIntroduction != null;
    }

    public async Task<InsuranceBatchIntroductionSettingData> GetSetting(
        InsuranceAdviser adviser
    )
    {
        var a = await _context.InsuranceBatchIntroductionSetting
            .Where(a => a.InsuranceAdviserId == adviser.Id)
            .SingleOrDefaultAsync();

        a ??= InsuranceBatchIntroductionSetting.GetDefault(adviser);
        return new InsuranceBatchIntroductionSettingData(a);
    }

    public void UpdateSetting(
        InsuranceAdviser adviser,
        InsuranceBatchIntroductionSettingData data,
        User user
    )
    {
        var batch = _context.InsuranceBatchIntroductionSetting
            .Where(a => a.InsuranceAdviserId == adviser.Id)
            .FirstOrDefault();
        if (batch == null)
        {
            batch = InsuranceBatchIntroductionSetting.GetDefault(adviser);
            _model.Create(batch);
            _context.SaveChanges();
        }
        _model.Update(data.To(batch, adviser.Id, _context));

        _model.Create(new IntroductionSettingHistory(batch, user));
        _context.SaveChanges();
    }

    private IQueryable<IntroductionSettingHistory> QueryIntroductionSettingHistory(
        string? text
    )
    {
        var query = _context.IntroductionSettingHistories.AsQueryable();
        if (!string.IsNullOrEmpty(text))
        {
            query = query.Where(
                i =>
                    i.AdviserName != null && i.AdviserName.Contains(text)
                    || i.InsuranceCompanyName != null
                        && i.InsuranceCompanyName.Contains(text)
                    || i.CreatedUserEMail != null
                        && i.CreatedUserEMail.Contains(text)
            );
        }
        return query;
    }

    public async Task<
        IEnumerable<IntroductionSettingHistoryData>
    > SearchHistory(string? text, int offset = 0, int count = 20)
    {
        var histories = await QueryIntroductionSettingHistory(text)
            .OrderByDescending(x => x.CreatedTime)
            .Skip(offset)
            .Take(count)
            .ToListAsync();
        return histories.Select(x => new IntroductionSettingHistoryData(x));
    }

    public async Task<int> SearchHistoryCount(string? text) =>
        await QueryIntroductionSettingHistory(text).CountAsync();

    public async Task<BatchIntroductionInsuranceCompanySettingData> GetInsuranceCompanySetting(
        InsuranceCompany insuranceCompany
    )
    {
        var a = await _context.BatchIntroductionInsuranceCompanySetting
            .Where(a => a.InsuranceCompanyId == insuranceCompany.Id)
            .SingleOrDefaultAsync();

        a ??= BatchIntroductionInsuranceCompanySetting.GetDefault(
            insuranceCompany
        );
        return new BatchIntroductionInsuranceCompanySettingData(a);
    }

    public void UpdateInsuranceCompanySetting(
        InsuranceCompany insuranceCompany,
        BatchIntroductionInsuranceCompanySettingData data
    )
    {
        var batch = _context.BatchIntroductionInsuranceCompanySetting
            .Where(a => a.InsuranceCompanyId == insuranceCompany.Id)
            .FirstOrDefault();
        if (batch == null)
        {
            batch = BatchIntroductionInsuranceCompanySetting.GetDefault(
                insuranceCompany
            );
            _model.Create(batch);
            _context.SaveChanges();
        }
        _model.Update(data.To(batch, insuranceCompany.Id, _context));
        _context.SaveChanges();
    }

    private IQueryable<InvestorKeyData> SearchQuery(string? name, string? eMail)
    {
        IQueryable<InsuranceBatchIntroduction> query =
            _context.InsuranceBatchIntroductions;
        if (!string.IsNullOrWhiteSpace(name))
        {
            query = query.Where(b => b.Name.Contains(name));
        }
        if (!string.IsNullOrWhiteSpace(eMail))
        {
            query = query.Where(b => b.EMail.Contains(eMail));
        }

        var groupedQuery = query
            .GroupBy(b => new { b.Name, b.EMail })
            .Select(
                g =>
                    new InvestorKeyData
                    {
                        Name = g.Key.Name,
                        EMail = g.Key.EMail,
                        StaffContacted = g.Any(b => b.StaffContacted),
                        // Assumes CreatedTime is the introduction time.
                        FirstIntroductionTime = g.Min(b => b.CreatedTime)
                    }
            )
            .OrderByDescending(b => b.FirstIntroductionTime);
        return groupedQuery;
    }

    public IEnumerable<InvestorKeyData> SearchKeys(
        string? name,
        string? eMail,
        uint offset,
        uint count
    )
    {
        return SearchQuery(name, eMail)
            .Skip((int)offset)
            .Take((int)count)
            .ToList();
    }

    public async Task UpdateStaffContacted(
        string name,
        string eMail,
        bool contacted
    )
    {
        var batchIntroductions = await _context.InsuranceBatchIntroductions
            .Where(b => b.Name == name && b.EMail == eMail)
            .ToListAsync();

        foreach (var batchIntroduction in batchIntroductions)
        {
            batchIntroduction.StaffContacted = contacted;
        }
        _context.SaveChanges();
    }

    public async Task UpdateInvestorRemarksFromStaff(
        string name,
        string eMail,
        string remarks
    )
    {
        var batchIntroductions = await _context.InsuranceBatchIntroductions
            .Where(b => b.Name == name && b.EMail == eMail)
            .ToListAsync();
        foreach (var batchIntroduction in batchIntroductions)
        {
            batchIntroduction.RemarksFromStaff = remarks;
        }
        _context.SaveChanges();
    }

    public int CountSearchKeys(string? name, string? eMail)
    {
        return SearchQuery(name, eMail).ToArray().Count();
    }

    public async Task DeletePrivacyInformation(string name, string eMail)
    {
        var batchIntroductions = await _context.InsuranceBatchIntroductions
            .Where(b => b.Name == name && b.EMail == eMail)
            .ToListAsync();
        foreach (var batchIntroduction in batchIntroductions)
        {
            Log.Information(
                "Delete investor privacy information: {Id},{Name},{EMail},{TelephoneNumber}",
                batchIntroduction.Id,
                batchIntroduction.Name,
                batchIntroduction.EMail,
                batchIntroduction.TelephoneNumber
            );
            batchIntroduction.DeleteInvestorPrivacyInformation();
        }
        _context.SaveChanges();
    }

    private IQueryable<InsuranceBatchIntroduction> GetQuery(
        string? text,
        string? eMail,
        bool requestedOnly
    )
    {
        var query = _context.InsuranceBatchIntroductions.AsQueryable();

        if (!string.IsNullOrEmpty(text))
        {
            text = text.Replace(" ", "").Replace("　", "");
            query = query.Where(a => a.Name.Contains(text));
        }

        if (!string.IsNullOrEmpty(eMail))
            query = query.Where(a => a.EMail.Contains(eMail));

        if (requestedOnly)
            query = query.Where(
                a => a.Status == InsuranceBatchIntroductionStatus.Request
            );

        query = query.OrderByDescending(b => b.Id);
        return query;
    }

    public Task<int> GetCount(string? text, string? eMail, bool requestedOnly)
    {
        return GetQuery(text, eMail, requestedOnly).CountAsync();
    }

    public ICollection<InsuranceBatchIntroduction> Search(
        string? text,
        string? eMail,
        bool requestedOnly,
        uint offset = 0,
        uint count = 20
    )
    {
        var query = GetQuery(text, eMail, requestedOnly);

        return query.Skip((int)offset).Take((int)count).ToArray();
    }

    public async Task SendMail(
        Guid accessKey,
        StaffInsuranceBatchIntroductionManagementData data,
        string watashiIfaUrl
    )
    {
        Log.Information(
            "Contact data by {MailAddress} from {OriginUrl}",
            data.EMail,
            data.OriginUrl
        );

        var mailFrom = new MailboxAddress(
            _settings.InsuranceConsultationRequestMail.FromName,
            _settings.InsuranceConsultationRequestMail.FromAddress
        );

        var error = false;
        InsuranceBatchIntroductionSurveyRequestMailTemplate template =
            new(accessKey, data, _context);
        string content = template.GetContentsForInvestor(watashiIfaUrl);

        error =
            !await _emailService.TrySendMail(
                InsuranceBatchIntroductionSurveyRequestMailTemplate.GetInvestorSubject(),
                mailFrom,
                data.EMail,
                content,
                data.OriginUrl
            ) || error;

        if (error)
            throw new NotificationMailError(data.OriginUrl, data.EMail);

        var staffNotificationMails = _settings
            .InsuranceConsultationRequestMail
            .NotificationMailAddresses;

        // Send mail to the adviser-navi staff.
        foreach (var mail in staffNotificationMails)
        {
            await _emailService.TrySendMail(
                template.GetNotificationSubject(!error),
                mailFrom,
                mail,
                content
                    + $@"
                    投資家名：{data.Name}
                    投資家メールアドレス：{data.EMail}
                    検索日：{data.CreatedTime}
                    検索ID： {data.Id}
                    ",
                data.OriginName
            );
        }
    }

    public async Task<InsuranceBatchIntroduction> GetInvestor(
        Ulid batchIntroductionId
    )
    {
        return await _model.SingleAsync(
            _context.InsuranceBatchIntroductions.Where(
                a => a.Id == batchIntroductionId
            )
        );
    }

    public IQueryable<InsuranceBatchIntroduction> GetLeadQuery(
        string? text,
        string? eMail
    )
    {
        var query = _context.InsuranceBatchIntroductions.AsQueryable();

        if (!string.IsNullOrEmpty(text))
        {
            text = text.Replace(" ", "").Replace("　", "");
            query = query.Where(i => i.Name.Contains(text));
        }

        if (!string.IsNullOrEmpty(eMail))
            query = query.Where(i => i.EMail.Contains(eMail));

        query = InsuranceBatchIntroduction
            .QueryNotTest(query)
            .Where(
                i =>
                    i.Status == InsuranceBatchIntroductionStatus.Introduction
                    && i.ParentIntroductionId == null
                    && !_context.InsuranceBatchIntroductions.Any(
                        b =>
                            b.EMail == i.EMail
                            && (
                                b.Status
                                == InsuranceBatchIntroductionStatus.Request
                            )
                    )
            )
            .OrderByDescending(b => b.CreatedTime);

        return query;
    }

    public ICollection<InsuranceBatchIntroduction> GetLeadList(
        string? text,
        string? eMail,
        uint offset = 0,
        uint count = 20
    )
    {
        var query = GetLeadQuery(text, eMail);
        return query.Skip((int)offset).Take((int)count).ToArray();
    }

    public async Task<int> CountLeadList(string? text, string? eMail)
    {
        var query = await GetLeadQuery(text, eMail).ToListAsync();
        return query.Count();
    }
}
