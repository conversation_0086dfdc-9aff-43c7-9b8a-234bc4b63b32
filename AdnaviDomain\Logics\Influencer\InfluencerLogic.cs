using Adnavi.Domain.DTOs.Influencer;
using Adnavi.Domain.Models;
using Adnavi.Domain.Models.Influencer;
using System.Threading.Tasks;
using Adnavi.Utils;
using Microsoft.Extensions.Options;

namespace Adnavi.Domain.Logics.Influencer;

public class InfluencerLogic
{
    private readonly AdnaviDomainContext _context;
    private readonly ModelUtils _model;
    private readonly ChatWorkUtils _chatWork;
    private readonly IOptions<AdnaviDomainSettings> _settings;

    public InfluencerLogic(
        ChatWorkUtils chatWork,
        AdnaviDomainContext context,
        ModelUtils model,
        IOptions<AdnaviDomainSettings> settings
    )
    {
        _context = context;
        _model = model;
        _chatWork = chatWork;
        _settings = settings;
    }

    public async Task<Models.Influencer.Influencer> CreateInfluencer(
        CreateInfluencerRequest request,
        string ipAddress
    )
    {
        var influencer = _model.Create(
            request.To(new Models.Influencer.Influencer(), ipAddress)
        );
        await _context.SaveChangesAsync();

        string message = "リーガルチェック用情報登録システムに情報が送信されました。";
        string templateInformation = influencer
            is { CorporationName: not null, CorporationAddress: not null }
            ? $@"組織タイプ：法人
代理店：{influencer.Organization}
法人名：{influencer.CorporationName}
法人住所：{influencer.CorporationAddress}"
            : $@"組織タイプ：個人
代理店：{influencer.Organization}
名前：{influencer.Name}
住所：{influencer.Address}
";

        await _chatWork.TrySend(
            _settings.Value.Investors.InfluencerChatWorkRoomId,
            $"{message}\n{templateInformation}\n"
        );
        return influencer;
    }
}
