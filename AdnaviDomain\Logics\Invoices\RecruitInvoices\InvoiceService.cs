using Adnavi.Domain.DTOs.Invoices.RecruitInvoices;
using Adnavi.Domain.Models;
using Adnavi.Domain.Models.Accounts;
using Adnavi.Domain.Models.Invoices.RecruitInvoices;
using Adnavi.Utils;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using NUlid;
using Serilog;

namespace Adnavi.Domain.Logics.Invoices.RecruitInvoices;

public class InvoiceService
{
    private readonly ModelUtils _model;
    private readonly AdnaviDomainContext _context;

    /// <summary>消費税</summary>
    private readonly byte _vatPercent;

    public InvoiceService(
        ModelUtils model,
        IOptions<AdnaviDomainSettings> settings
    )
    {
        _model = model;
        _context = model.Context;
        _vatPercent = settings.Value.VatPercent;
    }

    /// <summary>
    /// 与えられた日付で請求を作成する。
    /// </summary>
    /// <param name="date">請求の作成基準日</param>
    /// <returns></returns>
    public async Task CreateInvoice(DateTime date)
    {
        var yearMonth = new YearAndMonth(date);

        var processedOrganizationQuery = _context.RecruiterInvoices
            .Where(i => i.YearMonth == yearMonth)
            .Select(i => i.OrganizationId);

        // 手数料一覧の対象期間の料率が設定されている場合で、請求書が作成されていない企業を取得
        var organizations =
            from f in _context.EmployeeFees.TagWith(
                "OrganizationNotInvoiceCreated"
            )
            where
                f.StartYearMonth <= yearMonth
                && f.EndYearMonth >= yearMonth
                && f.RatePercent > 0
                && !processedOrganizationQuery.Contains(
                    f.Employment.OrganizationId
                )
            group f by new
            {
                f.Employment.Organization.Id,
                f.Employment.Organization.InvoiceRemarks
            } into g
            select new
            {
                g.Key.Id,
                g.Key.InvoiceRemarks,
                Profits = g.Select(f => new { f.EmploymentId, f.RatePercent, }),
            };

        // 請求の作成
        await foreach (var organization in organizations.AsAsyncEnumerable())
        {
            var profits = organization.Profits.Select(
                p =>
                    _model.Create(
                        new EmployeeProfit
                        {
                            FeeRatePercent = p.RatePercent,
                            EmploymentId = p.EmploymentId,
                        }
                    )
            );
            if (!profits.Any())
                continue;

            var invoice = _model.Create(
                new RecruiterInvoice
                {
                    OrganizationId = organization.Id,
                    YearMonth = yearMonth,
                    VatPercent = _vatPercent,
                    Profits = profits.ToList(),
                    Status = RecruiterInvoiceStatuses.Created,
                    Remarks = organization.InvoiceRemarks,
                }
            );
        }

        await _context.SaveChangesAsync();
    }

    public async Task UpdateInvoiceStatus(
        Ulid invoiceId,
        RecruiterInvoiceStatuses status
    )
    {
        var invoice = await _model.FindAsync(
            _context.RecruiterInvoices,
            invoiceId
        );

        var before = invoice.Status;
        invoice.Status = status;

        if (before == status)
            return;
        if (before == RecruiterInvoiceStatuses.Submitted)
            invoice.SubmittedTime = null;
        if (status == RecruiterInvoiceStatuses.Submitted)
            invoice.SubmittedTime = DateTime.UtcNow;

        await _context.SaveChangesAsync();
    }

    public ICollection<InvoiceOrganizationData> GetOrganizationsHaveSentInvoice()
    {
        return _context.RecruiterInvoices
            .GroupBy(i => new { i.Organization.Id, i.Organization.Name })
            .Select(
                g =>
                    new InvoiceOrganizationData
                    {
                        Id = g.Key.Id,
                        Name = g.Key.Name,
                        LastSubmitTime = g.Max(i => i.SubmittedTime),
                        FinishedCount = g.Count(
                            i => i.Status == RecruiterInvoiceStatuses.Submitted
                        ),
                        UnfinishedCount = g.Count(
                            i => i.Status == RecruiterInvoiceStatuses.Submitted
                        ),
                    }
            )
            .ToArray();
    }

    public ICollection<InvoiceMonthData> GetMonthsInvoiceCreated(
        Ulid organizationId
    )
    {
        return _context.RecruiterInvoices
            .Where(i => i.OrganizationId == organizationId)
            .Select(
                i =>
                    new InvoiceMonthData
                    {
                        Month = i.YearMonth,
                        Status = i.Status
                    }
            )
            .ToArray();
    }

    public ICollection<RecruiterInvoice> GetOrganizationInvoice(
        Ulid organizationId,
        YearAndMonth month
    )
    {
        return _context.RecruiterInvoices
            .Where(
                i => i.OrganizationId == organizationId && i.YearMonth == month
            )
            .ToArray();
    }

    public async Task UpdateInvoice(
        Organization organization,
        UpdateRecruiterInvoiceRequest request
    )
    {
        var invoice = await _model.SingleAsync(
            _context.RecruiterInvoices
                .Include(i => i.Profits)
                .Where(
                    i =>
                        i.Id == request.Id
                        && i.OrganizationId == organization.Id
                )
        );

        if (invoice.Status != RecruiterInvoiceStatuses.Created)
            throw new InvoiceHasAlreadySubmittedError();

        //using var transaction = _context.Database.BeginTransaction();

        UpdateInvoiceProfits(request.Id, request.Profits.AsEnumerable());
        await _context.SaveChangesAsync();

        CheckAndUpdateInvoiceAmount(invoice, request);
        await _context.SaveChangesAsync();

        //await transaction.CommitAsync();
    }

    private static InvoiceTotalAndVatResponse GetTotalAndVat(
        RecruiterInvoice invoice
    )
    {
        uint total = 0;
        foreach (var profit in invoice.Profits)
        {
            if (profit.Amount != null)
                // 四捨五入して加算する
                total += (uint)
                    Math.Round(
                        profit.Amount * profit.FeeRatePercent / 100.0 ?? 0.0,
                        MidpointRounding.AwayFromZero
                    );
        }

        total += invoice.OtherAmount ?? 0;

        var vat = total * invoice.VatPercent / 100;

        return new InvoiceTotalAndVatResponse
        {
            Total = total + vat,
            Vat = vat,
            VatPercent = invoice.VatPercent,
        };
    }

    private async void UpdateInvoiceProfits(
        Ulid invoiceId,
        IEnumerable<UpdateEmployeeProfitRequest> requests
    )
    {
        foreach (var profit in requests)
        {
            var entity = await _model.FindAsync(
                _context.EmployeeProfits,
                profit.Id
            );

            if (entity.RecruiterInvoiceId != invoiceId)
                throw new DifferentInvoiceIsContainedError();

            entity.Id = profit.Id;
            entity.RowVersion = profit.RowVersion;
            entity.Amount = profit.Amount;
        }
    }

    private static void CheckAndUpdateInvoiceAmount(
        RecruiterInvoice invoice,
        UpdateRecruiterInvoiceRequest request
    )
    {
        if (invoice.Status != RecruiterInvoiceStatuses.Created)
            throw new InvoiceHasAlreadySubmittedError();

        var totalAndVat = GetTotalAndVat(invoice);
        invoice.Amount = totalAndVat.Total;
        invoice.Vat = totalAndVat.Vat;

        if (!request.DoSubmit)
            return;

        foreach (var profit in invoice.Profits)
        {
            if (profit.Amount == null)
                throw new EmptyProfitDoesExistsError(profit.Id);
        }

        if (totalAndVat.Total != request.Amount)
        {
            Log.Error(
                "The requested amount {RequestAmount} does not match total profit {ProfitTotal}",
                request.Amount,
                totalAndVat.Total
            );
            throw new InvoiceAmountIsNotMatchError();
        }

        invoice.Status = RecruiterInvoiceStatuses.Submitted;
        invoice.SubmittedTime = DateTime.UtcNow;
    }
}
