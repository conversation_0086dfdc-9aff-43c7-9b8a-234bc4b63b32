using Adnavi.Utils;

namespace Adnavi.Domain.Logics.Invoices.MatchingInvoices;

internal class SendIfaRemindMailTemplate
{
    private readonly SendIfaRemindMailUser _data;
    protected readonly AdnaviDomainContext _context;

    public SendIfaRemindMailTemplate(
        SendIfaRemindMailUser data,
        AdnaviDomainContext context
    )
    {
        _data = data;
        _context = context;
    }

    public string GetRemindMailSubject() => "面談状況の入力をお願いします。";

    private string GetRequestContents()
    {
        var contents = "面談未入力の投資家";
        foreach (var invoice in _data.Invoices)
        {
            if (invoice.CreatedTime != null)
            {
                // invoice.CreateTimeをJTCに変換してstring型にする
                var introducedDate = DateUtils
                    .UtcToJst((DateTime)invoice.CreatedTime)
                    .ToString("yyyy/MM/dd");
                var investorName = invoice.BatchName;

                contents +=
                    $@"
                    紹介日：{{introducedDate}} {{investorName}}様
                    ";
            }
        }
        return contents;
    }

    private const string footerText =
        @"
━━━━━━━━━━━━━━━━━━━━━━━━━━

本自動配信メールは、ご登録いただいたメールアドレス宛てに、資産運用の相談サイト「資産運用ナビ」事務局から送られたものです。
お心当たりのない方は、お問合せ窓口までご連絡くださいませ。

（お問合せ窓口）
アドバイザーナビ株式会社
カスタマーサポート
<EMAIL>
";

    public string GetContentsForIfa()
    {
        var requestInformation = GetRequestContents();
        var contents =
            @"{{ data.ToName }} 様

お世話になります。
アドバイザーナビ株式会社「資産運用ナビ」カスタマーサポートチームでございます。

紹介の面談状況が入力されていない項目があります。
面談が完了している場合、お手数ですが、入力をおねがいいたします。

"
            + requestInformation
            + footerText;

        return contents;
    }
}
