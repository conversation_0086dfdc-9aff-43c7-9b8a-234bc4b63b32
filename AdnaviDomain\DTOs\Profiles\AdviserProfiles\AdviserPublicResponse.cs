using System.ComponentModel.DataAnnotations;
using Adnavi.Domain.DTOs.Profiles.IfaCompanyProfiles;
using Adnavi.Domain.Logics.Profiles.AdviserProfiles;
using Adnavi.Domain.Models.Commons;
using Adnavi.Domain.Models.Profiles.AdviserProfiles;
using Adnavi.Domain.Models.Profiles.IfaCompanyProfiles;
using Adnavi.Domain.Models.Profiles.InvestorProfiles;
using Adnavi.Utils;
using Adnavi.Utils.Images;
using Adnavi.Utils.Models;
using Microsoft.EntityFrameworkCore;
using NUlid;

namespace Adnavi.Domain.DTOs.Profiles.AdviserProfiles;

public sealed class AdviserPublicResponse : IDataTransferObject
{
    public Ulid Id { get; set; }

    public int? SequenceId { get; set; }

    [Timestamp]
    public byte[]? RowVersion { get; set; }

    public string FamilyName { get; set; }

    public string FirstName { get; set; }

    public string FamilyNameKana { get; set; }

    public string FirstNameKana { get; set; }

    public string? CompanyName { get; set; }

    public long? MinimumAcceptAssets { get; set; }

    public long? MaximumAcceptAssets { get; set; }

    public long? ActualMinimumAcceptAssetRanges { get; set; }

    public long? ActualMaximumAcceptAssetRanges { get; set; }

    public IfaCompanySimpleResponse? IfaCompany { get; set; }

    public YearAndMonth? IfaStartMonth { get; set; }

    public YearAndMonth? SalesStartMonth { get; set; }

    public int? DepositedAssetsMan { get; set; }

    public uint? NumberOfClientFamilies { get; set; }

    public byte? Age { get; set; }

    public int OpeningHour { get; set; }

    public int ClosingHour { get; set; }

    public IEnumerable<ImageSpecAndUrl> Photos { get; set; }

    public IEnumerable<Prefectures> VisitPrefectures { get; set; } =
        new List<Prefectures>();

    public IEnumerable<Prefectures> WebMeetingPrefectures { get; set; } =
        new List<Prefectures>();

    public YearAndMonth? CurrentEmploymentYear { get; set; }

    public string PostalCode { get; set; }
    public string Address { get; set; }
    public string Building { get; set; }
    public Prefectures? Prefecture { get; set; }

    public string? MainCareer { get; set; }

    [Required]
    public AdditionalProfileItemData? AdditionalProfileItem { get; set; }

    public List<ICollection<ImageSpecAndUrl>> PhotoCollections { get; set; } =
        new();

    public bool IsEnabledBatchIntroductionSetting { get; set; }

    public AdviserPublicResponse() { }

    public AdviserPublicResponse(
        Adviser e,
        AdnaviDomainContext context,
        IEnumerable<ImageSpecAndUrl> photos,
        IEnumerable<ICollection<ImageSpecAndUrl>> photoCollections
    )
    {
        Id = e.Id;
        SequenceId = e.SequenceId;
        RowVersion = e.RowVersion;
        FamilyName = e.FamilyName;
        FirstName = e.FirstName;
        FamilyNameKana = e.FamilyNameKana;
        FirstNameKana = e.FirstNameKana;
        MinimumAcceptAssets = e.MinimumAcceptAssets;
        MaximumAcceptAssets = e.MaximumAcceptAssets;
        CurrentEmploymentYear = e.CurrentEmploymentYear;
        CompanyName = e.IfaCompany?.Name;

        if (e.IfaCompany != null)
            IfaCompany = new IfaCompanySimpleResponse(e.IfaCompany);

        IfaStartMonth = e.IfaStartMonth;
        SalesStartMonth = e.SalesStartMonth;
        DepositedAssetsMan = e.DepositedAssetsMan;
        NumberOfClientFamilies = e.NumberOfClientFamilies;

        context.Entry(e).Collection(a => a.VisitPrefectures).Load();
        VisitPrefectures = e.VisitPrefectures.Select(p => p.Id);

        context.Entry(e).Collection(a => a.WebMeetingPrefectures).Load();
        WebMeetingPrefectures = e.WebMeetingPrefectures.Select(p => p.Id);

        if (e.BatchIntroductionSetting != null)
        {
            context
                .Entry(e.BatchIntroductionSetting)
                .Collection(b => b.AcceptAssetRanges)
                .Load();
        }

        if (e.IfaCompany?.BatchIntroductionIfaCompanySetting != null)
        {
            context
                .Entry(e.IfaCompany.BatchIntroductionIfaCompanySetting)
                .Collection(b => b.AcceptAssetRanges)
                .Load();
        }

        PostalCode = e.PostalCode;
        Address = e.Address;
        Building = e.Building;
        Prefecture = e.Prefecture;

        Age = e.Age;

        OpeningHour = e.OpeningHour;
        ClosingHour = e.ClosingHour;
        MainCareer = e.MainCareer;

        AdditionalProfileItem = new AdditionalProfileItemData(
            e.GetAdditionalProfileItemsOrDefault()
        );

        Photos = photos;
        PhotoCollections = photoCollections.ToList();

        IsEnabledBatchIntroductionSetting = context.BatchIntroductionSetting
            .Where(a => a.AdviserId == e.Id)
            .Any(a => a.Enabled);

        // Tính toán ActualAssetRanges
        var allAssetRanges = context.AssetRanges.ToList();
        var (actualMin, actualMax) = CalculateActualAssetRanges(
            e,
            allAssetRanges
        );
        ActualMinimumAcceptAssetRanges = actualMin;
        ActualMaximumAcceptAssetRanges = actualMax;
    }

    public static async Task<IQueryable<AdviserPublicResponse>> FromQuery(
        IQueryable<Adviser> query,
        AdviserProfileCommonLogic common,
        AdnaviDomainContext context
    )
    {
        query = query.AsNoTracking().Include(a => a.BatchIntroductionSetting)
            .ThenInclude(b => b!.AcceptAssetRanges).Include(a => a.IfaCompany)
            .ThenInclude(c => c!.BatchIntroductionIfaCompanySetting)
            .ThenInclude(s => s!.AcceptAssetRanges);
        var allAssetRanges = await context.AssetRanges.ToListAsync();

        var ifaCompanies = await (
            await IfaCompanySimpleResponse.FromQuery(
                query
                    .Where(a => a.IfaCompany != null)
                    .Select(a => a.IfaCompany!)
                    .Distinct()
            )
        ).ToDictionaryAsync(c => c.Id, c => c);

        var visitPrefectures = await query.GetAdviserVisitPrefectureIdList();
        var webMeetingPrefectures =
            await query.GetAdviserWebMeetingPrefectureIdList();

        var additionalProfileItems = await (
            AdditionalProfileItemData.FromQuery(
                query
                    .Where(a => a.AdditionalProfileItems != null)
                    .Select(a => a.AdditionalProfileItems!)
            )
        ).ToDictionaryAsync(a => a.Id!.Value, a => a);

        var photoIdList = await query
            .Select(
                a =>
                    new
                    {
                        a.Id,
#pragma warning disable CS0612 // Type or member is obsolete
                        a.PhotoFileKey,
#pragma warning restore CS0612 // Type or member is obsolete
                        a.NewPhotoFileKey
                    }
            )
            .ToArrayAsync();

        var photoList = photoIdList
            .Select(
                p =>
                    new
                    {
                        p.Id,
                        Urls = common
                            .GetPhotoUrls(
                                p.Id,
                                p.NewPhotoFileKey,
                                p.PhotoFileKey
                            )
                            .Result
                    }
            )
            .ToDictionary(p => p.Id, p => p.Urls);

        var photoCollectionList = photoIdList
            .Select(
                p =>
                    new
                    {
                        p.Id,
                        Urls = common.GetPhotoCollectionUrls(p.Id).Result
                    }
            )
            .ToDictionary(p => p.Id, p => p.Urls);
        var advisers = query.Select(
            a =>
                new AdviserPublicResponse
                {
                    Id = a.Id,
                    SequenceId = a.SequenceId,
                    RowVersion = a.RowVersion,
                    FamilyName = a.FamilyName,
                    FirstName = a.FirstName,
                    FamilyNameKana = a.FamilyNameKana,
                    FirstNameKana = a.FirstNameKana,
                    MinimumAcceptAssets = a.MinimumAcceptAssets,
                    MaximumAcceptAssets = a.MaximumAcceptAssets,
                    ActualMinimumAcceptAssetRanges = CalculateActualAssetRanges(
                        a,
                        allAssetRanges
                    ).min,
                    ActualMaximumAcceptAssetRanges = CalculateActualAssetRanges(
                        a,
                        allAssetRanges
                    ).max,
                    CurrentEmploymentYear = a.CurrentEmploymentYear,
                    IfaStartMonth = a.IfaStartMonth,
                    SalesStartMonth = a.SalesStartMonth,
                    DepositedAssetsMan = a.DepositedAssetsMan,
                    NumberOfClientFamilies = a.NumberOfClientFamilies,
                    VisitPrefectures = visitPrefectures.GetValueOrDefault(
                        a.Id,
                        new List<Prefectures>()
                    ),
                    WebMeetingPrefectures =
                        webMeetingPrefectures.GetValueOrDefault(
                            a.Id,
                            new List<Prefectures>()
                        ),
                    PostalCode = a.PostalCode,
                    Address = a.Address,
                    Building = a.Building,
                    Prefecture = a.Prefecture,
                    Age = a.Age,
                    OpeningHour = a.OpeningHour,
                    ClosingHour = a.ClosingHour,
                    MainCareer = a.MainCareer,
                    IfaCompany =
                        a.IfaCompanyId == null
                            ? null
                            : ifaCompanies.GetValueOrDefault(
                                a.IfaCompanyId.Value
                            ),
                    AdditionalProfileItem =
                        a.AdditionalProfileItems == null
                            ? null
                            : additionalProfileItems.GetValueOrDefault(
                                a.AdditionalProfileItems.Id
                            ),
                    Photos = photoList.GetValueOrDefault(
                        a.Id,
                        new List<ImageSpecAndUrl>()
                    ),
                    PhotoCollections = photoCollectionList.GetValueOrDefault(
                        a.Id,
                        new List<ICollection<ImageSpecAndUrl>>()
                    ),
                    IsEnabledBatchIntroductionSetting =
                        context.BatchIntroductionSetting
                            .Where(b => b.AdviserId == a.Id)
                            .Any(b => b.Enabled)
                }
        );

        return advisers;
    }

    private static (long? min, long? max) CalculateActualAssetRanges(
        Adviser adviser,
        IEnumerable<AssetRange> allAssetRanges
    )
    {
        // Adviser.XXXX.AcceptAssetRanges を調べる
        var adviserAssetRanges =
            adviser.BatchIntroductionSetting?.AcceptAssetRanges?.Any() == true
                ? adviser.BatchIntroductionSetting.AcceptAssetRanges
                : allAssetRanges;

        // IfaCompany.XXXX.AcceptAssetRanges を調べる
        var ifaCompanyAssetRanges =
            adviser.IfaCompany?.BatchIntroductionIfaCompanySetting?.AcceptAssetRanges?.Any()
            == true
                ? adviser
                    .IfaCompany
                    .BatchIntroductionIfaCompanySetting
                    .AcceptAssetRanges
                : allAssetRanges;

        // adviserAssetRanges と companyAssetRanges の 積集合を求め、 actualAssetRanges とする。
        var actualAssetRanges = adviserAssetRanges
            .Intersect(ifaCompanyAssetRanges)
            .ToList();

        // actualAssetRanges から最低と最大の受付金融資産を求める。
        if (!actualAssetRanges.Any())
            return (null, null);

        var min = actualAssetRanges.Min(x => x.Minimum);
        var max = actualAssetRanges.Max(x => x.Maximum);
        var hasNullMinimum = actualAssetRanges.Any(x => x.Minimum == null);
        var hasNullMaximum = actualAssetRanges.Any(x => x.Maximum == null);

        return (hasNullMinimum ? null : min, hasNullMaximum ? null : max);
    }
}
