using System.ComponentModel.DataAnnotations;
using Adnavi.Domain.Models.Accounts;
using Adnavi.Utils.Models;
using Microsoft.EntityFrameworkCore;
using NUlid;

namespace Adnavi.Domain.DTOs.Accounts.OrganizationManagement;

public sealed class UserManagementSummaryData : IDataTransferObject
{
    [Required]
    public Ulid UserId { get; set; }

    [Required]
    public Ulid OrganizationId { get; set; }

    public string? MainEMail { get; set; }

    [Required]
    public IEnumerable<OrganizationMembershipScopes> Scopes { get; set; }

    public UserManagementSummaryData() { }

    public static IQueryable<UserManagementSummaryData> FromQuery(
        IQueryable<OrganizationMembership> query
    )
    {
        return query
            .AsNoTracking()
            .Select(
                m =>
                    new UserManagementSummaryData
                    {
                        UserId = m.UserId,
                        OrganizationId = m.OrganizationId,
                        MainEMail = m.User.MainEMail,
                        Scopes = m.Scopes.Select(s => s.Id)
                    }
            );
    }
}
