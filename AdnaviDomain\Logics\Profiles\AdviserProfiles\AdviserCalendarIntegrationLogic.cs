using Adnavi.Domain.DTOs.Consultations.BatchIntroductions;
using Adnavi.Domain.Models.Profiles.AdviserProfiles;
using Adnavi.Utils.Exceptions;
using Microsoft.EntityFrameworkCore;
using NUlid;

namespace Adnavi.Domain.Logics.Profiles.AdviserProfiles;

/// <summary>
/// Googleカレンダー連携ロジック
/// </summary>
public class AdviserCalendarIntegrationLogic
{
    private readonly AdnaviDomainContext _context;
    private readonly AdviserGoogleCalendarLogic _googleCalendarLogic;
    private readonly AdviserMicrosoftCalendarLogic _microsoftCalendarLogic;

    public AdviserCalendarIntegrationLogic(
        AdnaviDomainContext context,
        AdviserGoogleCalendarLogic googleCalendarLogic,
        AdviserMicrosoftCalendarLogic microsoftCalendarLogic
    )
    {
        _context = context;
        _googleCalendarLogic = googleCalendarLogic;
        _microsoftCalendarLogic = microsoftCalendarLogic;
    }

    /// <summary>
    /// 指定スロット（startDateTime～1時間）に空きがあるアドバイザー一覧を返す
    /// </summary>
    public async Task<List<Adviser>> GetAdviserBySlot(
        List<Ulid> adviserIds,
        DateTimeOffset startDateTime,
        string googleClientId,
        string googleClientSecret,
        string microsoftClientId = "",
        string microsoftClientSecret = ""
    )
    {
        var slotStart = startDateTime;
        var slotEnd = startDateTime.AddHours(1);

        // adviserIdごとの空きスロットを取得
        var freeSlotsByAdviser = await GetFreeSlotsByAdviser(
            adviserIds,
            slotStart,
            slotEnd,
            googleClientId,
            googleClientSecret,
            microsoftClientId,
            microsoftClientSecret
        );

        // adviserIds から Adviser エンティティを取得
        var advisers = await _context.Advisers
            .Where(a => adviserIds.Contains(a.Id))
            .ToListAsync();

        // 空きがある Adviser のみ抽出
        var availableAdvisers = advisers
            .Where(
                adviser =>
                    freeSlotsByAdviser.TryGetValue(adviser.Id, out var slots)
                    && slots.Any(
                        slot => slot.Start <= slotStart && slot.End >= slotEnd
                    )
            )
            .ToList();

        return availableAdvisers;
    }

    /// <summary>
    /// Google認可URLを生成
    /// </summary>
    public string GetGoogleAuthUrl(
        string clientId,
        string redirectUri,
        string[] scopes
    )
    {
        return _googleCalendarLogic.GetGoogleAuthUrl(
            clientId,
            redirectUri,
            scopes
        );
    }

    /// <summary>
    /// Google認可コールバック処理
    /// </summary>
    public async Task HandleGoogleCallbackAsync(
        string code,
        Ulid adviserId,
        string clientId,
        string clientSecret,
        string redirectUri
    )
    {
        await _googleCalendarLogic.HandleGoogleCallbackAsync(
            code,
            adviserId,
            clientId,
            clientSecret,
            redirectUri
        );
    }

    /// <summary>
    /// Microsoft認可URLを生成
    /// </summary>
    public string GetMicrosoftAuthUrl(
        string clientId,
        string redirectUri,
        string[] scopes
    )
    {
        return _microsoftCalendarLogic.GetMicrosoftAuthUrl(
            clientId,
            redirectUri,
            scopes
        );
    }

    /// <summary>
    /// Microsoft認可コールバック処理
    /// </summary>
    public async Task HandleMicrosoftCallbackAsync(
        string code,
        Ulid adviserId,
        string clientId,
        string clientSecret,
        string redirectUri
    )
    {
        await _microsoftCalendarLogic.HandleMicrosoftCallbackAsync(
            code,
            adviserId,
            clientId,
            clientSecret,
            redirectUri
        );
    }

    /// <summary>
    /// Googleカレンダー連携解除
    /// </summary>
    public async Task DisconnectGoogleCalendarAsync(Ulid adviserId)
    {
        await _googleCalendarLogic.DisconnectGoogleCalendarAsync(adviserId);
    }

    /// <summary>
    /// Googleカレンダー連携済みか判定
    /// </summary>
    public async Task<bool> IsGoogleCalendarConnectedAsync(Ulid adviserId)
    {
        return await _googleCalendarLogic.IsGoogleCalendarConnectedAsync(
            adviserId
        );
    }

    public async Task DisconnectOutlookCalendarAsync(Ulid adviserId)
    {
        await _microsoftCalendarLogic.DisconnectOutlookCalendarAsync(adviserId);
    }

    public async Task<bool> IsMicrosoftCalendarConnectedAsync(Ulid adviserId)
    {
        return await _microsoftCalendarLogic.IsMicrosoftCalendarConnectedAsync(
            adviserId
        );
    }

    /// <summary>
    /// 指定アドバイザーのGoogleカレンダーから空き時間を取得します。
    /// </summary>
    /// <param name="adviserId">アドバイザーID</param>
    /// <param name="startDateTime">取得開始日時</param>
    /// <param name="endDateTime">取得終了日時</param>
    /// <param name="googleClientId">Google APIクライアントID</param>
    /// <param name="googleClientSecret">Google APIクライアントシークレット</param>
    /// <param name="microsoftClientId">Microsoft APIクライアントID（省略時は空文字）</param>
    /// <param name="microsoftClientSecret">Microsoft APIクライアントシークレット（省略時は空文字）</param>
    /// <param name="calendarId">カレンダーID（省略時は"primary"）</param>
    /// <returns>FreeSlotリスト</returns>
    private async Task<List<AdviserFreeSlot>> GetFreeSlots(
        Ulid adviserId,
        DateTimeOffset startDateTime,
        DateTimeOffset endDateTime,
        string googleClientId,
        string googleClientSecret,
        string microsoftClientId = "",
        string microsoftClientSecret = "",
        string calendarId = "primary"
    )
    {
        // Google連携があればGoogleカレンダーからbusy slot取得
        var googleAccessToken =
            await _googleCalendarLogic.GetValidGoogleAccessTokenAsync(
                adviserId,
                googleClientId,
                googleClientSecret
            );
        if (!string.IsNullOrEmpty(googleAccessToken))
        {
            try
            {
                var busySlots = await _googleCalendarLogic.GetGoogleBusySlots(
                    googleAccessToken,
                    startDateTime,
                    endDateTime,
                    calendarId
                );

                return CalculateFreeSlots(
                    busySlots,
                    startDateTime,
                    endDateTime
                );
            }
            catch (GoogleAuthException)
            {
                // Googleカレンダーの取得に失敗した場合は空リストを返す
                return new List<AdviserFreeSlot>();
            }
        }

        // Microsoft連携があればMicrosoftカレンダーからbusy slot取得

        var microsoftAccessToken =
            await _microsoftCalendarLogic.GetValidMicrosoftAccessTokenAsync(
                adviserId,
                microsoftClientId,
                microsoftClientSecret
            );

        if (!string.IsNullOrEmpty(microsoftAccessToken))
        {
            var busySlots = await _microsoftCalendarLogic.GetMicrosoftBusySlots(
                microsoftAccessToken,
                startDateTime,
                endDateTime
            );
            return CalculateFreeSlots(busySlots, startDateTime, endDateTime);
        }

        // どちらもなければ空リスト
        return new List<AdviserFreeSlot>();
    }

    /// <summary>
    /// 複数アドバイザーの空き時間をまとめて取得
    /// </summary>
    private async Task<
        Dictionary<Ulid, List<AdviserFreeSlot>>
    > GetFreeSlotsByAdviser(
        List<Ulid> adviserIds,
        DateTimeOffset startDateTime,
        DateTimeOffset endDateTime,
        string googleClientId,
        string googleClientSecret,
        string microsoftClientId = "",
        string microsoftClientSecret = ""
    )
    {
        // NOTE: IntegrationLogicのGetFreeSlotsを呼ぶ必要があるため、ここは元の実装を維持
        var result = new Dictionary<Ulid, List<AdviserFreeSlot>>();
        foreach (var adviserId in adviserIds)
        {
            var slots = await GetFreeSlots(
                adviserId,
                startDateTime,
                endDateTime,
                googleClientId,
                googleClientSecret,
                microsoftClientId,
                microsoftClientSecret
            );
            result[adviserId] = slots;
        }
        return result;
    }

    /// <summary>
    /// アドバイザーIDリスト・開始日時等を受けて1週間分の空きアドバイザー数を返す
    /// </summary>
    public async Task<List<SlotCountResponse>> GetWeeklyAvailableAdviserCounts(
        List<Ulid> adviserIds,
        DateTimeOffset startDateTime,
        string googleClientId,
        string googleClientSecret,
        TimeZoneInfo tz,
        string microsoftClientId = "",
        string microsoftClientSecret = "",
        int startHour = 8,
        int endHour = 24
    )
    {
        var slots = GenerateWeeklySlots(startDateTime, tz, startHour, endHour);
        if (slots.Count == 0)
            return new List<SlotCountResponse>();

        var slotsByAdviser = await GetFreeSlotsByAdviser(
            adviserIds,
            slots.First().start,
            slots.Last().end,
            googleClientId,
            googleClientSecret,
            microsoftClientId,
            microsoftClientSecret
        );

        var response = new List<SlotCountResponse>();
        foreach (var (start, end) in slots)
        {
            int availableCount = GetAvailableSlots(slotsByAdviser, start, end);
            response.Add(
                new SlotCountResponse
                {
                    SlotStart = start,
                    AvailableAdviserCount = availableCount
                }
            );
        }
        return response;
    }

    /// <summary>
    /// 指定アドバイザーのGoogleカレンダーにイベントを作成する
    /// </summary>
    /// <param name="adviserId">アドバイザーID</param>
    /// <param name="startDateTime">イベント開始日時</param>
    /// <param name="clientId">Google APIクライアントID</param>
    /// <param name="clientSecret">Google APIクライアントシークレット</param>
    /// <param name="eventTitle">イベントタイトル</param>
    /// <param name="calendarId">カレンダーID（省略時は"primary"）</param>
    public async Task<string> CreateEvent(
        Ulid adviserId,
        DateTimeOffset startDateTime,
        string googleClientId,
        string googleClientSecret,
        string eventTitle,
        string eventDescription,
        string microsoftClientId = "",
        string microsoftClientSecret = "",
        string calendarId = "primary"
    )
    {
        var googleAccessToken =
            await _googleCalendarLogic.GetValidGoogleAccessTokenAsync(
                adviserId,
                googleClientId,
                googleClientSecret
            );
        if (!string.IsNullOrEmpty(googleAccessToken))
        {
            return await _googleCalendarLogic.CreateEvent(
                googleAccessToken,
                startDateTime,
                eventTitle,
                eventDescription,
                calendarId
            );
        }

        // else
        // {
        //     var microsoftAccessToken =
        //         await _microsoftCalendarLogic.GetValidMicrosoftAccessTokenAsync(
        //             adviserId,
        //             microsoftClientId,
        //             microsoftClientSecret
        //         );
        //     if (!string.IsNullOrEmpty(microsoftAccessToken))
        //     {
        //         return await _microsoftCalendarLogic.CreateEvent(
        //             microsoftAccessToken,
        //             startDateTime,
        //             eventTitle,
        //             eventDescription
        //         );
        //     }
        // }

        // Googleカレンダー連携がない場合は例外をスロー
        throw new GoogleAuthException("Googleカレンダー連携がありません");
    }

    public async Task<string> BookConsultationEventAsync(
        Ulid adviserId,
        DateTimeOffset startDateTime,
        string googleClientId,
        string googleClientSecret,
        string eventTitle,
        string eventDescription,
        bool isDisallowed,
        bool testMode = false,
        string microsoftClientId = "",
        string microsoftClientSecret = "",
        string calendarId = "primary",
        int durationHours = 1
    )
    {
        if (isDisallowed)
        {
            throw new SlotNotAvailableException("すでに申込済みです。");
        }
        var endDateTime = startDateTime.AddHours(durationHours);
        var freeSlots = await GetFreeSlots(
            adviserId,
            startDateTime,
            endDateTime,
            googleClientId,
            googleClientSecret
        );
        bool isSlotAvailable = freeSlots.Any(
            slot => slot.Start <= startDateTime && slot.End >= endDateTime
        );
        // テストモードではスロットの空きチェックをスキップ
        if (!isSlotAvailable && !testMode)
        {
            throw new SlotNotAvailableException("指定された時間帯は予約できません。");
        }
        try
        {
            var eventId = await CreateEvent(
                adviserId,
                startDateTime,
                googleClientId,
                googleClientSecret,
                eventTitle,
                eventDescription,
                microsoftClientId,
                microsoftClientSecret,
                calendarId
            );
            return eventId;
        }
        catch (Exception ex)
        {
            throw new GoogleAuthException("Googleカレンダーのイベント作成に失敗しました。", ex);
        }
    }

    private List<AdviserFreeSlot> CalculateFreeSlots(
        List<(DateTimeOffset Start, DateTimeOffset End)> busySlots,
        DateTimeOffset startDateTime,
        DateTimeOffset endDateTime
    )
    {
        var freeSlots = new List<AdviserFreeSlot>();
        var slotStart = startDateTime;
        foreach (var busy in busySlots)
        {
            if (slotStart < busy.Start)
            {
                freeSlots.Add(
                    new AdviserFreeSlot { Start = slotStart, End = busy.Start }
                );
            }
            slotStart = (busy.End > slotStart) ? busy.End : slotStart;
        }
        if (slotStart < endDateTime)
        {
            freeSlots.Add(
                new AdviserFreeSlot { Start = slotStart, End = endDateTime }
            );
        }
        return freeSlots;
    }

    // 予約可能なアドバイザーの数を取得
    private int GetAvailableSlots(
        Dictionary<Ulid, List<AdviserFreeSlot>> freeSlots,
        DateTimeOffset startDateTime,
        DateTimeOffset endDateTime
    )
    {
        // 予約可能なアドバイザーの数をカウントする
        // 指定期間全体が空いている場合のみ空きとみなす
        int availableCount = freeSlots.Keys.Count(adviserId =>
        {
            var adviserFreeSlots = freeSlots[adviserId];
            // 指定期間全体が空いている場合のみ空きとみなす
            return adviserFreeSlots.Any(
                slot => slot.Start <= startDateTime && slot.End >= endDateTime
            );
        });

        return availableCount;
    }

    // 1週間分のスロットを生成
    private List<(
        DateTimeOffset start,
        DateTimeOffset end
    )> GenerateWeeklySlots(
        DateTimeOffset startDateTime,
        TimeZoneInfo tz,
        int startHour = 8,
        int endHour = 24
    )
    {
        var slots = new List<(DateTimeOffset, DateTimeOffset)>();
        for (int day = 0; day < 7; day++)
        {
            var baseDate = TimeZoneInfo
                .ConvertTime(startDateTime, tz)
                .Date.AddDays(day);

            for (int hour = startHour; hour < endHour; hour++)
            {
                var slotStart = new DateTimeOffset(
                    baseDate.Year,
                    baseDate.Month,
                    baseDate.Day,
                    hour,
                    0,
                    0,
                    tz.BaseUtcOffset
                );
                var slotEnd = slotStart.AddHours(1);
                slots.Add((slotStart, slotEnd));
            }
        }
        return slots;
    }
}
