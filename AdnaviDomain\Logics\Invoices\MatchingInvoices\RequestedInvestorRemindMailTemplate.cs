namespace Adnavi.Domain.Logics.Invoices.MatchingInvoices;

internal class RequestedInvestorRemindMailTemplate
{
    private readonly SendIfaRemindMailInvestor _data;

    public RequestedInvestorRemindMailTemplate(SendIfaRemindMailInvestor data)
    {
        _data = data;
    }

    public string GetInvestorRemindMailSubject() => "アドバイザーとの面談状況の確認";

    public string GetInvestorRemindMailContents()
    {
        var contents =
            @$"{_data.ToName} 様

お世話になっております。
アドバイザーナビ株式会社カスタマーサポートチームでございます。 

先日は、当社のサービスをご利用いただき、誠にありがとうございます。 
その後の進捗状況についてお伺いしたく、ご連絡させていただきました。 

アドバイザーとの面談はすでに完了されておりますでしょうか。 
現在の進捗状況や、ご不明点、ご懸念点などがございましたら、どうぞご遠慮なくお知らせください。 

今後とも、どうぞよろしくお願いいたします。

";
        return contents;
    }
}
