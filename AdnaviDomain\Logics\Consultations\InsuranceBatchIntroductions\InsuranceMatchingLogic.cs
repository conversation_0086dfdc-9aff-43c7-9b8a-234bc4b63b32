﻿using Adnavi.Domain.Models;
using Adnavi.Domain.Models.Profiles.AdviserProfiles;
using Adnavi.Domain.Models.Profiles.InvestorProfiles;
using Adnavi.Domain.Models.Profiles.PersonalityTraits;
using Adnavi.Utils;
using Adnavi.Utils.Exceptions;
using Microsoft.EntityFrameworkCore;
using MimeKit;
using NUlid;
using Serilog;
using Amazon.CloudWatch;
using Amazon.CloudWatch.Model;
using Adnavi.Domain.Logics.Consultations.BatchIntroductions;
using Adnavi.Domain.Models.Consultations.InsuranceBatchIntroductions;
using Adnavi.Domain.Models.Accounts;
using Adnavi.Domain.DTOs.Consultations.InsuranceBatchIntroductions;
using Adnavi.Domain.Logics.Common.EMails;
using Microsoft.Extensions.Options;

namespace Adnavi.Domain.Logics.Consultations.InsuranceBatchIntroductions;

public class InsuranceMatchingLogic
{
    private readonly IOptions<AdnaviDomainSettings> _settings;
    private readonly AdnaviDomainContext _context;
    private readonly ChatWorkUtils _chatWork;
    private readonly ModelUtils _model;
    private readonly IAmazonCloudWatch _amazonCloudWatch;
    private readonly Dimension _dimension;
    private readonly EMailService _emailService;
    private readonly CommonMatchingLogic _commonMatchingLogic;

    public InsuranceMatchingLogic(
        IOptions<AdnaviDomainSettings> settings,
        ChatWorkUtils chatWork,
        EMailService emailService,
        IAmazonCloudWatch amazonCloudWatch,
        AdnaviDomainContext context,
        ModelUtils model,
        CommonMatchingLogic commonMatchingLogic
    )
    {
        _settings = settings;
        _context = context;
        _chatWork = chatWork;
        _model = model;
        _amazonCloudWatch = amazonCloudWatch;
        _emailService = emailService;
        _commonMatchingLogic = commonMatchingLogic;
        _dimension = new Dimension
        {
            Name = "InsuranceBatchIntroduction Metrics",
            Value = "InsuranceBatchIntroduction request and introduction"
        };
    }

    public async Task Request(
        InsuranceBatchIntroduction data,
        bool isStaffRequest,
        User? registeredUser = null
    )
    {
        if (registeredUser?.MainEMail != null)
        {
            data.RegisterEmail = registeredUser.MainEMail;
        }
        var template = new InsuranceBatchIntroductionMailTemplate(
            data,
            _context,
            _settings,
            isStaffRequest
        );

        var mailFrom = new MailboxAddress(
            _settings.Value.Investors.InsuranceConsultationRequestMail.FromName,
            _settings
                .Value
                .Investors
                .InsuranceConsultationRequestMail
                .FromAddress
        );

        var staffNotificationMails = _settings
            .Value
            .Investors
            .InsuranceConsultationRequestMail
            .NotificationMailAddresses;

        var error = false;
        var errorAdvisers = new List<InsuranceAdviser>();

        // Send mail to the advisers, and add a MatchingInvoiceManagement to the DB.

        foreach (var adviser in data.Advisers)
        {
            // int matchInvoiceAmount = GetMatchingInvoiceAmount(data, adviser);

            if (adviser.User?.MainEMail == null)
            {
                errorAdvisers.Add(adviser);
                continue;
            }

            var e = !await _emailService.TrySendMail(
                template.GetAdviserSubject(),
                mailFrom,
                adviser.User.MainEMail,
                template.GetContentsForAdviser(adviser),
                data.OriginUrl
            );

            if (e)
                errorAdvisers.Add(adviser);

            var companyEmails = StringUtils.SplitCsvColum(
                adviser.InsuranceCompany?.NotificationEmail ?? ""
            );

            foreach (var companyEmail in companyEmails)
            {
                await _emailService.TrySendMail(
                    template.GetInsuranceCompanySubject(adviser),
                    mailFrom,
                    companyEmail,
                    template.GetContentsForInsuranceCompany(adviser),
                    data.OriginUrl
                );
            }

            if (adviser.InsuranceCompany?.Organization == null)
                continue;

            foreach (
                var organizationMembership in adviser
                    .InsuranceCompany
                    .Organization
                    .Memberships
            )
            {
                if (
                    organizationMembership.Scopes.Any(
                        b =>
                            b.Id
                            == OrganizationMembershipScopes.InsuranceCompanyManagement
                    )
                )
                {
                    if (organizationMembership?.User.MainEMail == null)
                        continue;

                    await _emailService.TrySendMail(
                        template.GetInsuranceCompanySubject(adviser),
                        mailFrom,
                        organizationMembership.User.MainEMail,
                        template.GetContentsForInsuranceCompany(adviser),
                        data.OriginUrl
                    );
                }
            }
        }

        // Send mail to the adviser-navi staff.

        var notificationContents = template.GetContentsForNotification(
            errorAdvisers
        );

        foreach (var mail in staffNotificationMails)
        {
            error =
                !await _emailService.TrySendMail(
                    template.GetNotificationSubject(data.OriginName),
                    mailFrom,
                    mail,
                    notificationContents,
                    data.OriginUrl
                ) || error;
            ;
        }

        // Send mail to the investor.

        error =
            !await _emailService.TrySendMail(
                template.GetInvestorSubject(),
                mailFrom,
                data.EMail,
                template.GetContentsForInvestor(errorAdvisers),
                data.OriginUrl
            ) || error;

        if (error)
            throw new NotificationMailError(data.OriginUrl, data.EMail);
        data.SendRemindMail = true;
        _context.SaveChanges();

        //Send metric to CloudWatch
        try
        {
            await _amazonCloudWatch.PutMetricDataAsync(
                new PutMetricDataRequest
                {
                    MetricData = new List<MetricDatum>
                    {
                        new MetricDatum
                        {
                            Dimensions = new List<Dimension> { _dimension },
                            MetricName =
                                "Insurance BatchIntroduction Requested",
                            Unit = StandardUnit.Count,
                            Value = 1,
                        }
                    },
                    Namespace = _settings
                        .Value
                        .Investors
                        .CloudWatchMetricsNamespace
                }
            );
        }
        catch (Exception e)
        {
            Log.Error(e, "Failed to send metric to CloudWatch");
        }
    }

    private IQueryable<InsuranceAdviser> AcceptedAdviserQuery =>
        _context.InsuranceAdvisers.Where(
            a =>
                a.CompanyByCompanyIntroduction != null
                    && a.InsuranceCompany != null
                    && a.InsuranceBatchIntroductionSetting != null
                    && a.InsuranceBatchIntroductionSetting.Enabled
                || (
                    a.User != null
                    && a.User.MainEMail
                        == _settings.Value.Investors.AdviserTestMailAddress
                )
        );

    internal static IQueryable<InsuranceAdviser> AddWhereAccepted(
        IQueryable<InsuranceAdviser> query
    )
    {
        return query.Where(
            a =>
                a.CompanyByCompanyIntroduction != null
                && a.InsuranceCompany != null
                && a.InsuranceBatchIntroductionSetting != null
                && a.InsuranceBatchIntroductionSetting.Enabled
        );
    }

    public async Task<IEnumerable<Ulid>> GetAdviserIds(
        InsuranceBatchIntroduction entity,
        bool staffIntroduction
    )
    {
        var query = AcceptedAdviserQuery;

        // スタッフからの紹介でない場合は前回の検索結果を引き継ぐ
        if (!staffIntroduction && entity.ParentIntroductionId != null)
        {
            var parentIntroduction = await _model.SingleAsync(
                _context.InsuranceBatchIntroductions.Where(
                    a => a.Id == entity.ParentIntroductionId
                )
            );
            query = query.Where(a => parentIntroduction.Advisers.Contains(a));
        }

        // WEBミーティングのある場合とない場合での条件分岐
        var contactMethods = entity.ContactMethods.Select(c => c.Id);
        query = query.Where(
            a =>
                (
                    // if web meeting is selected, adviser must have webMeetingPrefecture
                    contactMethods.Contains(ContactMethods.WebMeeting)
                    && a.AdditionalProfileItems != null
                    && a.AdditionalProfileItems.AvailableContactMethods.Any(
                        c => c.Id == ContactMethods.WebMeeting
                    )
                    && a.WebMeetingPrefectures.Any(
                        p => p.Id == entity.Prefecture
                    )
                )
                || (
                    // if office or visit is selected, adviser must have visitPrefecture.
                    (
                        contactMethods.Contains(ContactMethods.Office)
                        || contactMethods.Contains(ContactMethods.Visit)
                            && a.AdditionalProfileItems != null
                            && a.AdditionalProfileItems.AvailableContactMethods.Any(
                                c =>
                                    c.Id == ContactMethods.Office
                                    || c.Id == ContactMethods.Visit
                            )
                    ) && a.VisitPrefectures.Any(p => p.Id == entity.Prefecture)
                )
        );

        // アドバイザーの紹介可能年収範囲とのマッチング
        query = query.Where(
            a =>
                a.InsuranceBatchIntroductionSetting == null
                || a.InsuranceBatchIntroductionSetting.AcceptAnnualIncomes.Count
                    == 0
                || a.InsuranceBatchIntroductionSetting.AcceptAnnualIncomes.Any(
                    i => i.Id == entity.AnnualIncome
                )
        );

        // アドバイザーの紹介可能資産範囲とのマッチング
        query = query.Where(
            a =>
                a.InsuranceBatchIntroductionSetting == null
                || a.InsuranceBatchIntroductionSetting.AcceptAssetRanges.Count
                    == 0
                || a.InsuranceBatchIntroductionSetting.AcceptAssetRanges.Any(
                    i => i.Id == entity.AssetRange
                )
        );

        // アドバイザーの紹介可能年齢とのマッチング
        query = query.Where(
            a =>
                a.InsuranceBatchIntroductionSetting == null
                || a.InsuranceBatchIntroductionSetting.OlderLimit == null
                || a.InsuranceBatchIntroductionSetting.OlderLimit >= entity.Age
                    && (
                        a.InsuranceBatchIntroductionSetting.YoungerLimit == null
                        || a.InsuranceBatchIntroductionSetting.YoungerLimit
                            <= entity.Age
                    )
        );

        if (!staffIntroduction)
        {
            query = query.Where(
                a =>
                    a.InsuranceBatchIntroductionSetting == null
                    || a.InsuranceBatchIntroductionSetting.StaffIntroductionOnly
                        == false
            );
        }

        var adviserIds = query.Select(a => a.Id).ToList();

        return adviserIds;
    }

    public static ICollection<InsuranceAdviser> SortAdvisersByMatchingRate(
        IQueryable<InsuranceAdviser> query,
        InsuranceBatchIntroduction entity,
        PersonalitySurvey? survey
    )
    {
        // 日付でランダムシードが変わる様に変更
        var date = DateTime.UtcNow;
        var random = new Random(
            date.Year * 10000 + date.Month * 100 + date.Day
        );

        // 優先順位付のロジック
        var results = new List<AdviserAndPoint>();
        query = query
            .Include(a => a.InsuranceCompany)
            .Include(a => a.PersonalitySurvey);
        foreach (var adviser in query.ToArray())
        {
            var point = 0.0;

            // 対応業務のカバー率(cover of work types)
            var investorWorkType = entity.AdviserWorkTypes.Select(w => w.Id);
            var matchedWorkType = adviser
                .GetWorkTypes()
                .Intersect(investorWorkType)
                .ToArray();
            point += matchedWorkType.Count() / (double)investorWorkType.Count();

            //どんな人に相談したいか入力でのポイント加算
            // point += PointFromAdviserExperience(adviser, entity) * 0.1;

            var personalityPoint = 0;
            if (
                survey != null
                && adviser.PersonalitySurvey != null
                && adviser.PersonalitySurvey.SubmitTime != null
            )
                personalityPoint = CalculatePersonalityPoint(survey, adviser);

            results.Add(
                new AdviserAndPoint
                {
                    Adviser = adviser,
                    Point = CalculateAdviserPoint(point, adviser),
                    PersonalityPoint = personalityPoint,
                    Random = random.Next()
                }
            );
        }

        return results
            .OrderByDescending(a => a.Point)
            //.ThenByDescending(a => a.PersonalityPoint)
            .ThenBy(a => a.Random)
            .Select(a => a.Adviser)
            .ToArray();
    }

    public static IQueryable<InsuranceAdviser> SortAdvisersByAccessCount(
        IQueryable<InsuranceAdviser> query
    )
    {
        return query.OrderByDescending(a => a.ProfileAccessCount);
    }

    public static IQueryable<InsuranceAdviser> SortAdvisersByRequestedCount(
        IQueryable<InsuranceAdviser> query,
        int period
    )
    {
        var result = query
            .Select(
                a =>
                    new
                    {
                        InsuranceAdviser = a,
                        Count = a.InsuranceBatchIntroductions
                            .Where( // 30日以内のリクエストのみをカウント
                                b =>
                                    b.Status
                                        == InsuranceBatchIntroductionStatus.Request
                                    && b.CreatedTime
                                        > DateTime.UtcNow.AddDays(period * -1)
                            )
                            .Count()
                    }
            )
            .OrderByDescending(a => a.Count);

        return result.Select(a => a.InsuranceAdviser);
    }

    public async Task<InsuranceBatchIntroduction> GenerateBatchIntroductionForIntroduction(
        InsuranceBatchIntroductionData data,
        PersonalitySurvey? survey,
        string remoteHost,
        AdviserSortOptionForBatchIntroduction sortOption
    )
    {
        var entity = _model.Create(
            data.To(
                new InsuranceBatchIntroduction(),
                _context,
                remoteHost,
                InsuranceBatchIntroductionStatus.Introduction,
                sortOption
            )
        );
        entity.PersonalitySurvey = survey;

        // Save introduced advisers.
        var adviserIds = await GetAdviserIds(entity, false);
        var query = _context.InsuranceAdvisers.Where(
            a => adviserIds.Contains(a.Id)
        );

        query = query.Include(a => a.InsuranceCompany);

        var sortedAdvisers = SortAdvisers(query, sortOption, survey, entity);

        entity.Advisers = ExcludeSameCompanyRegularEmployee(sortedAdvisers);
        SaveIntroducedAdvisers(sortOption, entity);
        return entity;
    }

    public async Task<InsuranceBatchIntroduction> GenerateBatchIntroductionForStaff(
        string? name,
        InsuranceBatchIntroductionData data,
        PersonalitySurvey? survey,
        string remoteHost,
        AdviserSortOptionForBatchIntroduction sortOption
    )
    {
        var entity = _model.Create(
            data.To(
                new InsuranceBatchIntroduction(),
                _context,
                remoteHost,
                InsuranceBatchIntroductionStatus.Introduction,
                sortOption
            )
        );

        entity.PersonalitySurvey = survey;
        var adviserIds = await GetAdviserIds(entity, true);
        var query = _context.InsuranceAdvisers.Where(
            a => adviserIds.Contains(a.Id)
        );
        query = query.Include(a => a.InsuranceCompany);

        if (!string.IsNullOrEmpty(name))
        {
            query = query.Where(
                a => (a.FamilyName + a.FirstName).Contains(name)
            );
        }

        ICollection<InsuranceAdviser> sortedAdvisers = SortAdvisers(
            query,
            sortOption,
            survey,
            entity
        );

        entity.Advisers = query.ToList();

        SaveIntroducedAdvisers(sortOption, entity);
        return entity;
    }

    private static ICollection<InsuranceAdviser> SortAdvisers(
        IQueryable<InsuranceAdviser> query,
        AdviserSortOptionForBatchIntroduction sortOption,
        PersonalitySurvey? survey,
        InsuranceBatchIntroduction entity
    )
    {
        ICollection<InsuranceAdviser> sortedAdvisers;

        if (sortOption == AdviserSortOptionForBatchIntroduction.AccessCount)
            sortedAdvisers = SortAdvisersByAccessCount(query).ToArray();
        else if (
            sortOption == AdviserSortOptionForBatchIntroduction.RequestedCount
        )
            sortedAdvisers = SortAdvisersByRequestedCount(query, 30).ToArray();
        else
            sortedAdvisers = SortAdvisersByMatchingRate(query, entity, survey)
                .ToArray();
        return sortedAdvisers;
    }

    private void SaveIntroducedAdvisers(
        AdviserSortOptionForBatchIntroduction sortOption,
        InsuranceBatchIntroduction entity
    )
    {
        for (int i = 0; i < entity.Advisers.Count; i++)
        {
            _model.Create(
                new InsuranceAdviserIntroduction()
                {
                    Adviser = entity.Advisers.ElementAt(i),
                    InsuranceBatchIntroduction = entity,
                    SortOption = sortOption,
                    Rank = i + 1
                }
            );
        }
    }

    private static double PointBasedOnAssetRange(
        AssetRanges assetRange,
        CustomerStatics stats
    )
    {
        double point = 0;
        point += assetRange switch
        {
            AssetRanges.B0_A500 => stats.OwnedAssetsLess500 ?? 0,
            AssetRanges.B500_A1000 => stats.OwnedAssetsLess1000 ?? 0,
            AssetRanges.B1000_A2000 => stats.OwnedAssetsLess3000 ?? 0,
            AssetRanges.B2000_A3000 => stats.OwnedAssetsLess3000 ?? 0,
            AssetRanges.B1000_A3000 => stats.OwnedAssetsLess3000 ?? 0,
            AssetRanges.B3000_A5000 => stats.OwnedAssetsLess5000 ?? 0,
            AssetRanges.B5000_A7000 => stats.OwnedAssetsLess7000 ?? 0,
            AssetRanges.B7000_A10000 => stats.OwnedAssetsLess10000 ?? 0,
            AssetRanges.B10000_A20000 => stats.OwnedAssetsLess50000 ?? 0,
            AssetRanges.B20000_A30000 => stats.OwnedAssetsLess50000 ?? 0,
            AssetRanges.B30000_A40000 => stats.OwnedAssetsLess50000 ?? 0,
            AssetRanges.B40000_A50000 => stats.OwnedAssetsLess50000 ?? 0,
            AssetRanges.B50000_A100000
                => stats.OwnedAssetGreaterEqual50000 ?? 0,
            AssetRanges.B100000 => stats.OwnedAssetGreaterEqual50000 ?? 0,
            _
                => throw new ArgumentOutOfRangeException(
                    $"'{assetRange}' is not a valid value for AssetRange"
                )
        };
        return point;
    }

    private static double PointBasedOnOccupation(
        InvestorOccupationTypes OccupationType,
        CustomerStatics stats
    )
    {
        double point = 0;
        point += OccupationType switch
        {
            InvestorOccupationTypes.OfficeWorker
                => stats.OccupationOfficeWorker ?? 0,
            InvestorOccupationTypes.Unemployed
                => stats.OccupationUnemployed ?? 0,
            InvestorOccupationTypes.Proprietor
                => stats.OccupationProprietor ?? 0,
            InvestorOccupationTypes.Executive
                => stats.OccupationProprietor ?? 0,
            InvestorOccupationTypes.Government
                => stats.OccupationGovernment ?? 0,
            InvestorOccupationTypes.Profession
                => stats.OccupationProfession ?? 0,
            InvestorOccupationTypes.Doctor => stats.OccupationDoctor ?? 0,
            _
                => throw new ArgumentOutOfRangeException(
                    $"'{OccupationType}' is not a valid value for OccupationType"
                )
        };
        return point;
    }

    private static int CalculateAdviserPoint(
        double point,
        InsuranceAdviser adviser
    )
    {
        if (adviser.InsuranceCompany == null)
            return 0;

        point =
            point * adviser.InsuranceCompany.BatchIntroductionPriority / 100;

        //変更点
        point =
            point
            * adviser.InsuranceBatchIntroductionSetting.IntroductionRate
            / 100;

        return (int)(point * 100);
    }

    private static int CalculatePersonalityPoint(
        PersonalitySurvey survey,
        InsuranceAdviser adviser
    )
    {
        var personalityPoint = 0;
        var adviserSurvey = adviser.PersonalitySurvey;

        personalityPoint +=
            100
            - Math.Abs(
                (survey.NeuroticismStandardScore ?? 50)
                    - (adviserSurvey?.NeuroticismStandardScore ?? 50)
            );
        personalityPoint +=
            100
            - Math.Abs(
                (survey.OpennessStandardScore ?? 50)
                    - (adviserSurvey?.OpennessStandardScore ?? 50)
            );
        return personalityPoint;
    }

    public static ICollection<InsuranceAdviser> ExcludeSameCompanyRegularEmployee(
        IEnumerable<InsuranceAdviser> advisers,
        int? limit = null
    )
    {
        var results = new List<InsuranceAdviser>();
        var excludeCompany = new HashSet<Ulid>();
        foreach (var adviser in advisers)
        {
            if (
                adviser.InsuranceCompany == null
                || adviser.CompanyByCompanyIntroduction == true
                    && excludeCompany.Contains(adviser.InsuranceCompany.Id)
            )
                continue;

            results.Add(adviser);
            if (limit != null && results.Count >= limit)
                break;

            if (adviser.CompanyByCompanyIntroduction == true)
                excludeCompany.Add(adviser.InsuranceCompany.Id);
        }

        return results;
    }

    public async Task SendNotificationOnSearch(InsuranceBatchIntroduction data)
    {
        var template = new InsuranceBatchIntroductionMailTemplate(
            data,
            _context,
            _settings,
            false
        );
        await _chatWork.TrySend(
            _settings.Value.Investors.InsuranceChatWorkRoomId,
            template.GetContentsForNotificationOnSearch()
        );
        try
        {
            await _amazonCloudWatch.PutMetricDataAsync(
                new PutMetricDataRequest
                {
                    MetricData = new List<MetricDatum>
                    {
                        new MetricDatum
                        {
                            Dimensions = new List<Dimension> { _dimension },
                            MetricName = "Introduction",
                            Unit = StandardUnit.Count,
                            Value = 1,
                        }
                    },
                    Namespace = _settings
                        .Value
                        .Investors
                        .CloudWatchMetricsNamespace
                }
            );
        }
        catch (Exception e)
        {
            Log.Error(e, "Failed to send metric to CloudWatch");
        }
    }

    private class AdviserAndPoint
    {
        public InsuranceAdviser Adviser { get; set; }
        public int Point { get; set; }
        public int PersonalityPoint { get; set; }
        public int Random { get; set; }
    }

    public ICollection<Ulid> FilterAcceptedAdvisers(
        ICollection<Ulid> adviserIds
    )
    {
        return AcceptedAdviserQuery
            .Where(a => adviserIds.Contains(a.Id))
            .Select(a => a.Id)
            .ToArray();
    }

    public async Task SendMail(
        Guid accessKey,
        StaffInsuranceBatchIntroductionManagementData data,
        string watashiIfaUrl
    )
    {
        Log.Information(
            "Contact data by {MailAddress} from {OriginUrl}",
            data.EMail,
            data.OriginUrl
        );

        var mailFrom = new MailboxAddress(
            _settings.Value.Investors.InsuranceConsultationRequestMail.FromName,
            _settings
                .Value
                .Investors
                .InsuranceConsultationRequestMail
                .FromAddress
        );

        var error = false;
        InsuranceBatchIntroductionSurveyRequestMailTemplate template =
            new(accessKey, data, _context);
        string content = template.GetContentsForInvestor(watashiIfaUrl);

        error =
            !await _emailService.TrySendMail(
                BatchIntroductionSurveyRequestMailTemplate.GetInvestorSubject(),
                mailFrom,
                data.EMail,
                content,
                data.OriginUrl
            ) || error;

        if (error)
            throw new NotificationMailError(data.OriginUrl, data.EMail);

        var staffNotificationMails = _settings
            .Value
            .Investors
            .InsuranceConsultationRequestMail
            .NotificationMailAddresses;

        // Send mail to the adviser-navi staff.
        foreach (var mail in staffNotificationMails)
        {
            await _emailService.TrySendMail(
                template.GetNotificationSubject(!error),
                mailFrom,
                mail,
                content
                    + $@"
                    投資家名：{data.Name}
                    投資家メールアドレス：{data.EMail}
                    検索日：{data.CreatedTime}
                    検索ID： {data.Id}
                    ",
                data.OriginName
            );
        }
    }

    public void CheckValidInvestor(InsuranceBatchIntroduction entity) =>
        _commonMatchingLogic.CheckValidInvestor(entity);
}
