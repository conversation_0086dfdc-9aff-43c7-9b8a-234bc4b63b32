using Microsoft.EntityFrameworkCore;
using Adnavi.Utils.Models;
using NUlid;
using Adnavi.Domain.Models.Profiles.InvestorProfiles;
using Adnavi.Domain.Models.Profiles.InsuranceCompanyProfiles;

namespace Adnavi.Domain.Models.Invoices.MatchingInvoices;

/// <summary>
/// IFA紹介手数料
/// </summary>
/// <seealso cref="InsuranceReferralFeeData" />
[Index(nameof(InsuranceCompanyId), nameof(StartDate), IsUnique = true)]
public class InsuranceReferralFee : IHasId, IInsuranceReferralFee
{
    #region Static Member =============================================

    /// <summary>
    /// デフォルトの紹介手数料テーブル
    /// </summary>
    public static InsuranceFeeTable DefaultIntroductionFeeTable =>
        new InsuranceFeeTable
        {
            Income_B0_A500 = 0,
            Income_B500_A800 = 30_000,
            Income_B800_A1000 = 60_000,
            Income_B1000_A1500 = 100_000,
            Income_B1500_A2000 = 150_000,
            Income_B3000_A4000 = 150_000,
            Income_B4000 = 150_000,
        };

    /// <summary>
    /// デフォルトの面談手数料テーブル
    /// </summary>
    public static InsuranceFeeTable DefaultInterviewFeeTable =>
        new InsuranceFeeTable
        {
            Income_B0_A500 = 0,
            Income_B500_A800 = 30_000,
            Income_B800_A1000 = 60_000,
            Income_B1000_A1500 = 100_000,
            Income_B1500_A2000 = 150_000,
            Income_B3000_A4000 = 150_000,
            Income_B4000 = 150_000,
        };

    public static int GetDefaultIntroductionFee(AnnualIncomes annualIncome) =>
        DefaultIntroductionFeeTable.GetFee(annualIncome);

    public static int GetDefaultInterviewFee(AnnualIncomes annualIncome) =>
        DefaultInterviewFeeTable.GetFee(annualIncome);

    #endregion

    /// <summary>ID</summary>
    public Ulid Id { get; set; }

    /// <summary>IFA法人ID</summary>
    public Ulid InsuranceCompanyId { get; set; }

    // <summary>IFA法人</summary>
    public virtual InsuranceCompany InsuranceCompany { get; set; }

    /// <summary>手数料の適用開始日付</summary>
    public DateOnly StartDate { get; set; }

    /// <summary>送客手数料テーブル</summary>
    public InsuranceFeeTable MatchingFeeTable { get; set; }

    /// <summary>面談手数料テーブル</summary>
    public InsuranceFeeTable InterviewFeeTable { get; set; }

    public int GetIntroductionFee(AnnualIncomes annualIncome) =>
        MatchingFeeTable.GetFee(annualIncome);

    public int GetInterviewFee(AnnualIncomes annualIncome) =>
        InterviewFeeTable.GetFee(annualIncome);
}
