using Adnavi.Domain.DTOs.Profiles.AdviserColleagueEvaluations;
using Adnavi.Domain.Models;
using Adnavi.Domain.Models.Profiles;
using Adnavi.Domain.Models.Profiles.AdviserProfiles;
using Microsoft.EntityFrameworkCore;
using NUlid;

namespace Adnavi.Domain.Logics.Profiles.AdviserColleagueEvaluations;

public class AdviserColleagueEvaluationLogic
{
    private readonly AdnaviDomainContext _context;
    private readonly ModelUtils _model;

    public AdviserColleagueEvaluationLogic(
        AdnaviDomainContext context,
        ModelUtils model
    )
    {
        _context = context;
        _model = model;
    }

    // 評価の取得Get
    private IQueryable<AdviserColleagueEvaluation> Get(
        Ulid adviserId,
        string? text
    )
    {
        IQueryable<AdviserColleagueEvaluation> query =
            _context.AdviserColleagueEvaluations
                .Where(c => c.AdviserId == adviserId)
                .Include(a => a.Adviser);
        return query;
    }

    // 自分の書いた評価の一覧取得Search
    public IEnumerable<AdviserColleagueEvaluation> Search(
        Adviser adviser,
        string? text,
        uint offset = 0,
        uint count = 20
    ) => Get(adviser.Id, text).OrderByDescending(a => a.ModifiedTime).ToArray();

    // 評価の削除 Delete
    public async Task Delete(Ulid Id)
    {
        var AdviserColleagueEvaluation = await _model.FindAsync(
            _context.AdviserColleagueEvaluations,
            Id
        );
        _model.Delete(AdviserColleagueEvaluation);
    }

    // 評価の作成 Create
    public void Create(AdviserColleagueEvaluationRequest data, Adviser adviser)
    {
        var AdviserColleagueEvaluationCreate = data.To(
            new AdviserColleagueEvaluation { AdviserId = adviser.Id }
        );
        _model.Create(AdviserColleagueEvaluationCreate);
        _context.SaveChanges();
    }

    // 評価の変更 Update
    public async Task Update(
        Ulid Id,
        AdviserColleagueEvaluationRequest data,
        Adviser adviser
    )
    {
        var AdviserColleagueEvaluationUpdate = await _model.FindAsync(
            _context.AdviserColleagueEvaluations,
            Id
        );
        _model.Update(data.To(AdviserColleagueEvaluationUpdate));
        await _context.SaveChangesAsync();
    }
}
