using Adnavi.Domain.Models.Fxs;
using CsvHelper.Configuration.Attributes;
using Microsoft.EntityFrameworkCore;

namespace Adnavi.Domain.Logics.Fxs;

public sealed class FxCsvRecord
{
    public string Id { get; set; }

    [Name("名前")]
    public string Name { get; set; }

    [Name("公式サイトURL")]
    public string? OfficialSiteUrl { get; set; }

    [Name("最小取引単位 (Jpy 円)")]
    public int? MinTradeUnit { get; set; }

    [Name("最小取引単位評価ポイント(5段階)")]
    public int? MinTradeUnitAssessmentPoint { get; set; }

    [Name("アプリの使いやすさテキスト")]
    public string? AppComfortableText { get; set; }

    [Name("スマホアプリの使いやすさスコア評価ポイント(5段階)")]
    public int? AppComfortableTextAssessmentPoint { get; set; }

    [Name("デモ取引期間")]
    public int? DemoTradePeriodDays { get; set; }

    [Name("デモ取引期間評価ポイント(5段階)")]
    public int? DemoTradePeriodAssessmentPoint { get; set; }

    [Name("最大キャッシュバック額 (Jpy 円)")]
    public int? MaxCashBackAmount { get; set; }

    [Name("最大キャッシュバック額評価ポイント(5段階)")]
    public int? MaxCashBackAmountAssessmentPoint { get; set; }

    [Name("米ドル/円スプレッド 最小 銭(= pip = 1/100 円)")]
    public decimal? UsdJpySpread { get; set; }

    [Name("米ドル/円スプレッド 最大 銭(= pip = 1/100 円)")]
    public decimal? UsdJpySpreadMax { get; set; }

    [Name("米ドル/円スプレッド評価ポイント(5段階)")]
    public int? UsdJpySpreadAssessmentPoint { get; set; }

    [Name("ユーロ/円スプレッド 最小 銭(= pip = 1/100 円)")]
    public decimal? EurJpySpread { get; set; }

    [Name("ユーロ/円スプレッド 最大 銭(= pip = 1/100 円)")]
    public decimal? EurJpySpreadMax { get; set; }

    [Name("ユーロ/円スプレッド評価ポイント(5段階)")]
    public int? EurJpySpreadAssessmentPoint { get; set; }

    [Name("ポンド/円スプレッド 最小 銭(= pip = 1/100 円)")]
    public decimal? GbpJpySpread { get; set; }

    [Name("ポンド/円スプレッド 最大 銭(= pip = 1/100 円)")]
    public decimal? GbpJpySpreadMax { get; set; }

    [Name("ポンド/円スプレッド評価ポイント(5段階)")]
    public int? GbpJpySpreadAssessmentPoint { get; set; }

    [Name("豪ドル/円スプレッド 最小 銭(= pip = 1/100 円)")]
    public decimal? AudJpySpread { get; set; }

    [Name("豪ドル/円スプレッド 最大 銭(= pip = 1/100 円)")]
    public decimal? AudJpySpreadMax { get; set; }

    [Name("豪ドル/円スプレッド評価ポイント(5段階)")]
    public int? AudJpySpreadAssessmentPoint { get; set; }

    [Name("ユーロ/米ドルスプレッド 最小 銭(= pip = 1/100 円)")]
    public decimal? EurUsdSpread { get; set; }

    [Name("ユーロ/米ドルスプレッド 最大 銭(= pip = 1/100 円)")]
    public decimal? EurUsdSpreadMax { get; set; }

    [Name("ユーロ/米ドルスプレッド評価ポイント(5段階)")]
    public int? EurUsdSpreadAssessmentPoint { get; set; }

    [Name("キャンペーン用テキスト")]
    public string? CampaignText { get; set; }

    [Name("CTA用テキスト")]
    public string? CtaText { get; set; }

    [Name("バイアススコア (おすすめランキングに使用)")]
    public int BiasedScore { get; set; }

    [Name("作成日時")]
    public DateTime CreatedTime { get; set; }

    [Name("更新日時")]
    public DateTime? ModifiedTime { get; set; }

    [Name("非表示")]
    public bool? Hidden { get; set; }

    private FxCsvRecord() { }

    public static async Task<IEnumerable<FxCsvRecord>> FromQuery(
        IQueryable<Fx> query
    )
    {
        var result = query.Select(
            fx =>
                new FxCsvRecord
                {
                    Id = fx.Id.ToString(),
                    Name = fx.Name,
                    OfficialSiteUrl = fx.OfficialSiteUrl,
                    MinTradeUnit = fx.MinTradeUnit,
                    MinTradeUnitAssessmentPoint =
                        fx.MinTradeUnitAssessmentPoint,
                    AppComfortableText = fx.AppComfortableText,
                    AppComfortableTextAssessmentPoint =
                        fx.AppComfortableTextAssessmentPoint,
                    DemoTradePeriodDays = fx.DemoTradePeriodDays,
                    DemoTradePeriodAssessmentPoint =
                        fx.DemoTradePeriodAssessmentPoint,
                    MaxCashBackAmount = fx.MaxCashBackAmount,
                    MaxCashBackAmountAssessmentPoint =
                        fx.MaxCashBackAmountAssessmentPoint,
                    UsdJpySpread = fx.UsdJpySpread,
                    UsdJpySpreadAssessmentPoint =
                        fx.UsdJpySpreadAssessmentPoint,
                    EurJpySpread = fx.EurJpySpread,
                    EurJpySpreadAssessmentPoint =
                        fx.EurJpySpreadAssessmentPoint,
                    GbpJpySpread = fx.GbpJpySpread,
                    GbpJpySpreadAssessmentPoint =
                        fx.GbpJpySpreadAssessmentPoint,
                    AudJpySpread = fx.AudJpySpread,
                    AudJpySpreadAssessmentPoint =
                        fx.AudJpySpreadAssessmentPoint,
                    EurUsdSpread = fx.EurUsdSpread,
                    EurUsdSpreadAssessmentPoint =
                        fx.EurUsdSpreadAssessmentPoint,
                    CampaignText = fx.CampaignText,
                    CtaText = fx.CtaText,
                    BiasedScore = fx.BiasedScore,
                    CreatedTime = fx.CreatedTime,
                    ModifiedTime = fx.ModifiedTime,
                    Hidden = fx.Hidden
                }
        );

        return await result.ToListAsync();
    }
}
