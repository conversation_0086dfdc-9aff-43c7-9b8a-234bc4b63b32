using Adnavi.Utils;
using Adnavi.Utils.Images;
using Adnavi.Domain.Logics.Common.Images;
using NUlid;
using Serilog;

namespace Adnavi.Domain.Logics.Profiles.AdviserProfiles;

public class PhotoCollection
{
    private readonly S3Image _s3Image;
    private readonly S3Utils _s3;
    private readonly Ulid _adviserId;
    private readonly Ulid _inquiryId;
    private readonly string? _fileKey;
    private readonly ImageService _imageService;
    private readonly AdnaviDomainContext _context;

    private static readonly List<ImageFileSpec> _fileSpecs =
        new()
        {
            new ImageFileSpec(ImageFileTypes.Jpeg, false),
            new ImageFileSpec(ImageFileTypes.Jpeg, 600, 600, true),
            new ImageFileSpec(ImageFileTypes.Jpeg, 300, 300, false),
            new ImageFileSpec(ImageFileTypes.Webp, 600, 600, false),
            new ImageFileSpec(ImageFileTypes.Webp, 300, 300, false),
        };

    public PhotoCollection(
        S3Utils s3,
        Ulid adviserId,
        string? fileKey,
        ImageService imageService,
        AdnaviDomainContext context,
        Ulid? inquiryId = null
    )
    {
        _s3Image = new S3Image(s3);
        _adviserId = adviserId;
        _fileKey = fileKey;
        _imageService = imageService;
        _context = context;
        _inquiryId = inquiryId ?? Ulid.Empty;
        _s3 = s3;
    }

    internal IEnumerable<ImageSpecAndUrl> GetUrls(string baseUrl) =>
        _fileSpecs
            .Where(s => s.MaxWidth != null)
            .Select(
                s =>
                    new ImageSpecAndUrl
                    {
                        Id = _context.AdviserPhotoCollections
                            .Where(
                                a =>
                                    !string.IsNullOrWhiteSpace(a.PhotoFileKey)
                                    && a.PhotoFileKey == _fileKey
                            )
                            .Select(a => a.Id)
                            .FirstOrDefault(),
                        MaxWidth = s.MaxWidth,
                        MaxHeight = s.MaxHeight,
                        MediaType = s.MediaType,
                        Url = $"{baseUrl}/{PhotoS3Key}-{s.FileName}",
                        Fallback = s.Fallback,
                    }
            );

    internal async Task<IEnumerable<ImageSpecAndUrl>> GetNewUrls(
        Ulid newPhotoFileUlid
    )
    {
        var result = await _imageService.GetConvertedImageUrls(
            newPhotoFileUlid,
            _fileSpecs
        );

        return result;
    }

    internal IEnumerable<ImageSpecAndUrl> GetInquiryUrls(string baseUrl) =>
        _fileSpecs
            .Where(s => s.MaxWidth != null)
            .Select(
                s =>
                    new ImageSpecAndUrl
                    {
                        Id = _context.AdviserPhotoCollections
                            .Where(
                                a =>
                                    !string.IsNullOrWhiteSpace(a.PhotoFileKey)
                                    && a.PhotoFileKey == _fileKey
                            )
                            .Select(a => a.Id)
                            .FirstOrDefault(),
                        MaxWidth = s.MaxWidth,
                        MaxHeight = s.MaxHeight,
                        MediaType = s.MediaType,
                        Url =
                            _s3.CreateUrlIfExists(
                                InquiryPhotoS3Key + "-" + s.FileName
                            ).Result ?? string.Empty,
                        Fallback = s.Fallback,
                    }
            );

    public async Task Upload(Stream input)
    {
        Log.Information("Upload image {key}", PhotoS3Key);

        await _s3Image.Upload(
            await ToBinaryArray(input),
            PhotoS3Key,
            _fileSpecs
        );
    }

    public async Task UploadInquiryPhoto(Stream input)
    {
        Log.Information("Upload inquiry image {key}", InquiryPhotoS3Key);

        await _s3Image.Upload(
            await ToBinaryArray(input),
            InquiryPhotoS3Key,
            _fileSpecs
        );
    }

    public async Task Delete()
    {
        try
        {
            Log.Information("Delete image {key}", PhotoS3Key);
            await _s3Image.Delete(PhotoS3Key, _fileSpecs);
        }
        catch (Exception ex)
        {
            Log.Warning(
                ex,
                "The profile photo file cannot deleted. {adviserId}, {fileKey}",
                _adviserId,
                _fileKey
            );
        }
    }

    private string PhotoS3Key =>
        $"advisers/{_adviserId}/profile/photoCollection/{_fileKey}";

    private string InquiryPhotoS3Key =>
        $"advisers/{_adviserId}/inquiry/{_inquiryId}/{_fileKey}";

    private static async Task<byte[]> ToBinaryArray(Stream input)
    {
        var stream = new MemoryStream();
        await input.CopyToAsync(stream);
        return stream.ToArray();
    }
}
