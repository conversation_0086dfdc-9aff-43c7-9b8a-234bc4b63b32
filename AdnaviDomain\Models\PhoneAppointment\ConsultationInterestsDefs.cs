using Adnavi.Domain.Common;

namespace Adnavi.Domain.Models.PhoneAppointment;

public class ConsultationInterestsDefs : IDefinitionExport
{
    public string Name => GetType().Name.Replace("Defs", "");
    public string ValueType => nameof(ConsultationInterests);

    public IEnumerable<string> DefinitionNames =>
        new List<string> { $"dict/ja-jp/{Name}", $"list/{Name}" };

    public bool DictMatched(string name) => name == $"dict/ja-jp/{Name}";

    public bool ListMatched(string name) => name == $"list/{Name}";

    public List<(string, string)> GetDefList()
    {
        return new List<(string, string)>
        {
            (nameof(ConsultationInterests.Yes), "はい"),
            (nameof(ConsultationInterests.Neutral), "機会があれば"),
            (nameof(ConsultationInterests.No), "いいえ"),
        };
    }

    public object? GetDict() =>
        GetDefList().ToDictionary(i => i.Item1, i => i.Item2);

    public object? GetList() => GetDefList().Select(i => i.Item1).ToList();
}
