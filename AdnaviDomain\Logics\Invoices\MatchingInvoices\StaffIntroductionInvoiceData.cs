using Adnavi.Domain.Common;
using Adnavi.Domain.Models.Invoices.MatchingInvoices;
using Adnavi.Domain.Models.Profiles.InvestorProfiles;
using Adnavi.Utils;

namespace Adnavi.Domain.Logics.Invoices.MatchingInvoices;

public class StaffIntroductionInvoiceData : IObjectUpdater<IntroductionInvoice>
{
    public YearAndMonth? InvoiceMonth { get; set; }

    public int? SequenceNumber { get; set; }

    ///<summary>請求種別</summary>
    public InvoiceTypes InvoiceType { get; set; }

    ///<summary>請求許可</summary>
    public InvoiceStatuses InvoiceStatus { get; set; }

    /// <summary>作成日</summary>
    public DateTime? CreatedTime { get; set; }

    /// <summary>修正日</summary>
    public DateTime? ModifiedTime { get; set; }

    /// <summary>請求金額</summary>
    public int? InvoiceAmount { get; set; }

    /// <summary>備考</summary>
    public string Remarks { get; set; } = "";

    /// <summary>名前</summary>
    public string? InvestorName { get; set; }

    /// <summary>Email</summary>
    public string? Email { get; set; }

    /// <summary>資産</summary>
    public AssetRanges? AssetRange { get; set; }

    /// <summary> 返金申請事由</summary>

    public string? RefundReason { get; set; } = "";

    /// <summary>デフォルトコンストラクタ</summary>
    public StaffIntroductionInvoiceData() { }

    /// <summary>変換用コンストラクタ</summary>
    public StaffIntroductionInvoiceData(IntroductionInvoice entity)
    {
        InvoiceMonth = entity.InvoiceMonth;
        SequenceNumber = entity.SequenceNumber;
        InvoiceType = entity.InvoiceType;
        InvoiceStatus = entity.InvoiceStatus;
        CreatedTime = entity.CreatedTime;
        ModifiedTime = entity.ModifiedTime;
        InvoiceAmount = entity.InvoiceAmount;
        Remarks = entity.Remarks;
        InvestorName = entity?.MatchingInvoiceManagement.BatchIntroduction.Name;
        Email = entity?.MatchingInvoiceManagement.BatchIntroduction.EMail;
        AssetRange = entity
            ?.MatchingInvoiceManagement
            .BatchIntroduction
            .AssetRange;
        RefundReason = entity?.RefundReason ?? "";
    }

    /// <summary>エンティティ代入用関数</summary>
    public IntroductionInvoice To(IntroductionInvoice e)
    {
        if (InvoiceMonth != null)
            e.InvoiceMonth = InvoiceMonth.Value;

        e.InvoiceStatus = InvoiceStatus;
        e.InvoiceAmount = InvoiceAmount;
        e.Remarks = Remarks;

        if (RefundReason != null)
            e.RefundReason = RefundReason;
        return e;
    }
}
