﻿using System.ComponentModel.DataAnnotations;
using Adnavi.Domain.Models.Accounts;
using Adnavi.Utils.Models;
using NUlid;

namespace Adnavi.Domain.Models.Invoices.RecruitInvoices;

/// <summary>
/// Employment
/// /// </summary>
/// <seealso cref="EmploymentNameResponse" />
public class Employment : IHasId
{
    /// <summary>ID</summary>
    [Required]
    public Ulid Id { get; set; }

    /// <summary>バージョン</summary>
    [Timestamp]
    public byte[]? RowVersion { get; set; }

    /// <summary>企業ID</summary>
    [Required]
    public Ulid OrganizationId { get; set; }

    /// <summary>組織</summary>
    public virtual Organization Organization { get; set; }

    /// <summary>姓</summary>
    [Required]
    public string FamilyName { get; set; }

    /// <summary>名</summary>
    public string? FirstName { get; set; }

    /// <summary>メールアドレス</summary>
    [Required]
    public string EMail { get; set; }

    /// <summary>日付</summary>
    [Required]
    public DateTime Date { get; set; }

    /// <summary>手数料リスト</summary>
    public virtual ICollection<EmployeeFee> Fees { get; set; }
}
