﻿using System.ComponentModel.DataAnnotations;
using Adnavi.Utils.EnumTables;
using Microsoft.EntityFrameworkCore;

namespace Adnavi.Domain.Models.Consultations.ConsultationRequests;

[Index(nameof(Name), IsUnique = true)]
public class InvestmentPurpose : IEnumRecord<InvestmentPurposes>
{
    /// <summary>ID</summary>
    [Required]
    public InvestmentPurposes Id { get; set; }

    /// <summary>名前</summary>
    [Required]
    public string Name { get; set; }

    /// <summary>表示名</summary>
    [Required]
    public string DisplayName { get; set; }
}
