using System.Globalization;
using Adnavi.Domain.DTOs.Recruits.IfaDataBaseUpdates;
using Adnavi.Domain.Models.Accounts;
using Adnavi.Domain.Models.Invoices.RecruitInvoices;
using Adnavi.Utils;
using Adnavi.Utils.Exceptions;
using CsvHelper;
using CsvHelper.Configuration;
using Microsoft.EntityFrameworkCore;
using NUlid;

namespace Adnavi.Domain.Logics.Invoices.RecruitInvoices.Csv;

public class InvoiceCsvReader
{
    private readonly AdnaviDomainContext _context;

    public InvoiceCsvReader(AdnaviDomainContext context)
    {
        _context = context;
    }

    public async Task UpdateDataFromS3(
        S3Utils s3,
        string s3InvoiceOrganizationDataKey,
        string s3InvoiceEmployeeDataKey
    )
    {
        var organizationCsvStream = await s3.Get(s3InvoiceOrganizationDataKey);
        var employeeCsvStream = await s3.Get(s3InvoiceEmployeeDataKey);

        await UpdateData(organizationCsvStream, employeeCsvStream);
    }

    public async Task UpdateData(
        Stream organizationCsvStream,
        Stream employeeCsvStream
    )
    {
        try
        {
            await LoadOrganizations(organizationCsvStream);
            await LoadEmployee(employeeCsvStream);
        }
        catch (ValidationException ex)
        {
            throw new InvalidCsvError(typeof(IfaDatabaseCompanyData), ex);
        }
    }

    private async Task LoadOrganizations(Stream csvStream)
    {
        var config = new CsvConfiguration(CultureInfo.InvariantCulture) { };

        using var reader = new StreamReader(csvStream);
        using var csv = new CsvReader(reader, config);
        while (csv.Read())
        {
            OrganizationCsvData data;
            try
            {
                data = csv.GetRecord<OrganizationCsvData>();
            }
            catch (ValidationException ex)
            {
                throw new InvalidCsvError(typeof(OrganizationCsvData), ex);
            }

            var id = Ulid.Parse(data.Id);
            var organization =
                await _context.Organizations.SingleOrDefaultAsync(
                    c => c.Id == id
                );

            if (organization == null)
            {
                organization = new Organization { Id = id };
                _context.Organizations.Add(organization);
            }

            organization.Name = data.OrganizationName;
            organization.InvoiceRemarks = data.Remarks;
        }
    }

    private async Task LoadEmployee(Stream csvStream)
    {
        var config = new CsvConfiguration(CultureInfo.InvariantCulture) { };

        using var reader = new StreamReader(csvStream);
        using var csv = new CsvReader(reader, config);
        while (csv.Read())
        {
            EmploymentCsvData data;
            try
            {
                data = csv.GetRecord<EmploymentCsvData>();
            }
            catch (ValidationException ex)
            {
                throw new InvalidCsvError(typeof(EmploymentCsvData), ex);
            }

            var id = Ulid.Parse(data.Id);
            var employment = await _context.Employments
                .Include(e => e.Fees)
                .SingleOrDefaultAsync(e => e.Id == id);
            if (employment == null)
            {
                employment = new Employment
                {
                    Id = id,
                    Fees = new List<EmployeeFee>(),
                };
                _context.Employments.Add(employment);
            }

            var startMonth = new DateTime(
                data.StartDate.Year,
                data.StartDate.Month,
                1
            );

            employment.OrganizationId = Ulid.Parse(data.OrganizationId);
            employment.FirstName = data.FirstName;
            employment.FamilyName = data.FamilyName;
            employment.EMail = data.EMail;
            employment.Date = startMonth;

            employment.Fees = CreateFees(startMonth, employment.Fees, data);
        }
    }

    private static List<EmployeeFee> CreateFees(
        DateTime startMonth,
        ICollection<EmployeeFee> fees,
        EmploymentCsvData data
    )
    {
        var result = new List<EmployeeFee>();

        int year = 0;
        var rates = CreateRatesArray(data);
        foreach (var rate in rates)
        {
            year++;
            var start = startMonth.AddYears(year - 1);

            var fee = fees.SingleOrDefault(f => f.YearNumber == year);
            fee ??= new EmployeeFee { Id = Ulid.NewUlid() };

            fee.YearNumber = (uint)year;
            fee.RatePercent = rate;
            fee.StartYearMonth = new YearAndMonth(start);
            fee.EndYearMonth = new YearAndMonth(start.AddYears(1).AddDays(-1));
            result.Add(fee);
        }

        return result;
    }

    private static byte[] CreateRatesArray(EmploymentCsvData data) =>
        new[] { data.Y1, data.Y2, data.Y3, data.Y4, data.Y5, data.Y6 };
}
