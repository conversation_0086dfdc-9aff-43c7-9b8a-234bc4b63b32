/* AIよる要約(2025-06-17更新)

### `MatchingInvoicesLogic` クラスの要約

この `MatchingInvoicesLogic` クラスは、IFA（独立系ファイナンシャルアドバイザー）への投資家紹介（マッチング）に伴って発生する、**請求関連の業務ロジック**全体を管理する中心的なクラスです。請求データの生成から検索、手数料計算、更新、関係者への通知まで、幅広い責務を担っています。

#### 主な機能

1.  **請求データの検索と集計**
    *   IFA法人、アドバイザー、請求月、ステータスなど、多様な条件で請求データを柔軟に検索します (`GetQuery`, `SearchInvoices`)。
    *   検索結果の件数取得 (`GetCount`) や、合計請求額、承認済み金額などの集計 (`AggregateInvoices`) を行います。
    *   検索結果をCSV形式でダウンロードするためのデータ生成機能 (`GetCsvData`) も提供します。

2.  **手数料の自動計算**
    *   投資家の紹介（マッチング）時および面談実施時の手数料を、IFA法人ごとに設定された料金テーブルやデフォルト設定、適用可能なキャンペーン・プロモーション情報に基づいて自動で計算します
         (`CalculateMatchingInvoiceFee`, `CalculateInterviewInvoiceFee`)。
    *   過去の請求データに対して、最新の料金設定で手数料を再計算する機能 (`RecalculateReferenceFee`) も備えています。

3.  **請求データのライフサイクル管理**
    *   **生成**: 投資家がアドバイザーに紹介された際に「マッチング請求」を、面談が実施された際に「面談請求」を自動で生成・更新します (`CreateMatchingInvoice`, `UpdateInterviewInvoice`)。
    *   **更新**: 管理画面からの操作に応じて、個別の請求情報（ステータス変更など）や、案件全体の情報（進捗ステータス、面談日など）を更新します (`UpdateInvoice`, `UpdateManagement`)。
    *   **返金**: 返金処理のための請求データも作成できます (`CreateRefundInvoice`)。

4.  **自動メール通知**
    *   案件の進捗ステータスが「連絡つかず」などに更新された際に、投資家へリマインドメールを送信します。
    *   進捗が長期間更新されていない案件がある場合、担当のアドバイザーやIFA法人担当者へリマインドメールを送信するための対象者リストを生成します (`GetUsersWithNoProgressSet`)。

5.  **アクセス制御（権限管理）**
    *   アドバイザーやIFA法人が、自身に関係のない請求情報にアクセスできないようにチェックするセキュリティ機能を提供します (`CheckInvoiceAccess`, `CheckManagementAccess`)。

6.  **証跡管理**
    *   請求案件に紐づく写真（面談の証跡など）をアップロード、取得、削除する機能を提供します (`GetPhotoCollection`, `UpdatePhotoCollection`, `DeletePhotoCollection`)。

このクラスは、データベース (`AdnaviDomainContext`)、メール送信サービス (`EMailService`)、採番サービス (`SequenceNumberService`) などと連携し、
マッチング請求に関する複雑なビジネスルールを実装しています。
*/

using Adnavi.Domain.Common;
using Adnavi.Domain.Logics.Common;
using Adnavi.Domain.Logics.Common.SequenceNumber;
using Adnavi.Domain.Logics.Invoices.MatchingInvoices.Promotions;
using Adnavi.Domain.DTOs.Invoices.MatchingInvoices;
using Adnavi.Domain.Models;
using Adnavi.Domain.Models.Commons.SequenceNumber;
using Adnavi.Domain.Models.Consultations.BatchIntroductions;
using Adnavi.Domain.Models.Invoices.MatchingInvoices;
using Adnavi.Domain.Models.Invoices.MatchingInvoices.FeeTables;
using Adnavi.Domain.Models.Profiles.AdviserProfiles;
using Adnavi.Domain.Models.Profiles.IfaCompanyProfiles;
using Adnavi.Utils;
using Adnavi.Utils.Exceptions;
using Microsoft.EntityFrameworkCore;
using NUlid;
using Serilog;
using Adnavi.Domain.Models.Accounts;
using Adnavi.Domain.Logics.Common.EMails;
using Microsoft.Extensions.Options;
using MimeKit;
using System.Web;

namespace Adnavi.Domain.Logics.Invoices.MatchingInvoices;

public partial class MatchingInvoicesLogic
{
    private readonly AdnaviDomainSettings _settings;
    private readonly ModelUtils _model;
    private readonly IfaPromotionFactory _promotions;
    private readonly SequenceNumberService _seq;
    private readonly AdnaviDomainContext _context;
    private readonly EMailService _emailService;

    public MatchingInvoicesLogic(
        AdnaviDomainContext context,
        IfaPromotionFactory promotions,
        ModelUtils model,
        SequenceNumberService seq,
        EMailService eMailService,
        IOptions<AdnaviDomainSettings> settings
    )
    {
        _model = model;
        _promotions = promotions;
        _seq = seq;
        _context = context;
        _emailService = eMailService;
        _settings = settings.Value;
    }

    #region 検索処理 ============================================================

    private IQueryable<MatchingInvoiceManagement> GetQuery(
        Ulid? ifaCompanyId = null,
        Ulid? adviserId = null,
        InvoiceTypes? invoiceType = null,
        YearAndMonth? invoiceMonth = null,
        bool holdOnly = false,
        bool approvedOnly = false,
        bool byInvoiceMonth = false,
        bool interviewedWithNoStatus = false,
        MatchingInvoiceOrderColumn? orderColumn = null,
        bool descendence = false
    )
    {
        var query = _context.MatchingInvoiceManagements
            .IgnoreQueryFilters()
            .AsQueryable();
        if (ifaCompanyId != null)
            query = query.Where(a => a.IfaCompanyId == ifaCompanyId);
        if (adviserId != null)
            query = query.Where(a => a.AdviserId == adviserId);
        if (invoiceType != null)
            query = query.Where(
                a => a.Invoices.Any(i => i.InvoiceType == invoiceType)
            );
        if (interviewedWithNoStatus)
        {
            query = query.Where(
                a =>
                    a.Invoices.Any(i => i.InvoiceType == InvoiceTypes.Interview)
                    && (
                        a.ProgressStatus == null
                        || a.ProgressStatus == ProgressStatuses.Hold
                        || a.ProgressStatus == ProgressStatuses.Spam
                        || a.ProgressStatus == ProgressStatuses.NotAccessMeans
                        || a.ProgressStatus == ProgressStatuses.WrongAccessMeans
                        || a.ProgressStatus == ProgressStatuses.NoContact
                        || a.ProgressStatus == ProgressStatuses.ExistingCustomer
                        || a.ProgressStatus == ProgressStatuses.OnlyOnceContact
                        || a.ProgressStatus
                            == ProgressStatuses.ScheduledInterview
                    )
            );
        }

        if (!byInvoiceMonth && invoiceMonth != null)
        {
            var month = invoiceMonth.Value;
            query = query.Where(
                a =>
                    a.CreatedTime.HasValue
                    && a.CreatedTime.Value >= month.GetStartTimeUtc()
                    && a.CreatedTime.Value <= month.GetEndTimeUtc()
            );
        }
        if (byInvoiceMonth && invoiceMonth != null)
        {
            var month = invoiceMonth.Value;
            query = query.Where(
                a => a.Invoices.Any(i => i.InvoiceMonth == month)
            );
        }

        if (holdOnly)
            query = query.Where(
                a =>
                    a.Invoices.Any(i => i.InvoiceStatus == InvoiceStatuses.Hold)
            );

        if (approvedOnly)
            query = query.Where(
                a =>
                    a.Invoices.Any(
                        i => i.InvoiceStatus == InvoiceStatuses.Approved
                    )
            );

        if (orderColumn != null)
        {
            query = (orderColumn, descendence) switch
            {
                (MatchingInvoiceOrderColumn.Id, true)
                    => query.OrderByDescending(a => a.Id),
                (MatchingInvoiceOrderColumn.Id, false)
                    => query.OrderBy(a => a.Id),

                (MatchingInvoiceOrderColumn.CreatedTime, true)
                    => query
                        .OrderByDescending(a => a.CreatedTime)
                        .ThenByDescending(a => a.Id),
                (MatchingInvoiceOrderColumn.CreatedTime, false)
                    => query.OrderBy(a => a.CreatedTime).ThenBy(a => a.Id),

                (MatchingInvoiceOrderColumn.CompanyName, true)
                    => query.OrderByDescending(
                        a => a.IfaCompany == null ? null : a.IfaCompany.Name
                    ),
                (MatchingInvoiceOrderColumn.CompanyName, false)
                    => query.OrderBy(
                        a => a.IfaCompany == null ? null : a.IfaCompany.Name
                    ),

                (MatchingInvoiceOrderColumn.AssetRange, true)
                    => query.OrderByDescending(
                        a => a.BatchIntroduction.AssetRange
                    ),

                (MatchingInvoiceOrderColumn.AssetRange, false)
                    => query.OrderBy(a => a.BatchIntroduction.AssetRange),

                (MatchingInvoiceOrderColumn.ProgressStatus, true)
                    => query.OrderByDescending(a => a.ProgressStatus),

                (MatchingInvoiceOrderColumn.ProgressStatus, false)
                    => query.OrderBy(a => a.ProgressStatus),

                _ => throw new UnreachableCodeError(),
            };
        }

        return query;
    }

    public Task<int> GetCount(
        Ulid? ifaCompanyId = null,
        Ulid? adviserId = null,
        InvoiceTypes? invoiceType = null,
        YearAndMonth? invoiceMonth = null,
        bool holdOnly = false
    )
    {
        var query = GetQuery(
            ifaCompanyId,
            adviserId,
            invoiceType,
            invoiceMonth,
            holdOnly
        );
        return query.CountAsync();
    }

    public IQueryable<MatchingInvoiceManagement> SearchInvoices(
        Ulid? ifaCompanyId = null,
        Ulid? adviserId = null,
        InvoiceTypes? invoiceType = null,
        YearAndMonth? invoiceMonth = null,
        bool holdOnly = false,
        bool approvedOnly = false,
        bool byInvoiceMonth = false,
        bool interviewedWithNoStatus = false,
        uint offset = 0,
        uint count = 20,
        MatchingInvoiceOrderColumn orderColumn =
            MatchingInvoiceOrderColumn.CreatedTime,
        bool descendence = false
    )
    {
        var query = GetQuery(
            ifaCompanyId,
            adviserId,
            invoiceType,
            invoiceMonth,
            holdOnly,
            approvedOnly,
            byInvoiceMonth,
            interviewedWithNoStatus,
            orderColumn,
            descendence
        );

        return query
            .AsNoTracking()
            .Include(i => i.BatchIntroduction)
            .ThenInclude(i => i.Advisers)
            .Include(i => i.Invoices)
            .Skip((int)offset)
            .Take((int)count);
    }

    public async Task<MatchingInvoicesAggregateData> AggregateInvoices(
        Ulid? ifaCompanyId = null,
        Ulid? adviserId = null,
        InvoiceTypes? invoiceType = null,
        YearAndMonth? invoiceMonth = null,
        bool holdOnly = false,
        bool byInvoiceMonth = false
    )
    {
        var query = GetQuery(
            ifaCompanyId,
            adviserId,
            invoiceType,
            invoiceMonth,
            holdOnly,
            byInvoiceMonth: byInvoiceMonth
        );
        // aggregate adviser count and total amount of invoice.
        var result = await query
            .GroupBy(m => 1)
            .Select(
                g =>
                    new MatchingInvoicesAggregateData(
                        g.Select(a => a.IfaCompanyId).Distinct().Count(),
                        g.Select(a => a.AdviserId).Distinct().Count(),
                        g.Select(a => a.BatchIntroduction.Id)
                            .Distinct()
                            .Count(),
                        // 取り消しされていない請求の合計金額
                        g.Sum(
                            a =>
                                a.Invoices
                                    .Where(
                                        i =>
                                            i.InvoiceStatus
                                            != InvoiceStatuses.Canceled
                                    )
                                    .Sum(i => i.InvoiceAmount)
                        ) ?? 0,
                        g.Sum(
                            a =>
                                a.Invoices
                                    .Where(
                                        i =>
                                            i.InvoiceStatus
                                            == InvoiceStatuses.Approved
                                    )
                                    .Sum(i => i.InvoiceAmount)
                        ) ?? 0,
                        // 保留中の請求の数
                        g.Sum(
                            a =>
                                a.Invoices
                                    .Where(
                                        i =>
                                            i.InvoiceStatus
                                            == InvoiceStatuses.Hold
                                    )
                                    .Count()
                        )
                    )
            )
            .SingleOrDefaultAsync();
        return result ?? new MatchingInvoicesAggregateData();
    }

    private IQueryable<IfaCompany> QueryIfaCompanies(
        YearAndMonth? invoiceMonth
    ) =>
        GetQuery(invoiceMonth: invoiceMonth)
            .Where(x => x.IfaCompany != null)
            .Select(x => x.IfaCompany!)
            .Distinct();

    public async IAsyncEnumerable<SearchableItemData> GetIfaCompanyNameList(
        YearAndMonth? invoiceMonth
    )
    {
        var query = QueryIfaCompanies(invoiceMonth: invoiceMonth);

        await foreach (var company in query.AsAsyncEnumerable())
        {
            yield return new SearchableItemData(
                company.Id,
                company.Name,
                company.NameKana
                    + StringUtils.ToHankakuAlphaNumeric(company.Name)
            );
        }
    }

    private IQueryable<Adviser> QueryAdvisers(
        Ulid? ifaCompanyId,
        YearAndMonth? invoiceMonth
    ) =>
        GetQuery(invoiceMonth: invoiceMonth)
            .Where(
                x =>
                    x.Adviser != null
                    // ifaCompanyIdが指定されていればIfaCompanyで絞り込み
                    && (ifaCompanyId == null || x.IfaCompanyId == ifaCompanyId)
            )
            .Select(x => x.Adviser!)
            .Distinct();

    public IAsyncEnumerable<SearchableItemData> GetAdviserNameList(
        Ulid? ifaCompanyId,
        YearAndMonth? invoiceMonth
    )
    {
        var query = QueryAdvisers(ifaCompanyId, invoiceMonth: invoiceMonth)
            .Select(
                x =>
                    new SearchableItemData(
                        x.Id,
                        x.FamilyName + " " + x.FirstName,
                        x.FamilyNameKana + " " + x.FirstNameKana
                    )
            );

        return query.AsAsyncEnumerable();
    }

    public IEnumerable<MatchingInvoiceManagementCsvRecord> GetCsvData(
        List<MatchingInvoiceManagement> matchingInvoiceManagement
    )
    {
        var records = new List<dynamic>();
        foreach (var b in matchingInvoiceManagement.ToArray())
        {
            var assetRange = _context.AssetRanges
                .Where(
                    ar => ar.Name == b.BatchIntroduction.AssetRange.ToString()
                )
                .Select(ar => ar.DisplayName)
                .FirstOrDefault();

            var gender = _context.GenderTypes
                .Where(g => g.Name == b.BatchIntroduction.Gender.ToString())
                .Select(g => g.DisplayName)
                .FirstOrDefault();

            var preferredFirstContactMethod = _context.FirstContactMethodTypes
                .Where(
                    cm =>
                        cm.Name
                        == b.BatchIntroduction.FirstContactMethodType.ToString()
                )
                .Select(cm => cm.DisplayName)
                .FirstOrDefault();

            var prefecture = _context.Prefectures
                .Where(p => p.Name == b.BatchIntroduction.Prefecture.ToString())
                .Select(p => p.DisplayName)
                .FirstOrDefault();

            var investorOccupationType = _context.InvestorOccupationTypes
                .Where(
                    iot =>
                        iot.Name
                        == b.BatchIntroduction.InvestorOccupationType.ToString()
                )
                .Select(iot => iot.DisplayName)
                .FirstOrDefault();

            var adviserWorkTypes = _context.AdviserWorkTypes
                .Where(
                    awt =>
                        awt.BatchIntroductions.Any(
                            bi => bi.Id == b.BatchIntroduction.Id
                        )
                )
                .Select(awt => awt.DisplayName);

            var contactMethod = _context.ContactMethods
                .Where(
                    cm =>
                        cm.BatchIntroductions.Any(
                            bi => bi.Id == b.BatchIntroduction.Id
                        )
                )
                .Select(cm => cm.DisplayName);

            var record = new MatchingInvoiceManagementCsvRecord
            {
                Id = b.Id.ToString(),
                Name = b.BatchIntroduction.Name,
                Age = b.BatchIntroduction.Age,
                AssetRange = assetRange ?? "",
                Gender = gender ?? "",
                PreferredFirstContactMethod = preferredFirstContactMethod ?? "",
                TelephoneNumber = b.BatchIntroduction.TelephoneNumber,
                EMail = b.BatchIntroduction.EMail,
                Prefecture = prefecture ?? "",
                InvestorOccupationType = investorOccupationType ?? "",
                AdviserWorkType = string.Join(", ", adviserWorkTypes),
                ContactMethod = string.Join(", ", contactMethod),
                InvestorRemarks = b.BatchIntroduction.InvestorRemarks,
            };

            yield return record;
        }
    }

    #endregion

    #region 手数料の計算処理 ==================================================

    /// <summary>
    /// 送客請求データの再計算を行う
    /// </summary>
    /// <param name="year"></param>
    /// <param name="month"></param>
    /// <param name="ifaCompanyId"></param>
    public async Task RecalculateReferenceFee(
        ushort year,
        byte month,
        Ulid? ifaCompanyId = null,
        Ulid? adviserId = null,
        InvoiceTypes? invoiceType = null
    )
    {
        var query = GetQuery(
            ifaCompanyId,
            adviserId,
            invoiceType,
            new YearAndMonth(year, month)
        );

        foreach (
            var management in query
                .Include(m => m.Invoices)
                .Include(m => m.BatchIntroduction)
                .Include(m => m.Adviser)
                .ThenInclude(a => a.IfaCompany)
                .ThenInclude(c => c == null ? null : c.Organization)
                .ToArray()
        )
        {
            await UpdateMatchingInvoice(management, management.Adviser);
            await UpdateInterviewInvoice(management.Id, false);
        }
        await _context.SaveChangesAsync();
    }

    /// <summary>
    /// 企業の指定日付の手数料を取得
    /// </summary>
    /// <param name="ifaCompanyId"></param>
    /// <param name="matchingTimeUtc"></param>
    /// <returns></returns>
    private IfaReferralFee? GetCompanyFee(
        Ulid ifaCompanyId,
        DateTime matchingTimeUtc
    )
    {
        var introductionDate = DateOnly.FromDateTime(
            DateUtils.UtcToJst(matchingTimeUtc)
        );

        var referralFee = _context.IfaReferralFees
            .Where(
                f =>
                    f.IfaCompanyId == ifaCompanyId
                    && f.StartDate <= introductionDate
            )
            .OrderByDescending(a => a.StartDate)
            .FirstOrDefault();

        return referralFee;
    }

    /// <summary>
    /// 送客の手数料を取得
    /// </summary>
    /// <param name="adviser"></param>
    /// <param name="introduction"></param>
    /// <returns>金額と適用したキャンペーン</returns>
    private PromotionAndAmount CalculateMatchingInvoiceFee(
        Adviser adviser,
        BatchIntroduction introduction
    )
    {
        if (adviser.IfaCompanyId == null)
        {
            Log.Fatal("Referral fee not found.");
            return new PromotionAndAmount(null, null);
        }

        var assetRange = introduction.AssetRange;

        var ifaReferralFee = GetCompanyFee(
            adviser.IfaCompanyId.Value,
            introduction.CreatedTime
        );
        // 法人の金額または、デフォルトの金額を取得
        int fee =
            ifaReferralFee?.GetMatchingFee(assetRange, introduction)
            ?? IfaReferralFee.GetDefaultMatchingFee(assetRange, introduction);

        var promotionAmount = new PromotionAndAmount(null, null);
        // キャンペーン除外が有効でなければプロモーション金額を取得
        if (!(ifaReferralFee?.ExcludePromotion ?? false))
        {
            // プロモーション金額を取得
            promotionAmount = _promotions.GetMinimumMatchingFee(
                adviser,
                introduction
            );
        }

        // 一番金額が低くなるプロモーションが通常価格よりも小さければ適用
        var result =
            (promotionAmount != null && promotionAmount.Amount <= fee)
                ? promotionAmount
                : new PromotionAndAmount(null, fee);

        return result;
    }

    /// <summary>
    /// 面談の手数料を取得
    /// </summary>
    /// <param name="adviser"></param>
    /// <param name="introduction"></param>
    /// <returns>金額と適用したキャンペーン</returns>
    private PromotionAndAmount CalculateInterviewInvoiceFee(
        Adviser adviser,
        BatchIntroduction introduction,
        DateTime matchingTimeUtc
    )
    {
        if (adviser.IfaCompanyId == null)
        {
            Log.Fatal(
                "Referral fee not found. {Name}",
                adviser.Id,
                adviser.FullName
            );
            return new PromotionAndAmount(null, null);
        }

        var assetRange = introduction.AssetRange;

        var ifaReferralFee = GetCompanyFee(
            adviser.IfaCompanyId.Value,
            introduction.CreatedTime
        );
        // 法人の金額または、デフォルトの金額を取得
        int fee =
            ifaReferralFee?.GetInterviewFee(assetRange, introduction)
            ?? IfaReferralFee.GetDefaultInterviewFee(assetRange, introduction);

        var promotionAmount = new PromotionAndAmount(null, null);
        // キャンペーン除外が有効でなければプロモーション金額を取得
        if (!(ifaReferralFee?.ExcludePromotion ?? false))
        {
            // プロモーション金額を取得
            promotionAmount = _promotions.GetMinimumInterviewFee(
                adviser,
                introduction
            );
        }
        // 一番金額が低くなるプロモーションが通常価格よりも小さければ適用
        var result =
            (promotionAmount != null && promotionAmount.Amount <= fee)
                ? promotionAmount
                : new PromotionAndAmount(null, fee);

        return result;
    }

    #endregion

    #region 請求登録処理 ======================================================

    /// <summary>
    /// 送客時に請求管理用データと請求データを作成
    /// </summary>
    /// <param name="introduction"></param>
    /// <param name="adviser"></param>
    /// <returns></returns>
    public async Task CreateMatchingInvoice(
        BatchIntroduction introduction,
        Adviser adviser
    )
    {
        var management = _model.Create(
            new MatchingInvoiceManagement(introduction, adviser)
        );
        await _context.SaveChangesAsync();
        await UpdateMatchingInvoice(management, adviser);
    }

    private async Task UpdateMatchingInvoice(
        MatchingInvoiceManagement management,
        Adviser adviser
    )
    {
        var amount = CalculateMatchingInvoiceFee(
            adviser,
            management.BatchIntroduction
        );

        var invoice = management.Invoices
            .Where(i => i.InvoiceType == InvoiceTypes.Matching)
            .FirstOrDefault();

        // なければ新しく作る
        if (invoice == null)
        {
            invoice = new IntroductionInvoice()
            {
                InvoiceType = InvoiceTypes.Matching,
                InvoiceMonth = new YearAndMonth(
                    DateUtils.UtcToJst(management.BatchIntroduction.CreatedTime)
                ),
                SequenceNumber = await _seq.GetValue(SequenceNumberId.Invoice),
            };
            management.Invoices.Add(_model.Create(invoice));
        }

        if (invoice.InvoiceStatus != InvoiceStatuses.Hold)
            return;

        // シーケンス番号が振られていなければ振る
        if (invoice.SequenceNumber == null)
            invoice.SequenceNumber = await _seq.GetValue(
                SequenceNumberId.Invoice
            );

        invoice.InvoiceAmount = amount.Amount;
        invoice.PromotionId = amount.Promotion?.Id;
        invoice.ModifiedTime = DateTime.UtcNow;

        await _context.SaveChangesAsync();
    }

    /// <summary>
    /// 面談時に請求データを作成
    /// </summary>
    /// <param name="matchId"></param>
    /// <param name="date"></param>
    /// <returns></returns>
    public async Task UpdateInterviewInvoice(
        Ulid matchId,
        bool createNotExists = true
    )
    {
        var management = await _model.FindAsync(
            _context.MatchingInvoiceManagements,
            matchId
        );
        if (management.IfaCompanyId == null)
            return;
        if (management.InterviewDate == null)
            return;

        var amount = CalculateInterviewInvoiceFee(
            management.Adviser,
            management.BatchIntroduction,
            management.BatchIntroduction.CreatedTime
        );

        var invoice = management.Invoices
            .Where(i => i.InvoiceType == InvoiceTypes.Interview)
            .FirstOrDefault();

        // なければ新しく作る
        if (invoice == null)
        {
            if (!createNotExists)
                return;

            invoice = _model.Create(
                new IntroductionInvoice()
                {
                    InvoiceType = InvoiceTypes.Interview,
                    SequenceNumber = await _seq.GetValue(
                        SequenceNumberId.Invoice
                    ),
                }
            );
            management.Invoices.Add(_model.Create(invoice));
        }

        if (invoice.InvoiceStatus != InvoiceStatuses.Hold)
            return;

        invoice.InvoiceMonth = new YearAndMonth(
            DateUtils.UtcToJst(management.InterviewDate.Value)
        );
        invoice.InvoiceAmount = amount.Amount;
        invoice.PromotionId = amount.Promotion?.Id;
        invoice.ModifiedTime = DateTime.UtcNow;

        _model.Update(management);
        await _context.SaveChangesAsync();
    }

    public async Task CreateRefundInvoice(Ulid id, RefundRequestData data)
    {
        var invoice = await GetInvoice(id);
        var management = await _model.SingleAsync(
            _context.MatchingInvoiceManagements
                .IgnoreQueryFilters()
                .Where(a => a.Invoices.Any(i => i.Id == id))
                .Include(a => a.Invoices)
        );

        var reject = new IntroductionInvoice
        {
            InvoiceType = InvoiceTypes.Refund,
            InvoiceMonth = new YearAndMonth(
                DateUtils.UtcToJst(DateTime.UtcNow)
            ),
            SequenceNumber = await _seq.GetValue(SequenceNumberId.Invoice),
            InvoiceAmount = invoice.InvoiceAmount * -1,
            RefundInvoiceId = data.RefundInvoiceId,
            RefundReason = data.RefundReason,
            ModifiedTime = DateTime.UtcNow,
        };
        management.Invoices.Add(_model.Create(reject));

        await _context.SaveChangesAsync();
    }

    #endregion

    #region 請求変更処理 ========================================================

    public Task<IntroductionInvoice> GetInvoice(Ulid id)
    {
        return _model.SingleAsync(
            _context.IntroductionInvoices.Where(a => a.Id == id)
        );
    }

    public async Task UpdateInvoice(
        Ulid id,
        IObjectUpdater<IntroductionInvoice> data
    )
    {
        var invoice = await GetInvoice(id);

        data.To(invoice);
        invoice.ModifiedTime = DateTime.UtcNow;
        await _context.SaveChangesAsync();
    }

    #endregion

    #region 請求管理更新処理 ====================================================

    public async Task<MatchingInvoiceManagement> GetManagement(Ulid id)
    {
        return await _model.SingleAsync(
            _context.MatchingInvoiceManagements
                .IgnoreQueryFilters()
                .Where(a => a.Id == id)
        );
    }

    public async Task<MatchingInvoiceManagement> UpdateManagement(
        Ulid id,
        IObjectUpdater<MatchingInvoiceManagement> data
    )
    {
        var management = await GetManagement(id);
        var tmpInterviewDate = management.InterviewDate;
        var initialProgressStatus = management.ProgressStatus;
        data.To(management);

        bool sendRequestedInvestorRemindMail =
            (
                initialProgressStatus != ProgressStatuses.NoContact
                && management.ProgressStatus == ProgressStatuses.NoContact
            )
            || (
                initialProgressStatus != ProgressStatuses.OnlyOnceContact
                && management.ProgressStatus == ProgressStatuses.OnlyOnceContact
            );

        management.ModifiedTime = DateTime.UtcNow;

        // 面談日時が設定されたら面談請求を作成
        if (tmpInterviewDate == null && management.InterviewDate != null)
            await UpdateInterviewInvoice(id, true);

        await _context.SaveChangesAsync();

        if (sendRequestedInvestorRemindMail)
        {
            var invoiceQuery = GetQuery().Where(x => x.Id == id);
            var investor = GetSendMailInvestor(invoiceQuery);

            if (investor != null)
            {
                var mailTemplate = new RequestedInvestorRemindMailTemplate(
                    investor
                );
                var mailFrom = new MailboxAddress(
                    _settings.Investors.ConsultationRequestMail.FromName,
                    _settings.Investors.ConsultationRequestMail.FromAddress
                );
                await _emailService.TrySendMail(
                    mailTemplate.GetInvestorRemindMailSubject(),
                    mailFrom,
                    investor.ToMail,
                    mailTemplate.GetInvestorRemindMailContents(),
                    ""
                );
            }
        }

        return management;
    }

    #endregion

    #region アクセスチェック ====================================================

    public async Task CheckInvoiceAccess(Ulid invoiceId, Adviser adviser)
    {
        var exists = await _context.MatchingInvoiceManagements
            .IgnoreQueryFilters()
            .Where(m => m.Invoices.Any(i => i.Id == invoiceId))
            .Where(m => m.AdviserId == adviser.Id)
            .AnyAsync();

        if (!exists)
            throw new ForbiddenEntityAccessError(
                typeof(IntroductionInvoice),
                invoiceId.ToString()
            );
    }

    public async Task CheckInvoiceAccess(Ulid invoiceId, IfaCompany ifaCompany)
    {
        var exists = await _context.MatchingInvoiceManagements
            .IgnoreQueryFilters()
            .Where(m => m.Invoices.Any(i => i.Id == invoiceId))
            .Where(m => m.IfaCompanyId == ifaCompany.Id)
            .AnyAsync();

        if (!exists)
            throw new ForbiddenEntityAccessError(
                typeof(IntroductionInvoice),
                invoiceId.ToString()
            );
    }

    public async Task CheckManagementAccess(Ulid managementId, Adviser adviser)
    {
        var exists = await _context.MatchingInvoiceManagements
            .IgnoreQueryFilters()
            .Where(m => m.Id == managementId)
            .Where(m => m.AdviserId == adviser.Id)
            .AnyAsync();

        if (!exists)
            throw new ForbiddenEntityAccessError(
                typeof(MatchingInvoiceManagement),
                managementId.ToString()
            );
    }

    public async Task CheckManagementAccess(
        Ulid managementId,
        IfaCompany ifaCompany
    )
    {
        var exists = await _context.MatchingInvoiceManagements
            .IgnoreQueryFilters()
            .Where(m => m.Id == managementId)
            .Where(m => m.IfaCompanyId == ifaCompany.Id)
            .AnyAsync();

        if (!exists)
            throw new ForbiddenEntityAccessError(
                typeof(MatchingInvoiceManagement),
                managementId.ToString()
            );
    }

    #endregion

    public IEnumerable<SendIfaRemindMailUser> GetSendIfaRemindMailUsers(
        IQueryable<MatchingInvoiceManagement> invoiceQuery
    )
    {
        // 請求データからOrganizationIdを取得
        var organizationIds = invoiceQuery
            .Where(i => i.IfaCompany != null)
            .Select(i => i.IfaCompany)
            .Where(ic => ic != null && ic.Organization != null)
            .Select(
                ic =>
                    ic != null && ic.Organization != null
                        ? (Ulid?)ic.Organization.Id
                        : null
            )
            .Distinct()
            .ToList();

        // IfaCompanyとそののメールアドレスを取得
        // IfaのOrganizationからIfaManagementスコープを持つMembershipを取得
        var sendIfaRemindMailUsers = _context.OrganizationMemberships
            .Where(o => organizationIds.Contains(o.OrganizationId))
            .Where(
                y =>
                    y.Scopes.Any(
                        s =>
                            s.Id
                            == OrganizationMembershipScopes.IfaCompanyManagement
                    )
            )
            .Select(
                y =>
                    new SendIfaRemindMailUser
                    {
                        ToName = y.Organization.Name,
                        ToMail = y.User.MainEMail,
                        Invoices =
                            MatchingInvoiceManagementSummaryData.FromQuery(
                                _context,
                                (IQueryable<MatchingInvoiceManagement>)
                                    invoiceQuery.Where(
                                        i =>
                                            i.IfaCompany != null
                                            && i.IfaCompany.Organization != null
                                            && i.IfaCompany.Organization.Id
                                                == y.OrganizationId
                                    ),
                                null,
                                false,
                                false
                            )
                    }
            )
            .ToList();

        return sendIfaRemindMailUsers;
    }

    private SendIfaRemindMailInvestor? GetSendMailInvestor(
        IQueryable<MatchingInvoiceManagement> invoiceQuery
    ) =>
        invoiceQuery
            .Where(
                i =>
                    i.BatchIntroduction != null
                    && !string.IsNullOrEmpty(i.BatchIntroduction.EMail)
            )
            .Select(
                i =>
                    new SendIfaRemindMailInvestor
                    {
                        ToName = i.BatchIntroduction.Name,
                        ToMail = i.BatchIntroduction.EMail,
                    }
            )
            .SingleOrDefault();

    public IEnumerable<SendIfaRemindMailUser> GetSendMailAdvisers(
        IQueryable<MatchingInvoiceManagement> invoiceQuery
    )
    {
        // 請求データからAdviserIdを取得
        var adviserIds = invoiceQuery
            .Where(i => i.Adviser != null)
            .Select(i => i.Adviser)
            .Distinct()
            .Select(i => i.Id)
            .ToList();

        // Adviserごとに名前、メールアドレス、請求データを取得
        var sendMailAdvisers = _context.Advisers
            .IgnoreQueryFilters()
            .Where(a => adviserIds.Contains(a.Id))
            .ToList()
            .Select(
                a =>
                    new SendIfaRemindMailUser
                    {
                        ToName = a.FullName,
                        ToMail = a.User != null ? a.User.MainEMail : null,
                        Invoices =
                            MatchingInvoiceManagementSummaryData.FromQuery(
                                _context,
                                invoiceQuery.Where(
                                    q =>
                                        q.Adviser != null
                                        && q.Adviser.Id == a.Id
                                ),
                                null,
                                false,
                                false
                            )
                    }
            );

        return sendMailAdvisers;
    }

    // リマインドメール送るユーザを取得する
    public IEnumerable<SendIfaRemindMailUser> GetUsersWithNoProgressSet()
    {
        // 請求データの取得のうち、進行状況が未着手のものを取得
        var invoiceQuery = GetQuery()
            .Where(
                x =>
                    x.ProgressStatus == null
                    || x.ProgressStatus == ProgressStatuses.Hold
            );

        IEnumerable<SendIfaRemindMailUser> sendIfaRemindMailUsers =
            GetSendIfaRemindMailUsers(invoiceQuery);

        IEnumerable<SendIfaRemindMailUser> sendMailAdvisers =
            GetSendMailAdvisers(invoiceQuery);

        IEnumerable<SendIfaRemindMailUser> sendAllUsers =
            sendIfaRemindMailUsers.Concat(sendMailAdvisers);

        return sendAllUsers;
    }

    public List<Ulid> GetPhotoCollection(Ulid id)
    {
        var photoCollection = _context.MatchingInvoicePhotoCollections
            .Where(x => x.MatchingInvoiceId == id)
            .Select(x => x.Image)
            .ToList();
        return photoCollection;
    }

    public async Task UpdatePhotoCollection(Ulid id, Ulid photoFileKey)
    {
        var photoCollection = new MatchingInvoicePhotoCollection
        {
            Id = Ulid.NewUlid(),
            MatchingInvoiceId = id,
            Image = photoFileKey
        };
        _context.MatchingInvoicePhotoCollections.Add(photoCollection);

        await _context.SaveChangesAsync();
    }

    public async Task DeletePhotoCollection(Ulid image)
    {
        var photoCollection = _context.MatchingInvoicePhotoCollections
            .Where(x => x.Image == image)
            .FirstOrDefault();
        if (photoCollection != null)
        {
            _context.MatchingInvoicePhotoCollections.Remove(photoCollection);
            await _context.SaveChangesAsync();
        }
    }

    public IEnumerable<AdviserIntroductionInvoiceResponse> GetInvoiceList(
        Ulid adviserId,
        YearAndMonth? invoiceMonth,
        bool? allPeriod,
        bool? hold,
        bool? approved,
        bool? matching,
        bool? interviewed,
        string? investorName
    )
    {
        var invoices = _context.IntroductionInvoices.Where(
            i => i.MatchingInvoiceManagement.AdviserId == adviserId
        );

        if (allPeriod == false && invoiceMonth != null)
            invoices = invoices.Where(i => i.InvoiceMonth == invoiceMonth);

        if (hold == true && approved == false)
            invoices = invoices.Where(
                i => i.InvoiceStatus == InvoiceStatuses.Hold
            );

        if (hold == false && approved == true)
            invoices = invoices.Where(
                i => i.InvoiceStatus == InvoiceStatuses.Approved
            );

        if (hold == true && approved == true)
            invoices = invoices.Where(
                i =>
                    i.InvoiceStatus == InvoiceStatuses.Hold
                    || i.InvoiceStatus == InvoiceStatuses.Approved
            );

        if (matching == true && interviewed == false)
            invoices = invoices.Where(
                i => i.InvoiceType == InvoiceTypes.Matching
            );

        if (matching == false && interviewed == true)
            invoices = invoices.Where(
                i => i.InvoiceType == InvoiceTypes.Interview
            );

        if (matching == true && interviewed == true)
            invoices = invoices.Where(
                i =>
                    i.InvoiceType == InvoiceTypes.Matching
                    || i.InvoiceType == InvoiceTypes.Interview
            );

        if (!string.IsNullOrEmpty(investorName))
            invoices = invoices.Where(
                i =>
                    i.MatchingInvoiceManagement.BatchIntroduction.Name.Contains(
                        investorName
                    )
            );

        return invoices
            .Include(i => i.MatchingInvoiceManagement)
            .ThenInclude(m => m.BatchIntroduction)
            .Select(i => new AdviserIntroductionInvoiceResponse(i));
    }
}
