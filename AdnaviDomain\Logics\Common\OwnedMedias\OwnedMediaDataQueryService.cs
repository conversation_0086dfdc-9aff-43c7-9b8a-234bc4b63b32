using Adnavi.Domain.Models;
using Amazon.Athena;
using Amazon.Athena.Model;
using Adnavi.Utils.Exceptions;
using Microsoft.Extensions.Options;
using Adnavi.Utils;
using Adnavi.Domain.Models.Commons.OwnedMedias;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using AdnaviDomain.Logics.Common.AwsClients;
using Microsoft.Extensions.DependencyInjection;

namespace Adnavi.Domain.Logics.Common.OwnedMedias;

public class OwnedMediaDataQueryService : IHostedService, IDisposable
{
    private readonly ILogger<OwnedMediaDataQueryService> _logger;
    private readonly AdnaviS3Settings _settings;
    private Timer? _timer = null;
    private readonly IServiceProvider _serviceProvider;
    private readonly AmazonAthenaClient _athenaClient;

    public OwnedMediaDataQueryService(
        ILogger<OwnedMediaDataQueryService> logger,
        IServiceProvider serviceProvider
    )
    {
        using var scope = serviceProvider.CreateScope();
        _settings = scope.ServiceProvider
            .GetRequiredService<IOptions<AdnaviS3Settings>>()
            .Value;
        _logger = logger;
        _serviceProvider = serviceProvider;
        _athenaClient = AthenaClientFactory.Create(_settings.S3DefaultRegion);
    }

    public Task StartAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation(
            "Initiating StartAsync operation for OwnedMediaDataQueryService."
        );
        try
        {
            _logger.LogInformation("OwnedMediaDataQuery Service running.");
            var now = DateTime.UtcNow;
            var nextMidnight = new DateTime(
                now.Year,
                now.Month,
                now.Day,
                0,
                0,
                0,
                DateTimeKind.Utc
            ).AddDays(1);
            _logger.LogInformation("Calculating delay for the next execution.");
            var delay =
                nextMidnight - now > TimeSpan.FromHours(6)
                    ? nextMidnight - now - TimeSpan.FromHours(6)
                    : nextMidnight - now + TimeSpan.FromHours(18);
            _logger.LogInformation("Setting up timer for DoWork execution.");
            _timer = new Timer(
                async _ => await DoWork(null),
                null,
                delay,
                TimeSpan.FromHours(24)
            );

            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error occurred StartAsync of OwnedMediaDataQueryService."
            );
            throw;
        }
    }

    public Task StopAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation(
            "Initiating StopAsync operation for OwnedMediaDataQueryService."
        );
        try
        {
            _logger.LogInformation("OwnedMediaDataQuery Service is stopping.");
            _timer?.Change(Timeout.Infinite, 0);
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error occurred StopAsync of OwnedMediaDataQueryService."
            );
            throw;
        }
    }

    public void Dispose()
    {
        _logger.LogInformation(
            "Initiating Dispose operation for OwnedMediaDataQueryService."
        );
        try
        {
            _logger.LogInformation("OwnedMediaDataQuery Service is disposing.");
            _timer?.Dispose();
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error occurred Dispose of OwnedMediaDataQueryService."
            );
            throw;
        }
    }

    private async Task DoWork(object? state)
    {
        try
        {
            _logger.LogInformation("Reminder Service is started");
            var start = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(-1));
            var end = start;
            await StoreData(start, end);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error occurred DoWork of OwnedMediaDataQueryService."
            );
        }
    }

    private async Task StoreData(DateOnly start, DateOnly end)
    {
        _logger.LogInformation(
            "Initiating StoreData operation for OwnedMediaDataQueryService."
        );
        try
        {
            var startDate = start.ToString("yyyy-MM-dd");
            var endDate = end.ToString("yyyy-MM-dd");
            var queryExecutionId = await StartQueryExecution(
                startDate,
                endDate
            );
            await WaitQueryExecutionResponse(queryExecutionId);
            var result = await GetResultAsList(queryExecutionId);

            //headerを除外(result.Skip(1))
            using var scope = _serviceProvider.CreateScope();
            var context =
                scope.ServiceProvider.GetRequiredService<AdnaviDomainContext>();
            var modelUtils =
                scope.ServiceProvider.GetRequiredService<ModelUtils>();
            var setting = scope.ServiceProvider
                .GetRequiredService<IOptions<AdnaviDomainSettings>>()
                .Value;

            foreach (var row in result.Skip(1))
            {
                ProcessRow(row, context, modelUtils);
            }
            await modelUtils.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error occurred StoreData of OwnedMediaDataQueryService."
            );
        }
    }

    private void ProcessRow(
        Dictionary<string, string> row,
        AdnaviDomainContext context,
        ModelUtils modelUtils
    )
    {
        try
        {
            if (
                row["date"] != null
                && row["referrer"] != null
                && row["query_string"] != null
                && int.TryParse(row["click_count"], out var click_count)
            )
            {
                var column =
                    context.OwnedColumns.SingleOrDefault(
                        a => a.Uri == row["referrer"]
                    ) ?? modelUtils.Create(new OwnedColumn());

                var transitionStatistic =
                    column.TransitionStatistics.SingleOrDefault(
                        a =>
                            a.TargetUri == row["query_string"]
                            && a.Date.ToString("yyyy-mm-dd") == row["date"]
                    );
                if (transitionStatistic == null)
                {
                    column.TransitionStatistics =
                        column.TransitionStatistics.Append(
                            new TransitionStatistic
                            {
                                Date = DateOnly.Parse(row["date"]),
                                TargetUri = row["query_string"],
                                Count = click_count
                            }
                        );
                }
                else
                    transitionStatistic.Count = click_count;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error occurred StoreData of OwnedMediaDataQueryService.{row}",
                row
            );
        }
    }

    private async Task<string> StartQueryExecution(string start, string end)
    {
        try
        {
            var queryString =
                $@"
        select date, referrer, query_string, count(*) click_count from from ""{_settings.AdviserNaviAthenaDatabaseName}"".""{_settings.AdviserNaviAthenaTableName}""
        where ""date"" BETWEEN DATE '{start}' AND DATE '{end}'
        and sc_content_type like '%text/html%'
        and uri = '/lib/redirect/'
        and request_ip <> '*************'
        and request_ip <> '*************'
        and length(referrer) < 50
        and length(referrer) > 10
        group by date, referrer, query_string
        order by count(*) desc;
         ";

            var startQueryExecutionResponse =
                await _athenaClient.StartQueryExecutionAsync(
                    new StartQueryExecutionRequest
                    {
                        QueryString = queryString,
                        WorkGroup = "primary",
                        ResultConfiguration = new ResultConfiguration()
                        {
                            OutputLocation = _settings.AthenaOutputLocation
                        },
                        QueryExecutionContext = new QueryExecutionContext
                        {
                            Database = _settings.AdviserNaviAthenaDatabaseName
                        }
                    }
                );
            return startQueryExecutionResponse.QueryExecutionId;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error occurred StartQueryExecution of OwnedMediaDataQueryService."
            );
            throw;
        }
    }

    private async Task WaitQueryExecutionResponse(string queryExecutionId)
    {
        _logger.LogInformation(
            "Waiting for query execution response in OwnedMediaDataQueryService."
        );
        try
        {
            int retryCount = 0;
            int maxRetryCount = 10;
            while (retryCount++ < maxRetryCount)
            {
                var queryExecutionResponse =
                    await _athenaClient.GetQueryExecutionAsync(
                        new GetQueryExecutionRequest
                        {
                            QueryExecutionId = queryExecutionId
                        }
                    );

                var queryExecutionState = queryExecutionResponse
                    .QueryExecution
                    .Status
                    .State;
                if (
                    queryExecutionState == QueryExecutionState.RUNNING
                    || queryExecutionState == QueryExecutionState.QUEUED
                )
                {
                    // Introduce a 5-second delay before the next API call
                    await Task.Delay(5000);
                    continue;
                }

                if (queryExecutionState == QueryExecutionState.FAILED)
                    throw new InternalException("Athena query failed.");
                break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error occurred WaitQueryExecutionResponse of OwnedMediaDataQueryService."
            );
            throw;
        }
    }

    private async Task<List<Dictionary<string, string>>> GetResultAsList(
        string queryExecutionId
    )
    {
        _logger.LogInformation(
            "Initiating GetResultAsList operation for OwnedMediaDataQueryService."
        );
        try
        {
            var result = new List<Dictionary<string, string>>();

            var response = await _athenaClient.GetQueryResultsAsync(
                new GetQueryResultsRequest
                {
                    QueryExecutionId = queryExecutionId
                }
            );

            foreach (var row in response.ResultSet.Rows)
            {
                var rowDict = new Dictionary<string, string>();
                for (int i = 0; i < row.Data.Count; i++)
                {
                    rowDict[
                        response.ResultSet.ResultSetMetadata.ColumnInfo[i].Name
                    ] = row.Data[i].VarCharValue;
                }
                result.Add(rowDict);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error occurred GetResultAsList of OwnedMediaDataQueryService."
            );
            throw;
        }
    }
}
