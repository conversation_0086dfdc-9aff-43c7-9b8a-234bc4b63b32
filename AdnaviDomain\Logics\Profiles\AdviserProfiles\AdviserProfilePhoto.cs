using Adnavi.Utils;
using Adnavi.Utils.Images;
using NUlid;
using Serilog;

namespace Adnavi.Domain.Logics.Profiles.AdviserProfiles;

public class AdviserProfilePhoto
{
    private readonly S3Image _s3Image;
    private readonly Ulid _adviserId;
    private readonly string _fileKey;

    public static readonly List<ImageFileSpec> _fileSpecs =
        new()
        {
            new ImageFileSpec(ImageFileTypes.Jpeg, 304, 304, true),
            new ImageFileSpec(ImageFileTypes.Jpeg, 152, 152, false),
            new ImageFileSpec(ImageFileTypes.Webp, 304, 304, false),
            new ImageFileSpec(ImageFileTypes.Webp, 152, 152, false),
        };

    public AdviserProfilePhoto(S3Utils s3, Ulid adviserId, string fileKey)
    {
        _s3Image = new S3Image(s3);
        _adviserId = adviserId;
        _fileKey = fileKey;
    }

    internal IEnumerable<ImageSpecAndUrl> GetUrls(string baseUrl) =>
        _fileSpecs
            .Where(s => s.MaxWidth != null)
            .Select(
                s =>
                    new ImageSpecAndUrl
                    {
                        MaxWidth = s.MaxWidth,
                        MaxHeight = s.MaxHeight,
                        MediaType = s.MediaType,
                        Url = $"{baseUrl}/{PhotoS3Key}-{s.FileName}",
                        Fallback = s.Fallback,
                    }
            );

    public async Task Upload(Stream input)
    {
        Log.Information("Upload image {key}", PhotoS3Key);

        await _s3Image.Upload(
            await ToBinaryArray(input),
            PhotoS3Key,
            _fileSpecs
        );
    }

    public async Task Delete()
    {
        try
        {
            Log.Information("Delete image {key}", PhotoS3Key);
            await _s3Image.Delete(PhotoS3Key, _fileSpecs);
        }
        catch (Exception ex)
        {
            Log.Warning(
                ex,
                "The profile photo file cannot deleted. {adviserId}, {fileKey}",
                _adviserId,
                _fileKey
            );
        }
    }

    private string PhotoS3Key =>
        $"advisers/{_adviserId}/profile/photo/{_fileKey}";

    private static async Task<byte[]> ToBinaryArray(Stream input)
    {
        var stream = new MemoryStream();
        await input.CopyToAsync(stream);
        return stream.ToArray();
    }
}
