using Adnavi.Domain.Common;
using MimeKit;

namespace Adnavi.Domain.Logics.Consultations.ConsultationRequests;

public class CsEvaluationRequestMailTemplate
{
    protected readonly MailNotification _notifications;

    public CsEvaluationRequestMailTemplate(InvestorsSettings settings)
    {
        _notifications = settings.ConsultationRequestMail2;
    }

    public string GetSubject() => "アンケートのお願い「資産運用アドバイザーマッチングサービスカスタマーサポート」";

    public MailboxAddress GetFromMailAddress() =>
        new(_notifications.FromName, _notifications.FromAddress);

    public virtual string GetContents()
    {
        var contents =
            $@"
お世話なっております。
アドバイザーナビのカスタマーサポートでございます。

ご面談ありがとうございました。

下記アンケートURLになりますのでお答えいただけますと幸いです。

https://questant.jp/q/QWP3AHCM

ご確認の程、よろしくお願いいたします。
";
        return contents;
    }
}
