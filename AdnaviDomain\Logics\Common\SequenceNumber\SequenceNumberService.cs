using Adnavi.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Adnavi.Domain.Models.Commons.SequenceNumber;
using System.Data;

namespace Adnavi.Domain.Logics.Common.SequenceNumber;

public class SequenceNumberService
{
    private readonly AdnaviDomainContext _context;
    private readonly ModelUtils _model;

    public SequenceNumberService(AdnaviDomainContext context, ModelUtils model)
    {
        _context = context;
        _model = model;
    }

    public async Task<int> GetValue(SequenceNumberId id)
    {
        using var transaction = _context.Database.BeginTransaction(
            IsolationLevel.Serializable
        );

        var sequenceNumber = await _model.FindAsync(
            _context.SequenceNumbers,
            id
        );
        sequenceNumber.UpdateValue();
        transaction.Commit();

        return sequenceNumber.Value;
    }

    public async Task SetValue(SequenceNumberId id, int newValue)
    {
        using var transaction = _context.Database.BeginTransaction(
            IsolationLevel.Serializable
        );

        var sequenceNumber = await _model.FindAsync(
            _context.SequenceNumbers,
            id
        );

        sequenceNumber.Value = newValue;
        transaction.Commit();
    }
}
