using System.Text;
using Adnavi.Domain.Logics.Common.EMails;
using Adnavi.Domain.Models.Consultations.BatchIntroductions;
using Adnavi.Utils;
using Microsoft.Extensions.Options;
using MimeKit;

namespace Adnavi.Domain.Logics.Consultations.BatchIntroductions;

public class ScheduleAdjustmentReminderMailLogic
{
    private readonly AdnaviDomainContext _context;
    private readonly EMailService _emailService;
    private readonly InvestorsSettings _settings;

    public ScheduleAdjustmentReminderMailLogic(
        IOptions<AdnaviDomainSettings> settings,
        AdnaviDomainContext context,
        EMailService emailService
    )
    {
        _settings = settings.Value.Investors;
        _context = context;
        _emailService = emailService;
    }

    //TODO:DateTimeOffsetを用いているため、2度のSQLクエリを叩いている。
    // 1度のSQLクエリで取得できるように改善したい。
    public List<BatchIntroduction> Get24HoursReminderIntroductions()
    {
        var now = DateTimeOffset.UtcNow;
        var twentyFourHoursLater = now.AddHours(24);
        var twentyThreeHoursLater = now.AddHours(23);

        return _context.BatchIntroductions
            .Where(
                b =>
                    b.ConsultationStartTime.HasValue
                    && b.Status == BatchIntroductionStatus.Request
                    && b.SendScheduleAdjustmentRemind24HoursAgo != true
            )
            .OrderByDescending(b => b.CreatedTime)
            .AsEnumerable()
            .Where(
                b =>
                    b.ConsultationStartTime.HasValue
                    && b.ConsultationStartTime.Value >= twentyThreeHoursLater
                    && b.ConsultationStartTime.Value <= twentyFourHoursLater
            )
            .GroupBy(b => b.EMail)
            .Select(g => g.First())
            .ToList();
    }

    //TODO:DateTimeOffsetを用いているため、2度のSQLクエリを叩いている。
    // 1度のSQLクエリで取得できるように改善したい。
    public List<BatchIntroduction> Get1HourReminderIntroductions()
    {
        var now = DateTimeOffset.UtcNow;
        var oneHourLater = now.AddHours(1);
        var fiftyMinutesLater = now.AddMinutes(50);

        return _context.BatchIntroductions
            .Where(
                b =>
                    b.ConsultationStartTime.HasValue
                    && b.Status == BatchIntroductionStatus.Request
                    && b.SendScheduleAdjustmentRemind1HourAgo != true
            )
            .OrderByDescending(b => b.CreatedTime)
            .AsEnumerable()
            .Where(
                b =>
                    b.ConsultationStartTime.HasValue
                    && b.ConsultationStartTime.Value >= fiftyMinutesLater
                    && b.ConsultationStartTime.Value <= oneHourLater
            )
            .GroupBy(b => b.EMail)
            .Select(g => g.First())
            .ToList();
    }

    public async Task DoSendAttachmentMail(
        BatchIntroduction data,
        ScheduleAdjustmentReminderMailTemplate template,
        string origin,
        string textType = "plain"
    )
    {
        List<MimePart>? attachments = null;

        var mailFrom = new MailboxAddress(
            _settings.ConsultationRequestMail.FromName,
            _settings.ConsultationRequestMail.FromAddress
        );
        string content = template.GetReminderContent(data);

        if (
            // NOTE: 現状は面談予約のメールにのみICSを添付するようにしている。
            data.ConsultationStartTime.HasValue
            && data.ConsultationEndTime.HasValue
        )
        {
            var icsContent = IcsCalendarGenerator.GenerateIcs(
                summary: "面談予約リマインダー",
                description: "アドバイザーとの面談予約のリマインダーです。",
                startTime: data.ConsultationStartTime.Value,
                endTime: data.ConsultationEndTime.Value,
                location: "オンライン",
                organizerEmail: _settings.ConsultationRequestMail.FromAddress,
                organizerName: _settings.ConsultationRequestMail.FromName
            );

            var stream = new MemoryStream(Encoding.UTF8.GetBytes(icsContent));
            var icsAttachment = new MimePart("application", "ics")
            {
                Content = new MimeContent(stream),
                ContentDisposition = new ContentDisposition(
                    ContentDisposition.Attachment
                ),
                ContentTransferEncoding = ContentEncoding.Base64,
                FileName = "reminder.ics"
            };

            attachments = new List<MimePart> { icsAttachment };
        }

        var success = await _emailService.TrySendMail(
            template.GetReminderSubject(),
            mailFrom,
            data.EMail,
            content,
            origin,
            textType,
            attachments
        );

        var staffNotificationMails = _settings
            .ConsultationRequestMail
            .NotificationMailAddresses;

        // Send mail to the adviser-navi staff.
        foreach (var mail in staffNotificationMails)
        {
            await _emailService.TrySendMail(
                template.GetNotificationSubject(success),
                mailFrom,
                mail,
                content
                    + $@"<pre>
━━━━━━━━━━━ 管理用情報 ━━━━━━━━━━━
投資家名:{data.Name}
投資家メールアドレス:{data.EMail}
検索日:{DateUtils.UtcToJst(data.CreatedTime)}
検索ID：{data.Id}
お問い合わせ元のページ:{data.OriginUrl}
</pre>
",
                origin,
                textType,
                attachments
            );
        }
    }
}
