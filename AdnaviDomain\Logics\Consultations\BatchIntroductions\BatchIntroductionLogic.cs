﻿/* AIによる要約(2025-06-17)

### この関数の目的

この`BatchIntroductionLogic`クラスは、投資家（相談者）とファイナンシャルアドバイザーを一括でマッチング（紹介）するための中心的なビジネスロジックを担っています。
投資家が入力した希望条件に合致するアドバイザーを複数人探し出し、リストとして提示するまでの一連のプロセスを管理します。

### 主な機能

このクラスは、大きく分けて以下の機能を提供します。

1.  **紹介リクエストの受付と処理 (`Request`, `StaffRequest`)**
    *   投資家からの相談申し込み（紹介リクエスト）を受け付けます。
    *   スタッフが手動で紹介リクエストを作成・実行することも可能です。
    *   リクエストが完了すると、関係者（投資家本人、アドバイザー、所属IFA法人、運営スタッフ）にメールで通知します。
    *   短期間での重複リクエストを検知し、自動で拒否する機能も備わっています。

2.  **アドバイザーの検索と絞り込み (`GetAdviserIds`)**
    *   投資家の希望条件（居住地、相談したい内容、資産額、性別、連絡方法など）に合致するアドバイザーをデータベースから検索します。
    *   アドバイザー側が設定している受付条件（例：特定の資産額以上の投資家のみ受け付けるなど）も考慮して、候補者を絞り込みます。

3.  **アドバイザーの並び替え (`SortAdvisersBy...`系のメソッド群)**
    *   見つかったアドバイザー候補を、投資家にとって最適な順番で表示するために並び替えます。
    *   並び替えの基準は複数用意されています。
        *   **マッチング率順:** 相談内容の合致度やアドバイザーの得意分野などを基に算出したスコア順。
        *   **AI予測順:** AIが予測した投資家とアドバイザーの相性（満足度スコア）順。
        *   **人気順:** プロフィール閲覧数や申し込み数が多い順。

4.  **紹介リストの生成と保存 (`GenerateBatchIntroduction...`系のメソッド群)**
    *   上記の検索・並び替え処理を経て、最終的に投資家へ提示するアドバイザーのリスト（`BatchIntroduction`エンティティ）を生成・保存します。
    *   「同じ会社のアドバイザーは1名まで」といったルールを適用する機能もあります。

5.  **設定管理 (`UpdateSetting`, `UpdateIfaCompanySetting`)**
    *   アドバイザーや所属IFA法人が、紹介の受付可否や、受け入れたい投資家の条件などを個別に設定するための機能を提供します。

6.  **管理・運用機能**
    *   過去の紹介履歴を検索したり、CSV形式でダウンロードしたりする管理画面向けの機能。
    *   紹介後の投資家アンケート（満足度調査）のリクエスト作成や回答処理。

### 要約

一言でいうと、このクラスは**「投資家とアドバイザーの高度なマッチングシステム」**です。単純な条件検索だけでなく、
**AIによる相性予測**や**複数の基準に基づいたスコアリング**を行い、投資家一人ひとりにとって最適なアドバイザーを推薦する複雑なロジックを実装しています。
また、申し込みから通知、アフターフォロー（アンケート）までの一連のワークフローを自動化する役割も担っています。
*/

using System.Web;
using Adnavi.Domain.Models;
using Adnavi.Domain.Models.Consultations.BatchIntroductions;
using Adnavi.Domain.Models.Profiles;
using Adnavi.Domain.Models.Profiles.AdviserProfiles;
using Adnavi.Domain.Models.Profiles.IfaCompanyProfiles;
using Adnavi.Domain.Models.Profiles.InvestorProfiles;
using Adnavi.Domain.Models.Profiles.PersonalityTraits;
using Adnavi.Domain.Models.Accounts;
using Adnavi.Utils;
using Adnavi.Utils.Exceptions;
using Microsoft.EntityFrameworkCore;
using MimeKit;
using NUlid;
using Serilog;
using Amazon.CloudWatch;
using Amazon.CloudWatch.Model;
using Adnavi.Domain.Models.Commons;
using Adnavi.Domain.Logics.Consultations.MinimumRequirements;
using Adnavi.Domain.Models.Consultations.ConsultationRequests;
using Adnavi.Domain.Models.Profiles.AdviserProfiles.InvestorFilters;
using Adnavi.Domain.Logics.Invoices.MatchingInvoices;
using Adnavi.Domain.Logics.Common.SequenceNumber;
using Adnavi.Domain.Models.Commons.SequenceNumber;
using Adnavi.Domain.Logics.Common;
using Adnavi.Domain.DTOs.Consultations.BatchIntroductions;
using Adnavi.Domain.DTOs.Profiles.CustomerEvaluations;
using Adnavi.Domain.Logics.Common.EMails;
using Microsoft.Extensions.Options;
using Adnavi.Domain.Logics.Accounts;
using System.Text;
using Adnavi.Domain.Logics.Common.PredictSatisfactions;
using Adnavi.Domain.Logics.Profiles.AdviserProfiles;
using Adnavi.Utils.Images;

namespace Adnavi.Domain.Logics.Consultations.BatchIntroductions;

public class BatchIntroductionLogic
{
    private readonly AdnaviDomainContext _context;
    private readonly ChatWorkUtils _chatWork;
    private readonly ModelUtils _model;
    private readonly IAmazonCloudWatch? _amazonCloudWatch;
    private readonly MatchingInvoicesLogic _matchingInvoicesLogic;
    private readonly CommonMatchingLogic _commonMatchingLogic;
    private readonly SequenceNumberService _seq;
    private readonly LogDictionary _logDictionary;
    private readonly AccessUserLogic _accessUser;
    private readonly EMailService _emailService;
    private readonly Dimension _dimension;
    private readonly PredictSatisfactionService _predictSatisfactionService;
    private readonly bool _redirectAdviserTestMail = false;
    private const double _defaultSatisfactionScore = 3.0;
    private readonly AdnaviDomainSettings _settings;
    private readonly AdviserProfileCommonLogic _adviserProfileCommonLogic;
    private readonly AdviserCalendarIntegrationLogic _adviserCalendarIntegrationLogic;

    public BatchIntroductionLogic(
        IOptions<AdnaviDomainSettings> settings,
        AdnaviDomainContext context,
        ChatWorkUtils chatWork,
        IAmazonCloudWatch? amazonCloudWatch,
        SequenceNumberService seq,
        MatchingInvoicesLogic matchingInvoicesLogic,
        CommonMatchingLogic commonMatchingLogic,
        LogDictionary logDictionary,
        ModelUtils model,
        EMailService emailService,
        PredictSatisfactionService predictSatisfactionService,
        AccessUserLogic accessUser,
        AdviserProfileCommonLogic adviserProfileCommonLogic,
        AdviserCalendarIntegrationLogic adviserCalendarIntegrationLogic
    )
    {
        _settings = settings.Value;
        _context = context;
        _chatWork = chatWork;
        _model = model;
        _amazonCloudWatch = amazonCloudWatch;
        _matchingInvoicesLogic = matchingInvoicesLogic;
        _commonMatchingLogic = commonMatchingLogic;
        _logDictionary = logDictionary;
        _seq = seq;
        _accessUser = accessUser;
        _predictSatisfactionService = predictSatisfactionService;

        _dimension = new Dimension
        {
            Name = "BatchIntroduction Metrics",
            Value = "BatchIntroduction request and introduction"
        };
        _emailService = emailService;
        _adviserProfileCommonLogic = adviserProfileCommonLogic;
        _adviserCalendarIntegrationLogic = adviserCalendarIntegrationLogic;
    }

    private string GetSendToEmail(string email) =>
        _redirectAdviserTestMail
            ? _settings.Investors.AdviserTestMailAddress
            : email;

    public async Task Request(
        BatchIntroduction data,
        bool disallowedRequest = false
    )
    {
        if (disallowedRequest == true)
        {
            _logDictionary.DuplicateRequestBySameIp(
                data.RemoteHost,
                data.AdnParentId
            );
            await _chatWork.TrySend(
                _settings.Investors.ChatWorkRoomId,
                "重複リクエストがありました。"
                    + "\n申し込みを拒否しました。"
                    + "\n━━━━━━━━━━━━━━━━━━━━━━━━━━"
                    + "\n問い合わせ情報"
                    + "\n━━━━━━━━━━━━━━━━━━━━━━━━━━"
                    + $"\nIp: {data.RemoteHost}"
                    + $"\nName: {data.Name}"
                    + $"\nEmail: {data.EMail}"
                    + $"\n電話番号: {data.TelephoneNumber}"
                    + $"\nリクエストID: {data.ParentIntroductionId}"
            );
            return;
        }
        var template = new BatchIntroductionMailTemplate(
            data,
            _context,
            _settings
        );

        var mailFrom = new MailboxAddress(
            _settings.Investors.ConsultationRequestMail.FromName,
            _settings.Investors.ConsultationRequestMail.FromAddress
        );

        var staffNotificationMails = _settings
            .Investors
            .ConsultationRequestMail
            .NotificationMailAddresses;

        var error = false;
        var errorAdvisers = new List<Adviser>();

        // Send mail to the advisers, and add a MatchingInvoiceManagement to the DB.

        foreach (var adviser in data.Advisers)
        {
            await _matchingInvoicesLogic.CreateMatchingInvoice(data, adviser);

            if (adviser.User?.MainEMail == null)
            {
                errorAdvisers.Add(adviser);
                continue;
            }

            var e = !await _emailService.TrySendMail(
                template.GetAdviserSubject(),
                mailFrom,
                GetSendToEmail(adviser.User.MainEMail),
                template.GetContentsForAdviser(adviser),
                data.OriginUrl
            );

            if (e)
                errorAdvisers.Add(adviser);

            var companyEmails = StringUtils.SplitCsvColum(
                adviser.IfaCompany?.NotificationEmail ?? ""
            );

            foreach (var companyEmail in companyEmails)
            {
                await _emailService.TrySendMail(
                    template.GetIfaCompanySubject(adviser),
                    mailFrom,
                    companyEmail,
                    template.GetContentsForIfaCompany(adviser),
                    data.OriginUrl
                );
            }
        }

        // Send mail to the adviser-navi staff.

        var notificationContents = template.GetContentsForNotification(
            errorAdvisers,
            _settings.WatashiIfaUrl
        );

        foreach (var mail in staffNotificationMails)
        {
            error =
                !await _emailService.TrySendMail(
                    template.GetNotificationSubject(data.OriginName),
                    mailFrom,
                    mail,
                    notificationContents,
                    data.OriginUrl
                ) || error;
            ;
        }

        // Send mail to the investor. (ignore error)
        await _emailService.TrySendMail(
            template.GetInvestorSubject(),
            mailFrom,
            data.EMail,
            template.GetContentsForInvestor(errorAdvisers),
            data.OriginUrl
        );

        if (error)
            throw new NotificationMailError(data.OriginUrl, data.EMail);

        data.SendRemindMail = true;
        _context.SaveChanges();
    }

    public async Task StaffRequest(
        BatchIntroduction data,
        User? registeredUser = null
    )
    {
        if (registeredUser?.MainEMail != null)
        {
            data.RegisterEmail = registeredUser.MainEMail;
        }

        var template = new StaffBatchIntroductionMailTemplate(
            data,
            _context,
            _settings
        );

        var mailFrom = new MailboxAddress(
            _settings.Investors.ConsultationRequestMail.FromName,
            _settings.Investors.ConsultationRequestMail.FromAddress
        );

        var staffNotificationMails = _settings
            .Investors
            .ConsultationRequestMail
            .NotificationMailAddresses;

        var error = false;
        var errorAdvisers = new List<Adviser>();

        // Send mail to the advisers, and add a MatchingInvoiceManagement to the DB.

        foreach (var adviser in data.Advisers)
        {
            await _matchingInvoicesLogic.CreateMatchingInvoice(data, adviser);

            if (adviser.User?.MainEMail == null)
            {
                errorAdvisers.Add(adviser);
                continue;
            }

            var staffRemarksToAdviser = _context.AdviserIntroductions
                .SingleOrDefault(
                    ai =>
                        ai.Adviser.Id == adviser.Id
                        && ai.BatchIntroduction.Id == data.Id
                )
                ?.StaffRemarksToAdvisers;

            var e = !await _emailService.TrySendMail(
                template.GetAdviserSubject(),
                mailFrom,
                GetSendToEmail(adviser.User.MainEMail),
                template.GetContentsForAdviser(adviser, staffRemarksToAdviser),
                data.OriginUrl
            );

            if (e)
                errorAdvisers.Add(adviser);

            var companyEmails = StringUtils.SplitCsvColum(
                adviser.IfaCompany?.NotificationEmail ?? ""
            );

            foreach (var companyEmail in companyEmails)
            {
                await _emailService.TrySendMail(
                    template.GetIfaCompanySubject(adviser),
                    mailFrom,
                    companyEmail,
                    template.GetContentsForIfaCompany(
                        adviser,
                        staffRemarksToAdviser
                    ),
                    data.OriginUrl
                );
            }
        }

        // Send mail to the adviser-navi staff.

        var notificationContents = template.GetContentsForNotification(
            errorAdvisers
        );

        foreach (var mail in staffNotificationMails)
        {
            error =
                !await _emailService.TrySendMail(
                    template.GetNotificationSubject(data.OriginName),
                    mailFrom,
                    mail,
                    notificationContents,
                    data.OriginUrl
                ) || error;
            ;
        }

        // Send mail to the investor. (ignore error)
        await _emailService.TrySendMail(
            template.GetInvestorSubjectAsDirectRequest(),
            mailFrom,
            data.EMail,
            template.GetContentsForInvestorAsDirectRequest(errorAdvisers),
            data.OriginUrl
        );

        if (error)
            throw new NotificationMailError(data.OriginUrl, data.EMail);

        data.SendRemindMail = true;
        _context.SaveChanges();

        //Send metric to CloudWatch
        if (_amazonCloudWatch != null)
        {
            try
            {
                await _amazonCloudWatch.PutMetricDataAsync(
                    new PutMetricDataRequest
                    {
                        MetricData = new List<MetricDatum>
                        {
                            new MetricDatum
                            {
                                Dimensions = new List<Dimension> { _dimension },
                                MetricName = "BatchIntroduction Requested",
                                Unit = StandardUnit.Count,
                                Value = 1,
                            }
                        },
                        Namespace = _settings
                            .Investors
                            .CloudWatchMetricsNamespace
                    }
                );
            }
            catch (Exception e)
            {
                Log.Error(e, "Failed to send metric to CloudWatch");
            }
        }
    }

    private IQueryable<Adviser> AcceptedAdviserQuery =>
        _context.Advisers.Where(
            a =>
                a.CompanyByCompanyIntroduction != null
                    && a.IfaCompany != null
                    // IFA法人に所属のアドバイザーのみを対象とする
                    && a.IfaCompany.CompanyType == CompanyTypes.IfaCompany
                    && a.IfaCompany.EnableBatchIntroduction == true
                    && a.BatchIntroductionSetting != null
                    && a.BatchIntroductionSetting.Enabled
                || (
                    a.User != null
                    && a.User.MainEMail
                        == _settings.Investors.AdviserTestMailAddress
                )
        );

    private IQueryable<Adviser> AcceptedAdviserQueryForCalendar =>
        _context.Advisers.Where(
            a =>
                a.CompanyByCompanyIntroduction != null
                    && a.IfaCompany != null
                    // IFA法人に所属のアドバイザーのみを対象とする
                    && a.IfaCompany.CompanyType == CompanyTypes.IfaCompany
                    && a.IfaCompany.EnableCalendarIntroduction == true
                    && a.BatchIntroductionSetting != null
                    && a.BatchIntroductionSetting.Enabled
                || (
                    a.User != null
                    && a.User.MainEMail
                        == _settings.Investors.AdviserTestMailAddress
                )
        );

    internal static IQueryable<Adviser> AddWhereAccepted(
        IQueryable<Adviser> query
    )
    {
        return query.Where(
            a =>
                a.CompanyByCompanyIntroduction != null
                && a.IfaCompany != null
                && a.IfaCompany.EnableBatchIntroduction == true
                && a.BatchIntroductionSetting != null
                && a.BatchIntroductionSetting.Enabled
        );
    }

    public async Task<IEnumerable<Ulid>> GetAdviserIds(
        BatchIntroduction entity,
        bool staffIntroduction
    )
    {
        return await GetAdviserIdsInternal(
            AcceptedAdviserQuery,
            entity,
            staffIntroduction
        );
    }

    public async Task<IEnumerable<Ulid>> GetAdviserIdsForCalendar(
        BatchIntroduction entity,
        bool staffIntroduction
    )
    {
        return await GetAdviserIdsInternal(
            AcceptedAdviserQueryForCalendar,
            entity,
            staffIntroduction
        );
    }

    private async Task<IEnumerable<Ulid>> GetAdviserIdsInternal(
        IQueryable<Adviser> baseQuery,
        BatchIntroduction entity,
        bool staffIntroduction
    )
    {
        var query = baseQuery;

        // スタッフからの紹介でない場合は前回の検索結果を引き継ぐ
        if (!staffIntroduction && entity.ParentIntroductionId != null)
        {
            var parentIntroduction = await _model.SingleAsync(
                _context.BatchIntroductions.Where(
                    a => a.Id == entity.ParentIntroductionId
                )
            );
            query = query.Where(a => parentIntroduction.Advisers.Contains(a));
        }

        // スタッフからの紹介でない場合は一度紹介した会社は紹介しない
        if (
            !staffIntroduction
            && await _accessUser.GetFlag(Flags.NoIntroductionAgain)
        )
        {
            // 同一人物からの申し込みがあれば全て取得
            var requestedEntities = _context.BatchIntroductions.Where(
                a =>
                    a.Status == BatchIntroductionStatus.Request
                    && (
                        (
                            entity.AdnParentId != null
                            && a.AdnParentId == entity.AdnParentId
                        )
                        || a.EMail == entity.EMail
                    )
            );

            // すでに申し込んだ企業のアドバイザーを除外する
            query = query.Where(
                a =>
                    !requestedEntities.Any(
                        b =>
                            b.Advisers.Any(
                                ad => ad.IfaCompanyId == a.IfaCompanyId
                            )
                    )
            );
        }

        // WEBミーティングのある場合とない場合での条件分岐
        var contactMethods = entity.ContactMethods.Select(c => c.Id);
        query = query.Where(
            a =>
                (
                    // if web meeting is selected, adviser must have webMeetingPrefecture
                    contactMethods.Contains(ContactMethods.WebMeeting)
                    && a.AdditionalProfileItems != null
                    && a.AdditionalProfileItems.AvailableContactMethods.Any(
                        c => c.Id == ContactMethods.WebMeeting
                    )
                    && a.WebMeetingPrefectures.Any(
                        p => p.Id == entity.Prefecture
                    )
                )
                || (
                    // if office or visit is selected, adviser must have visitPrefecture.
                    (
                        contactMethods.Contains(ContactMethods.Office)
                        || contactMethods.Contains(ContactMethods.Visit)
                    )
                    && a.AdditionalProfileItems != null
                    && a.AdditionalProfileItems.AvailableContactMethods.Any(
                        c =>
                            c.Id == ContactMethods.Office
                            || c.Id == ContactMethods.Visit
                    )
                    && a.VisitPrefectures.Any(p => p.Id == entity.Prefecture)
                )
        );

        if (!staffIntroduction)
        {
            query = query.Where(
                a =>
                    a.BatchIntroductionSetting == null
                    || a.BatchIntroductionSetting.StaffIntroductionOnly == false
            );
        }

        var adviserIds = new List<Ulid>();

        var requirements = GetAllPreferences(
            entity.AssetRange,
            entity.AdviserPreferences.Select(p => p.Id)
        );

        //必須項目確認
        var advisers = AdviserRequirementsCheck.FromQuery(query).ToArray();
        foreach (var adviserData in advisers)
        {
            if (requirements.Any(r => !r.CheckAdviser(adviserData)))
                continue;

            // 投資家の条件を満たすか確認する
            var filters = GetInvestorFilters(
                adviserData.BatchIntroductionSetting,
                adviserData.IfaCompanyId
            );
            if (filters.Any(f => !f.IsMatched(entity)))
                continue;

            adviserIds.Add(adviserData.AdviserId);
        }

        return adviserIds;
    }

    private static List<IRequirement> GetAllPreferences(
        AssetRanges assetRange,
        IEnumerable<AdviserPreferences> adviserPreferences
    )
    {
        var requirements = new List<IRequirement>();
        foreach (var preference in adviserPreferences)
        {
            if (preference == AdviserPreferences.AfterNineteen)
                requirements.Add(new After(19));
            else if (preference == AdviserPreferences.HolidayAvailable)
                requirements.Add(new HolidayAvailable());
            else if (preference == AdviserPreferences.Male)
            {
                // すでに性別の条件が含まれる場合、性別の条件を削除し、性別の条件を追加しない
                if (requirements.Any(item => item is GenderRequirement))
                    requirements.RemoveAll(item => item is GenderRequirement);
                else
                    requirements.Add(new GenderRequirement(GenderTypes.Male));
            }
            else if (preference == AdviserPreferences.Female)
            {
                // すでに性別の条件が含まれる場合、性別の条件を削除し、性別の条件を追加しない
                if (requirements.Any(item => item is GenderRequirement))
                    requirements.RemoveAll(item => item is GenderRequirement);
                else
                    requirements.Add(new GenderRequirement(GenderTypes.Female));
            }
            else if (preference == AdviserPreferences.CustomerAssetRange)
                requirements.Add(new CustomerAssetRange(assetRange));
            else if (preference == AdviserPreferences.SBIAvailable)
                requirements.Add(new AvailableSecurities("株式会社ＳＢＩ証券"));
            else if (preference == AdviserPreferences.RakutenAvailable)
                requirements.Add(new AvailableSecurities("楽天証券株式会社"));
        }

        return requirements;
    }

    private List<IInvestorFilter> GetInvestorFilters(
        BatchIntroductionSetting? setting,
        Ulid? ifaCompanyId
    )
    {
        var filters = InvestorFilterFactory.GetFilters(setting).ToList();

        var ifaCompanySetting =
            _context.BatchIntroductionIfaCompanySetting.SingleOrDefault(
                s => s.IfaCompanyId == ifaCompanyId
            );
        var companyFilters = InvestorFilterFactory.GetFilters(
            ifaCompanySetting
        );

        filters.AddRange(companyFilters);
        return filters;
    }

    private static int GenerateHash(
        int investorAge,
        GenderTypes investorGender,
        Prefectures investorPrefecture,
        int adviserAge,
        string? adviserName
    )
    {
        int prime1 = 31;
        int prime2 = 17;
        int prime3 = 13;
        int prime4 = 7;
        int prime5 = 23;

        int hash =
            (investorAge * prime1)
            + (int)investorGender * prime2
            + (int)investorPrefecture * prime3
            + adviserAge * prime4
            + (adviserName?.GetHashCode() ?? 0) * prime5;

        return Math.Abs(hash % 100);
    }

    public ICollection<Adviser> SortAdvisersByMatchingRate(
        IQueryable<Adviser> query,
        BatchIntroduction entity,
        PersonalitySurvey? survey,
        bool aiLogicUsed = false,
        bool simpleAiLogicUsed = false
    )
    {
        // 日付でランダムシードが変わる様に変更
        var date = DateTime.UtcNow;
        var random = new Random(
            date.Year * 10000 + date.Month * 100 + date.Day
        );
        var randomForAi = new Random(
            date.Year * 10000 + date.Month * 100 + date.Day
        );

        // 優先順位付のロジック
        var results = new List<AdviserAndPoint>();
        query = query
            .Include(a => a.IfaCompany)
            .Include(a => a.PersonalitySurvey);
        foreach (var adviser in query.ToArray())
        {
            var point = 0.0;
            var predictedSatisfactionScore = 0.0;

            // AI logicを使用する設定の場合、予測された満足度(0-5)を加算
            if (aiLogicUsed && !simpleAiLogicUsed)
            {
                predictedSatisfactionScore = _predictSatisfactionService
                    .PredictSatisfactionScore(
                        adviser.Id,
                        entity.Age,
                        entity.Gender,
                        entity.Prefecture
                    )
                    .Result;
                if (predictedSatisfactionScore == 0.0)
                {
                    predictedSatisfactionScore =
                        _defaultSatisfactionScore
                        + randomForAi.NextDouble() * 2.0;
                }
                point += predictedSatisfactionScore;
            }
            else
            {
                // 対応業務のカバー率(cover of work types)
                var investorWorkType = entity.AdviserWorkTypes.Select(
                    w => w.Id
                );
                var matchedWorkType = adviser
                    .GetWorkTypes()
                    .Intersect(investorWorkType)
                    .ToArray();
                point +=
                    matchedWorkType.Count() / (double)investorWorkType.Count();

                // マッチ度(5段階)の2.5/5を対応可能業務でのカバー率とする
                predictedSatisfactionScore += point * 2.5;

                //どんな人に相談したいか入力でのポイント加算
                var adviserExperience = PointFromAdviserExperience(
                    adviser,
                    entity
                );
                point += adviserExperience * 0.1;

                // マッチ度(5段階)の0.5/5を対応可能業務でのカバー率とする
                // adviserExperienceが30を超えている場合、0.5を加算、それ以下は割合に応じて加算
                if (adviserExperience > 30)
                    predictedSatisfactionScore += 0.5;
                else
                {
                    predictedSatisfactionScore +=
                        adviserExperience / 30.0 * 0.5;
                }

                //ハッシュ値を用い、1.5 - 2範囲でランダムにポイントを加算
                predictedSatisfactionScore +=
                    GenerateHash(
                        entity.Age,
                        entity.Gender ?? 0,
                        entity.Prefecture,
                        (int)(adviser.Age ?? 0),
                        adviser.FamilyName
                    ) / 200.0
                    + 1.5;
            }
            var personalityPoint = 0;
            if (
                survey != null
                && adviser.PersonalitySurvey != null
                && adviser.PersonalitySurvey.SubmitTime != null
            )
                personalityPoint = CalculatePersonalityPoint(survey, adviser);

            results.Add(
                new AdviserAndPoint
                {
                    Adviser = adviser,
                    Point = aiLogicUsed
                        ? (int)(predictedSatisfactionScore * 100)
                        : CalculateAdviserPoint(point, adviser),
                    PersonalityPoint = personalityPoint,
                    Random = random.Next(),
                }
            );
            var newAdviserBatchIntroductionPoint = _model.Create(
                new AdviserBatchIntroductionPoint()
                {
                    Adviser = adviser,
                    BatchIntroduction = entity,
                    PredictedSatisfactionScore = predictedSatisfactionScore,
                    WorkTypesCoverScore = point
                }
            );

            adviser.AdviserBatchIntroductionPoints.Add(
                newAdviserBatchIntroductionPoint
            );
        }

        return results
            .OrderByDescending(a => a.Point)
            //.ThenByDescending(a => a.PersonalityPoint)
            .ThenBy(a => a.Random)
            .Select(a => a.Adviser)
            .ToArray();
    }

    public ICollection<Adviser> SortAdvisersByAccessCount(
        IQueryable<Adviser> query
    )
    {
        return query.OrderByDescending(a => a.ProfileAccessCount).ToArray();
    }

    public ICollection<Adviser> SortAdvisersByAIMatchingRate(
        IQueryable<Adviser> query,
        BatchIntroduction entity,
        PersonalitySurvey? survey,
        bool aiLogicUsed = false,
        bool simpleAiLogicUsed = false
    )
    {
        var result = SortAdvisersByMatchingRate(
            query,
            entity,
            survey,
            aiLogicUsed,
            simpleAiLogicUsed
        );

        var sortedByAiMatching = result
            .Select(
                a =>
                    new
                    {
                        Adviser = a,
                        AiMatching = a.AdviserBatchIntroductionPoints
                            .Select(p => p.PredictedSatisfactionScore)
                            .FirstOrDefault()
                    }
            )
            .OrderByDescending(a => a.AiMatching)
            .ToList(); // Convert to list and return

        return sortedByAiMatching.Select(a => a.Adviser).ToArray();
    }

    public ICollection<Adviser> SortAdvisersByRequestedCount(
        IQueryable<Adviser> query,
        int period
    )
    {
        var result = query
            .Select(
                a =>
                    new
                    {
                        Adviser = a,
                        Count = a.BatchIntroductions
                            .Where( // 30日以内のリクエストのみをカウント
                                b =>
                                    b.Status == BatchIntroductionStatus.Request
                                    && b.CreatedTime
                                        > DateTime.UtcNow.AddDays(period * -1)
                            )
                            .Count()
                    }
            )
            .OrderByDescending(a => a.Count);

        return result.Select(a => a.Adviser).ToArray();
    }

    public async Task<BatchIntroduction> GenerateBatchIntroductionForIntroduction(
        BatchIntroductionData data,
        PersonalitySurvey? survey,
        string remoteHost,
        AdviserSortOptionForBatchIntroduction sortOption
    )
    {
        var sequenceNumber = await _seq.GetValue(
            SequenceNumberId.BatchIntroduction
        );
        var entity = _model.Create(
            data.To(
                new BatchIntroduction() { SequenceNumber = sequenceNumber, },
                _context,
                remoteHost,
                BatchIntroductionStatus.Introduction,
                sortOption
            )
        );
        entity.PersonalitySurvey = survey;
        entity.SequenceNumber = await _seq.GetValue(
            SequenceNumberId.BatchIntroduction
        );

        // Save introduced advisers.
        IEnumerable<Ulid> adviserIds;
        if (IsCalendarIntroduction(data))
        {
            adviserIds = await GetAdviserIdsForCalendar(entity, false);
        }
        else
        {
            adviserIds = await GetAdviserIds(entity, false);
        }
        var query = _context.Advisers.Where(a => adviserIds.Contains(a.Id));

        query = query.Include(a => a.IfaCompany);

        ICollection<Adviser> sortedAdvisers = SortAdvisers(
            query,
            sortOption,
            survey,
            entity,
            data.AiLogicUsed ?? false,
            data.SimpleAiLogicUsed ?? false
        );
        entity.Advisers = ExcludeSameCompanyRegularEmployee(
            advisers: sortedAdvisers,
            introduceCompanyMultiple: data.IntroduceCompanyMultiple
        );
        SaveIntroducedAdvisers(sortOption, entity);
        return entity;
    }

    public async Task<BatchIntroduction> GenerateBatchIntroductionForStaff(
        string? name,
        BatchIntroductionData data,
        PersonalitySurvey? survey,
        string remoteHost,
        AdviserSortOptionForBatchIntroduction sortOption
    )
    {
        var sequenceNumber = await _seq.GetValue(
            SequenceNumberId.BatchIntroduction
        );

        var entity = _model.Create(
            data.To(
                new BatchIntroduction() { SequenceNumber = sequenceNumber },
                _context,
                remoteHost,
                BatchIntroductionStatus.Introduction,
                sortOption
            )
        );

        entity.PersonalitySurvey = survey;
        entity.SequenceNumber = await _seq.GetValue(
            SequenceNumberId.BatchIntroduction
        );

        var adviserIds = await GetAdviserIds(entity, true);
        var query = _context.Advisers.Where(a => adviserIds.Contains(a.Id));
        query = query.Include(a => a.IfaCompany);

        if (!string.IsNullOrEmpty(name))
        {
            query = query.Where(
                a => (a.FamilyName + a.FirstName).Contains(name)
            );
        }

        ICollection<Adviser> sortedAdvisers = SortAdvisers(
            query,
            sortOption,
            survey,
            entity,
            data.AiLogicUsed ?? false
        );

        entity.Advisers = sortedAdvisers.ToList();

        SaveIntroducedAdvisers(sortOption, entity);
        return entity;
    }

    private bool IsCalendarIntroduction(BatchIntroductionData data)
    {
        return data.IsCalendarIntegrated == true
            || data.ConsultationStartTime.HasValue
            || data.ConsultationEndTime.HasValue;
    }

    public async Task<List<Ulid>> GenerateAvailableAdvisersForCalendar(
        BatchIntroductionData data,
        PersonalitySurvey? survey,
        string remoteHost
    )
    {
        var sequenceNumber = await _seq.GetValue(
            SequenceNumberId.BatchIntroduction
        );

        var entity = _model.Create(
            data.To(
                new BatchIntroduction() { SequenceNumber = sequenceNumber },
                _context,
                remoteHost,
                BatchIntroductionStatus.GetCalendar,
                AdviserSortOptionForBatchIntroduction.None
            )
        );

        entity.PersonalitySurvey = survey;
        await _model.SaveChangesAsync();
        var adviserIds = await GetAdviserIdsForCalendar(entity, false);
        return adviserIds.ToList();
    }

    public async Task<BatchIntroduction> GenerateBatchIntroductionForSpecificSlot(
        BatchIntroductionData data,
        PersonalitySurvey? survey,
        string remoteHost,
        DateTimeOffset startTime,
        string googleClientId,
        string googleClientSecret,
        string microsoftClientId = "",
        string microsoftClientSecret = ""
    )
    {
        var sequenceNumber = await _seq.GetValue(
            SequenceNumberId.BatchIntroduction
        );
        var entity = _model.Create(
            data.To(
                new BatchIntroduction() { SequenceNumber = sequenceNumber, },
                _context,
                remoteHost,
                BatchIntroductionStatus.Introduction,
                AdviserSortOptionForBatchIntroduction.None
            )
        );
        entity.PersonalitySurvey = survey;
        // Save introduced advisers.
        var adviserIds = await GetAdviserIdsForCalendar(entity, false);

        var advisers = await _adviserCalendarIntegrationLogic.GetAdviserBySlot(
            adviserIds.ToList(),
            startTime,
            googleClientId,
            googleClientSecret,
            microsoftClientId,
            microsoftClientSecret
        );

        entity.Advisers = advisers;
        SaveIntroducedAdvisers(
            AdviserSortOptionForBatchIntroduction.None,
            entity
        );
        return entity;
    }

    private ICollection<Adviser> SortAdvisers(
        IQueryable<Adviser> query,
        AdviserSortOptionForBatchIntroduction sortOption,
        PersonalitySurvey? survey,
        BatchIntroduction entity,
        bool aiLogicUsed = false,
        bool simpleAiLogicUsed = false
    )
    {
        ICollection<Adviser> sortedAdvisers;

        if (sortOption == AdviserSortOptionForBatchIntroduction.AccessCount)
            sortedAdvisers = SortAdvisersByAccessCount(query).ToArray();
        else if (
            sortOption == AdviserSortOptionForBatchIntroduction.RequestedCount
        )
            sortedAdvisers = SortAdvisersByRequestedCount(query, 30).ToArray();
        else if (
            sortOption == AdviserSortOptionForBatchIntroduction.AIMatchingRate
        )
            sortedAdvisers = SortAdvisersByAIMatchingRate(
                    query,
                    entity,
                    survey,
                    aiLogicUsed,
                    simpleAiLogicUsed
                )
                .ToArray();
        else
            sortedAdvisers = SortAdvisersByMatchingRate(
                    query,
                    entity,
                    survey,
                    aiLogicUsed,
                    simpleAiLogicUsed
                )
                .ToArray();

        return sortedAdvisers;
    }

    private void SaveIntroducedAdvisers(
        AdviserSortOptionForBatchIntroduction sortOption,
        BatchIntroduction entity
    )
    {
        for (int i = 0; i < entity.Advisers.Count; i++)
        {
            _model.Create(
                new AdviserIntroduction()
                {
                    Adviser = entity.Advisers.ElementAt(i),
                    IfaCompany = entity.Advisers.ElementAt(i).IfaCompany,
                    BatchIntroduction = entity,
                    SortOption = sortOption,
                    Rank = i + 1
                }
            );
        }
    }

    private static double PointFromAdviserExperience(
        Adviser adviser,
        BatchIntroduction entity
    )
    {
        double point = 0;
        if (adviser.CustomerStatics == null)
            return point;
        foreach (
            var FavorableAdviserExperience in entity.FavorableAdviserExperiences
        )
        {
            if (
                FavorableAdviserExperience.Id
                == FavorableAdviserExperiences.AssetRangeCustomer
            )
            {
                point += PointBasedOnAssetRange(
                    entity.AssetRange,
                    adviser.CustomerStatics
                );
            }
            else if (
                FavorableAdviserExperience.Id
                == FavorableAdviserExperiences.OccupationCustomer
            )
            {
                point += PointBasedOnOccupation(
                    entity.InvestorOccupationType,
                    adviser.CustomerStatics
                );
            }
        }
        return point;
    }

    private static double PointBasedOnAssetRange(
        AssetRanges assetRange,
        CustomerStatics stats
    )
    {
        double point = 0;
        point += assetRange switch
        {
            AssetRanges.B0_A500 => stats.OwnedAssetsLess500 ?? 0,
            AssetRanges.B500_A1000 => stats.OwnedAssetsLess1000 ?? 0,
            AssetRanges.B1000_A2000 => stats.OwnedAssetsLess3000 ?? 0,
            AssetRanges.B2000_A3000 => stats.OwnedAssetsLess3000 ?? 0,
            AssetRanges.B1000_A3000 => stats.OwnedAssetsLess3000 ?? 0,
            AssetRanges.B3000_A5000 => stats.OwnedAssetsLess5000 ?? 0,
            AssetRanges.B5000_A7000 => stats.OwnedAssetsLess7000 ?? 0,
            AssetRanges.B7000_A10000 => stats.OwnedAssetsLess10000 ?? 0,
            AssetRanges.B10000_A20000 => stats.OwnedAssetsLess50000 ?? 0,
            AssetRanges.B20000_A30000 => stats.OwnedAssetsLess50000 ?? 0,
            AssetRanges.B30000_A40000 => stats.OwnedAssetsLess50000 ?? 0,
            AssetRanges.B40000_A50000 => stats.OwnedAssetsLess50000 ?? 0,
            AssetRanges.B50000_A100000
                => stats.OwnedAssetGreaterEqual50000 ?? 0,
            AssetRanges.B100000 => stats.OwnedAssetGreaterEqual50000 ?? 0,
            _
                => throw new ArgumentOutOfRangeException(
                    $"'{assetRange}' is not a valid value for AssetRange"
                )
        };
        return point;
    }

    private static double PointBasedOnOccupation(
        InvestorOccupationTypes OccupationType,
        CustomerStatics stats
    )
    {
        double point = 0;
        point += OccupationType switch
        {
            InvestorOccupationTypes.OfficeWorker
                => stats.OccupationOfficeWorker ?? 0,
            InvestorOccupationTypes.Unemployed
                => stats.OccupationUnemployed ?? 0,
            InvestorOccupationTypes.Proprietor
                => stats.OccupationProprietor ?? 0,
            InvestorOccupationTypes.Executive
                => stats.OccupationProprietor ?? 0,
            InvestorOccupationTypes.Government
                => stats.OccupationGovernment ?? 0,
            InvestorOccupationTypes.Profession
                => stats.OccupationProfession ?? 0,
            InvestorOccupationTypes.Doctor => stats.OccupationDoctor ?? 0,
            _
                => throw new ArgumentOutOfRangeException(
                    $"'{OccupationType}' is not a valid value for OccupationType"
                )
        };
        return point;
    }

    private static int CalculateAdviserPoint(double point, Adviser adviser)
    {
        if (adviser.IfaCompany == null)
            return 0;

        point = point * adviser.IfaCompany.BatchIntroductionPriority / 100;
        if (adviser.BatchIntroductionSetting != null)
        {
            //変更点
            point =
                point * adviser.BatchIntroductionSetting.IntroductionRate / 100;
        }

        return (int)(point * 100);
    }

    private static int CalculatePersonalityPoint(
        PersonalitySurvey survey,
        Adviser adviser
    )
    {
        var personalityPoint = 0;
        var adviserSurvey = adviser.PersonalitySurvey;

        personalityPoint +=
            100
            - Math.Abs(
                (survey.NeuroticismStandardScore ?? 50)
                    - (adviserSurvey?.NeuroticismStandardScore ?? 50)
            );
        personalityPoint +=
            100
            - Math.Abs(
                (survey.OpennessStandardScore ?? 50)
                    - (adviserSurvey?.OpennessStandardScore ?? 50)
            );
        return personalityPoint;
    }

    public ICollection<Adviser> ExcludeSameCompanyRegularEmployee(
        IEnumerable<Adviser> advisers,
        int? limit = null,
        bool? introduceCompanyMultiple = false
    )
    {
        var results = new List<Adviser>();
        var excludeCompany = new HashSet<Ulid>();

        foreach (var adviser in advisers)
        {
            if (
                adviser.IfaCompany == null
                || adviser.CompanyByCompanyIntroduction == true
                    && excludeCompany.Contains(adviser.IfaCompany.Id)
                    && !(introduceCompanyMultiple ?? false)
            )
                continue;

            results.Add(adviser);
            if (limit != null && results.Count >= limit)
                break;

            if (adviser.CompanyByCompanyIntroduction == true)
                excludeCompany.Add(adviser.IfaCompany.Id);
        }

        return results;
    }

    public async Task SendNotificationOnSearch(BatchIntroduction data)
    {
        var template = new BatchIntroductionMailTemplate(
            data,
            _context,
            _settings
        );
        await _chatWork.TrySend(
            _settings.Investors.ChatWorkRoomId,
            template.GetContentsForNotificationOnSearch()
        );

        if (_amazonCloudWatch != null)
        {
            try
            {
                await _amazonCloudWatch.PutMetricDataAsync(
                    new PutMetricDataRequest
                    {
                        MetricData = new List<MetricDatum>
                        {
                            new MetricDatum
                            {
                                Dimensions = new List<Dimension> { _dimension },
                                MetricName = "Introduction",
                                Unit = StandardUnit.Count,
                                Value = 1,
                            }
                        },
                        Namespace = _settings
                            .Investors
                            .CloudWatchMetricsNamespace
                    }
                );
            }
            catch (Exception e)
            {
                Log.Error(e, "Failed to send metric to CloudWatch");
            }
        }
    }

    private class AdviserAndPoint
    {
        public Adviser Adviser { get; set; }
        public int Point { get; set; }
        public int PersonalityPoint { get; set; }
        public int Random { get; set; }
    }

    public ICollection<Ulid> FilterAcceptedAdvisers(
        ICollection<Ulid> adviserIds
    )
    {
        return AcceptedAdviserQuery
            .Where(a => adviserIds.Contains(a.Id))
            .Select(a => a.Id)
            .ToArray();
    }

    public ICollection<Ulid> FilterAcceptedAdvisersForCalendar(
        ICollection<Ulid> adviserIds
    )
    {
        return AcceptedAdviserQueryForCalendar
            .Where(a => adviserIds.Contains(a.Id))
            .Select(a => a.Id)
            .ToArray();
    }

    public IEnumerable<BatchIntroductionCsvData> GetCsvData(
        bool requestOnly = false
    )
    {
        var timeZone = DateUtils.GetJapanTimeZoneInfo();

        // 過去一ヶ月間のデータだけ持ってくるようにする
        var query = _context.BatchIntroductions
            .Where(b => b.CreatedTime > DateTime.UtcNow.AddMonths(-1))
            .Include(b => b.Advisers)
            .ThenInclude(b => b.IfaCompany)
            .ThenInclude(
                c => c == null ? null : c.BatchIntroductionIfaCompanySetting
            )
            .Include(b => b.AdviserWorkTypes)
            .Include(b => b.ConsultationTypes)
            .Include(b => b.ContactMethods)
            .AsQueryable();

        var queryRequest = query
            .Where(b => b.Status == BatchIntroductionStatus.Request)
            .ToArray();

        // 紹介については、同じ日に同じメールアドレスからの紹介は1件として扱う
        var queryIntro = query
            .Where(b => b.Status == BatchIntroductionStatus.Introduction)
            .ToArray()
            .GroupBy(b => new { b.EMail, b.CreatedTime.Date })
            .Select(g => g.First())
            .ToArray();

        var queryAll = queryRequest.Concat(queryIntro);

        queryAll = queryAll.OrderBy(b => b.CreatedTime);

        if (requestOnly)
            query = query.Where(
                b => b.Status == BatchIntroductionStatus.Request
            );

        var records = new List<dynamic>();
        foreach (var b in queryAll.ToArray())
        {
            Uri.TryCreate(b.OriginUrl, UriKind.Absolute, out var uri);
            var urlQuery = HttpUtility.ParseQueryString(uri?.Query ?? "");

            if (b.IsTest())
                continue;

            var record = new BatchIntroductionCsvData
            {
                Id = b.Id.ToString(),
                Status = b.Status,
                Age = b.Age,
                AssetRange = b.AssetRange.ToString(),
                ConsultationRequestType = b.ConsultationRequestType,
                Name = b.Name,
                Furigana = b.Furigana,
                Gender = b.Gender,
                TelephoneNumber = b.TelephoneNumber,
                EMail = b.EMail,
                Prefecture = b.Prefecture,
                PostalCode = b.PostalCode,
                Address = b.Address,
                AdviserWorkTypes = string.Join(
                    ";",
                    b.AdviserWorkTypes.Select(w => w.DisplayName)
                ),
                InvestmentPurpose = b.InvestmentPurpose,
                ContactMethods = string.Join(
                    ";",
                    b.ContactMethods.Select(c => c.DisplayName)
                ),
                Remarks = b.Remarks,
                AnnualIncome = b.AnnualIncome,
                InvestorOccupationType = ModelUtils.GetDisplayName(
                    _context.InvestorOccupationTypes,
                    b.InvestorOccupationType
                ),
                ConsultationTypes = string.Join(
                    ";",
                    b.ConsultationTypes.Select(c => c.DisplayName)
                ),
                AcceptPrivacy = b.AcceptPrivacy,
                AcceptMailMagazine = b.AcceptMailMagazine,
                Advisers = string.Join(
                    ";",
                    b.Advisers.Select(a => $"{a.FamilyName} {a.FirstName}")
                ),
                RemoteHost = b.RemoteHost,
                OriginUrl = b.OriginUrl,
                OriginName = b.OriginName,
                CreatedTime = TimeZoneInfo.ConvertTimeFromUtc(
                    b.CreatedTime,
                    timeZone
                ),
                AdvisersCount = b.Advisers.Count,
                Advsn = urlQuery.Get("advsn"),
                UtmSource = urlQuery.Get("utm_source"),
                UtmMedium = urlQuery.Get("utm_medium"),
                UtmCampaign = urlQuery.Get("utm_campaign"),
                UtmContent = urlQuery.Get("utm_content"),
                UtmTerm = urlQuery.Get("utm_term"),
                RegisterEmail = b.RegisterEmail
            };

            if (b.Advisers.Count > 4)
                record.Adviser0Name = "ERROR: Too many advisers";

            foreach (var a in b.Advisers)
            {
                if (string.IsNullOrEmpty(record.Adviser0Name))
                {
                    record.Adviser0Name = $"{a.FamilyName} {a.FirstName}";
                    record.Adviser0IfaCompanyName = a.IfaCompany?.Name ?? "";
                }
                else if (string.IsNullOrEmpty(record.Adviser1Name))
                {
                    record.Adviser1Name = $"{a.FamilyName} {a.FirstName}";
                    record.Adviser1IfaCompanyName = a.IfaCompany?.Name ?? "";
                }
                else if (string.IsNullOrEmpty(record.Adviser2Name))
                {
                    record.Adviser2Name = $"{a.FamilyName} {a.FirstName}";
                    record.Adviser2IfaCompanyName = a.IfaCompany?.Name ?? "";
                }
                else if (string.IsNullOrEmpty(record.Adviser3Name))
                {
                    record.Adviser3Name = $"{a.FamilyName} {a.FirstName}";
                    record.Adviser3IfaCompanyName = a.IfaCompany?.Name ?? "";
                }
            }

            yield return record;
        }
    }

    public bool CanUse(Adviser adviser)
    {
        return adviser.IfaCompanyId != null
            && adviser.CompanyByCompanyIntroduction != null;
    }

    public async Task<BatchIntroductionSetting> GetSetting(Adviser adviser)
    {
        var a = await _context.BatchIntroductionSetting
            .Where(a => a.AdviserId == adviser.Id)
            .SingleOrDefaultAsync();

        a ??= BatchIntroductionSetting.GetDefault(adviser);
        return a;
    }

    public async Task UpdateSetting(
        Adviser adviser,
        BatchIntroductionSettingData data,
        User user
    )
    {
        var batch = _context.BatchIntroductionSetting
            .Where(a => a.AdviserId == adviser.Id)
            .FirstOrDefault();
        if (batch == null)
        {
            batch = BatchIntroductionSetting.GetDefault(adviser);
            _model.Create(batch);
        }
        else
        {
            _model.Update(data.To(batch, adviser.Id, _context));
        }
        _model.Create(new IntroductionSettingHistory(batch, user));
        await _context.SaveChangesAsync();
    }

    public async Task<BatchIntroductionIfaCompanySettingData> GetIfaCompanySetting(
        IfaCompany ifaCompany
    )
    {
        var a = await _context.BatchIntroductionIfaCompanySetting
            .Where(a => a.IfaCompanyId == ifaCompany.Id)
            .SingleOrDefaultAsync();

        a ??= BatchIntroductionIfaCompanySetting.GetDefault(ifaCompany);
        return new BatchIntroductionIfaCompanySettingData(a);
    }

    public void UpdateIfaCompanySetting(
        IfaCompany ifaCompany,
        BatchIntroductionIfaCompanySettingData data,
        bool fromIfaCompany
    )
    {
        _logDictionary.IfaCompanyBatchIntroductionSettingIsChanged(
            ifaCompany,
            data
        );

        var batch = _context.BatchIntroductionIfaCompanySetting
            .Where(a => a.IfaCompanyId == ifaCompany.Id)
            .FirstOrDefault();
        if (batch == null)
        {
            batch = BatchIntroductionIfaCompanySetting.GetDefault(ifaCompany);
            _model.Create(batch);
            _context.SaveChanges();
        }
        if (fromIfaCompany && batch.NotCompanySettingRange)
        {
            throw new CompanyHasNotChangingSettingRangePermissionError();
        }
        _model.Update(data.To(batch, ifaCompany.Id, _context));
        _context.SaveChanges();
    }

    private IQueryable<InvestorKeyData> SearchQuery(string? name, string? eMail)
    {
        IQueryable<BatchIntroduction> query = _context.BatchIntroductions;
        if (!string.IsNullOrWhiteSpace(name))
        {
            query = query.Where(b => b.Name.Contains(name));
        }
        if (!string.IsNullOrWhiteSpace(eMail))
        {
            query = query.Where(b => b.EMail.Contains(eMail));
        }

        var groupedQuery = query
            .GroupBy(b => new { b.Name, b.EMail })
            .Select(
                g =>
                    new InvestorKeyData
                    {
                        Name = g.Key.Name,
                        EMail = g.Key.EMail,
                        StaffContacted = g.Any(b => b.StaffContacted),
                        // Assumes CreatedTime is the introduction time.
                        FirstIntroductionTime = g.Min(b => b.CreatedTime)
                    }
            )
            .OrderByDescending(b => b.FirstIntroductionTime);
        return groupedQuery;
    }

    public IEnumerable<InvestorKeyData> SearchKeys(
        string? name,
        string? eMail,
        uint offset,
        uint count
    )
    {
        return SearchQuery(name, eMail)
            .Skip((int)offset)
            .Take((int)count)
            .ToList();
    }

    public async Task UpdateStaffContacted(
        string name,
        string eMail,
        bool contacted
    )
    {
        var batchIntroductions = await _context.BatchIntroductions
            .Where(b => b.Name == name && b.EMail == eMail)
            .ToListAsync();

        foreach (var batchIntroduction in batchIntroductions)
        {
            batchIntroduction.StaffContacted = contacted;
        }
        _context.SaveChanges();
    }

    public async Task UpdateInvestorInformationFromStaff(
        string name,
        string eMail,
        StaffBatchIntroductionLeadUpdateData data
    )
    {
        var batchIntroductions = await _context.BatchIntroductions
            .Where(b => b.Name == name && b.EMail == eMail)
            .ToListAsync();
        foreach (var batchIntroduction in batchIntroductions)
        {
            batchIntroduction.RemarksFromStaff = data.RemarksFromStaff;
            batchIntroduction.ConsultationContents = data.ConsultationContents;
            batchIntroduction.AssetStatusText = data.AssetStatusText;
            batchIntroduction.CustomerAttributeText =
                data.CustomerAttributeText;
        }
        _context.SaveChanges();
    }

    public async Task UpdateInvestorAssetRange(
        Ulid batchIntroductionId,
        AssetRanges assetRange
    )
    {
        BatchIntroduction investor = await GetInvestor(batchIntroductionId);
        investor.AssetRange = assetRange;
        await _context.SaveChangesAsync();
    }

    public int CountSearchKeys(string? name, string? eMail)
    {
        return SearchQuery(name, eMail).ToArray().Count();
    }

    public async Task DeletePrivacyInformation(string name, string eMail)
    {
        var batchIntroductions = await _context.BatchIntroductions
            .Where(b => b.Name == name && b.EMail == eMail)
            .ToListAsync();
        foreach (var batchIntroduction in batchIntroductions)
        {
            Log.Information(
                "Delete investor privacy information: {Id},{Name},{EMail},{TelephoneNumber}",
                batchIntroduction.Id,
                batchIntroduction.Name,
                batchIntroduction.EMail,
                batchIntroduction.TelephoneNumber
            );
            batchIntroduction.DeleteInvestorPrivacyInformation();
        }
        _context.SaveChanges();
    }

    private IQueryable<BatchIntroduction> GetQuery(
        string? text,
        string? eMail,
        bool requestedOnly
    )
    {
        var query = _context.BatchIntroductions.AsQueryable();

        if (!string.IsNullOrEmpty(text))
        {
            text = text.Replace(" ", "").Replace("　", "");
            query = query.Where(a => a.Name.Contains(text));
        }

        if (!string.IsNullOrEmpty(eMail))
            query = query.Where(a => a.EMail.Contains(eMail));

        if (requestedOnly)
            query = query.Where(
                a => a.Status == BatchIntroductionStatus.Request
            );

        query = query.OrderByDescending(b => b.Id);
        return query;
    }

    public Task<int> GetCount(
        string? text,
        string? eMail,
        bool requestedOnly,
        string? staffMail
    )
    {
        if (staffMail != null)
        {
            return GetLeadQuery(text, eMail)
                .Where(
                    a => a.RegisterEmail != null && a.RegisterEmail == staffMail
                )
                .CountAsync();
        }
        return GetQuery(text, eMail, requestedOnly).CountAsync();
    }

    public ICollection<BatchIntroduction> Search(
        string? text,
        string? eMail,
        bool requestedOnly,
        uint offset = 0,
        uint count = 20
    )
    {
        var query = GetQuery(text, eMail, requestedOnly);

        return query.Skip((int)offset).Take((int)count).ToArray();
    }

    public async Task SendMail(
        Guid accessKey,
        StaffBatchIntroductionManagementData data,
        string watashiIfaUrl
    )
    {
        Log.Information(
            "Contact data by {MailAddress} from {OriginUrl}",
            data.EMail,
            data.OriginUrl
        );

        var mailFrom = new MailboxAddress(
            _settings.Investors.ConsultationRequestMail.FromName,
            _settings.Investors.ConsultationRequestMail.FromAddress
        );

        var error = false;
        BatchIntroductionSurveyRequestMailTemplate template =
            new(accessKey, data, _context);
        string content = template.GetContentsForInvestor(watashiIfaUrl);

        error =
            !await _emailService.TrySendMail(
                BatchIntroductionSurveyRequestMailTemplate.GetInvestorSubject(),
                mailFrom,
                data.EMail,
                content,
                data.OriginUrl
            ) || error;

        if (error)
            throw new NotificationMailError(data.OriginUrl, data.EMail);
    }

    public async Task<Guid> CreateEvaluationRequest(
        StaffBatchIntroductionManagementData data
    )
    {
        EvaluationRequest evaluationRequest = _model.Create(
            new EvaluationRequest
            {
                AccessKey = Guid.NewGuid(),
                Status = EvaluationStatuses.Request,
                BatchIntroductionId = data.Id
            }
        );
        await _context.SaveChangesAsync();
        return evaluationRequest.AccessKey;
    }

    public async Task<BatchIntroduction> GetInvestor(Ulid batchIntroductionId)
    {
        return await _model.SingleAsync(
            _context.BatchIntroductions.Where(a => a.Id == batchIntroductionId)
        );
    }

    public async Task AnswerCustomerSurvey(
        Ulid batchIntroductionId,
        CustomerSurveyRequest data,
        PersonalitySurvey? survey
    )
    {
        BatchIntroduction user = await GetInvestor(batchIntroductionId);
        bool b = _context.EvaluationRequests.Any(
            a => a.BatchIntroductionId == batchIntroductionId
        );
        EvaluationRequest evaluationRequest = await _model.SingleAsync(
            _context.EvaluationRequests.Where(
                a => a.BatchIntroductionId == batchIntroductionId
            )
        );
        ICollection<CustomerEvaluation> customerEvaluations =
            new List<CustomerEvaluation>();
        ICollection<CustomerBranchEvaluation> customerBranchEvaluations =
            new List<CustomerBranchEvaluation>();

        if (data.CustomerEvaluation != null)
        {
            foreach (var d in data.CustomerEvaluation)
            {
                CustomerEvaluation evaluation = _model.Create(
                    d.To(
                        new CustomerEvaluation
                        {
                            CreatedUserEmail = user.EMail ?? "",
                        },
                        DateTime.UtcNow
                    )
                );
                evaluation.Gender ??= user.Gender;
                evaluation.Prefecture ??= user.Prefecture;
                evaluation.Age ??= (byte?)user.Age;
                customerEvaluations.Add(evaluation);
                evaluationRequest.CustomerEvaluationId = evaluation.Id;
                evaluationRequest.CustomerEvaluation = evaluation;
            }
        }

        if (data.CustomerBranchEvaluation != null)
        {
            foreach (var d in data.CustomerBranchEvaluation)
            {
                CustomerBranchEvaluation branchEvaluation = _model.Create(
                    d.To(
                        new CustomerBranchEvaluation
                        {
                            CreatedUserEmail = user.EMail ?? "",
                        },
                        DateTime.UtcNow
                    )
                );
                branchEvaluation.Gender ??= user.Gender;
                branchEvaluation.Prefecture ??= user.Prefecture;
                branchEvaluation.Age ??= (byte?)user.Age;
                customerBranchEvaluations.Add(branchEvaluation);
                evaluationRequest.CustomerEvaluationId = branchEvaluation.Id;
                evaluationRequest.CustomerBranchEvaluation = branchEvaluation;
            }
        }

        CustomerSurvey customerSurvey = _model.Create(
            data.To(
                new CustomerSurvey
                {
                    BatchIntroduction = user,
                    CustomerEvaluation = customerEvaluations.Any()
                        ? customerEvaluations
                        : null,
                    CustomerBranchEvaluation = customerBranchEvaluations.Any()
                        ? customerBranchEvaluations
                        : null,
                    PersonalitySurvey = survey,
                }
            )
        );

        customerSurvey.Name = user.Name;
        customerSurvey.Age = user.Age;

        evaluationRequest.CustomerSurveyId = customerSurvey.Id;
        evaluationRequest.CustomerSurvey = customerSurvey;
        evaluationRequest.Status = EvaluationStatuses.Completed;
    }

    public async Task<StaffEvaluationRequestData> GetEvaluationRequest(
        Ulid batchIntroductionId
    )
    {
        return new StaffEvaluationRequestData(
            await _model.SingleAsync(
                _context.EvaluationRequests.Where(
                    a => a.BatchIntroductionId == batchIntroductionId
                )
            )
        );
    }

    public IQueryable<BatchIntroduction> GetLeadQuery(
        string? text,
        string? eMail,
        bool withRequested = false
    )
    {
        var query = _context.BatchIntroductions.AsQueryable();

        if (!string.IsNullOrEmpty(text))
        {
            text = text.Replace(" ", "").Replace("　", "");
            query = query.Where(i => i.Name.Contains(text));
        }

        if (!string.IsNullOrEmpty(eMail))
            query = query.Where(i => i.EMail.Contains(eMail));

        query = BatchIntroduction
            .QueryNotTest(query)
            .Where(
                i =>
                    // withRequestedで申込み済み者を含めるかどうかを判定
                    (
                        withRequested
                        && i.Status == BatchIntroductionStatus.Request
                    )
                    || (
                        i.Status == BatchIntroductionStatus.Introduction
                        && i.ParentIntroductionId == null
                        && !_context.BatchIntroductions.Any(
                            b =>
                                b.EMail == i.EMail
                                && b.Status == BatchIntroductionStatus.Request
                        )
                    )
            )
            .OrderByDescending(b => b.CreatedTime);

        return query;
    }

    public ICollection<BatchIntroduction> GetLeadList(
        string? text,
        string? eMail,
        uint offset = 0,
        uint count = 20,
        bool withRequested = false
    )
    {
        var query = GetLeadQuery(text, eMail, withRequested);
        return query.Skip((int)offset).Take((int)count).ToArray();
    }

    public int CountLeadList(string? text, string? eMail, string? staffMail)
    {
        var query = GetLeadQuery(text, eMail);
        if (!string.IsNullOrEmpty(staffMail))
        {
            query = query.Where(
                a => a.RegisterEmail != null && a.RegisterEmail == staffMail
            );
        }
        return query.Count();
    }

    private IQueryable<IntroductionSettingHistory> QueryIntroductionSettingHistory(
        string? text
    )
    {
        var query = _context.IntroductionSettingHistories.AsQueryable();
        if (!string.IsNullOrEmpty(text))
        {
            query = query.Where(
                i =>
                    i.AdviserName != null && i.AdviserName.Contains(text)
                    || i.IfaCompanyName != null
                        && i.IfaCompanyName.Contains(text)
                    || i.CreatedUserEMail != null
                        && i.CreatedUserEMail.Contains(text)
            );
        }
        return query;
    }

    public async Task<
        IEnumerable<IntroductionSettingHistoryData>
    > SearchHistory(string? text, int offset = 0, int count = 20)
    {
        var histories = await QueryIntroductionSettingHistory(text)
            .OrderByDescending(x => x.CreatedTime)
            .Skip(offset)
            .Take(count)
            .ToListAsync();
        return histories.Select(x => new IntroductionSettingHistoryData(x));
    }

    public async Task<int> SearchHistoryCount(string? text) =>
        await QueryIntroductionSettingHistory(text).CountAsync();

    public async Task<IntroductionSettingHistoryData?> GetIntroductionSettingHistory(
        Ulid adviserId
    )
    {
        var settingHistory = await _context.IntroductionSettingHistories
            .Where(a => a.AdviserId == adviserId)
            .OrderByDescending(a => a.CreatedTime)
            .FirstOrDefaultAsync();

        return settingHistory == null
            ? null
            : new IntroductionSettingHistoryData(settingHistory);
    }

    public async Task<
        IEnumerable<StaffEvaluationRequestData>
    > SearchEvaluationRequest(
        EvaluationStatuses? status,
        uint offset = 0,
        uint count = 20
    )
    {
        var query = _context.EvaluationRequests.AsQueryable();
        if (status != null)
        {
            query = query.Where(i => i.Status == status);
        }
        var evaluationRequests = await query
            .OrderByDescending(x => x.CreatedTime)
            .Skip((int)offset)
            .Take((int)count)
            .ToListAsync();
        return evaluationRequests.Select(
            x => new StaffEvaluationRequestData(x)
        );
    }

    public async Task CompleteTimerex(string eMail)
    {
        var batchIntroduction = await _context.BatchIntroductions
            .Where(a => a.EMail == eMail)
            .OrderByDescending(a => a.CreatedTime)
            .FirstOrDefaultAsync();
        if (batchIntroduction != null)
        {
            batchIntroduction.StaffContacted = true;
            await _context.SaveChangesAsync();
        }
    }

    public MemoryStream OutputNotRequestedInvestors()
    {
        var list = _context.BatchIntroductions
            .Select(
                b =>
                    new
                    {
                        b.Name,
                        b.EMail,
                        b.TelephoneNumber,
                        b.Status,
                        b.OriginName,
                        b.OriginUrl,
                        b.AssetRange,
                        b.AcceptMailMagazine
                    }
            )
            .GroupBy(b => b.EMail)
            .Where(
                g =>
                    g.All(
                        b =>
                            b.Status != BatchIntroductionStatus.Request
                            && !b.OriginName.Contains("スタッフ")
                            && !b.OriginUrl.Contains("staff")
                            && b.AssetRange != AssetRanges.B0_A500
                            && b.AssetRange != AssetRanges.B500_A1000
                            && !b.Name.Contains("テスト")
                            && !b.Name.Contains("test")
                            && !b.Name.Contains("てすと")
                            && !b.EMail.Contains("adviser-navi")
                            && b.AcceptMailMagazine
                    )
            )
            .Select(g => g.Key)
            .ToList();

        var stream = new MemoryStream();
        var writer = new StreamWriter(stream, Encoding.UTF8);
        writer.WriteLine("メールアドレス");
        foreach (var email in list)
        {
            writer.WriteLine(email);
        }
        writer.Flush();
        stream.Position = 0;
        return stream;
    }

    public void CheckValidInvestor(BatchIntroduction entity) =>
        _commonMatchingLogic.CheckValidInvestor(entity);

    public async Task<bool> IsAlreadyRequested(
        string remoteHost,
        string? AdnParentId
    )
    {
        if (AdnParentId == null)
        {
            // If AdnParentId is null, we only check the remote host.
            return await _context.BatchIntroductions.AnyAsync(
                a =>
                    a.RemoteHost == remoteHost
                    && a.Status == BatchIntroductionStatus.Request
            );
        }
        else
        {
            return await _context.BatchIntroductions.AnyAsync(
                a =>
                    (a.RemoteHost == remoteHost || a.AdnParentId == AdnParentId)
                    && a.Status == BatchIntroductionStatus.Request
            );
        }
    }

    public async Task<
        IEnumerable<RegisteredInvestorIntroductionData>
    > GetIntroductions(User user)
    {
        var adviserFamilyName = "";
        var adviserFirstName = "";
        var adviserCompanyName = "";
        var adviserPhotos = new List<ImageSpecAndUrl>();
        var adviserReviewAllAverage = 0.0;
        if (user.Adviser != null)
        {
            var customerEvaluationList = await _context.CustomerEvaluations
                .Where(e => e.AdviserId == user.Adviser.Id)
                .ToListAsync();
            if (customerEvaluationList != null && customerEvaluationList.Any())
            {
                adviserReviewAllAverage =
                    customerEvaluationList.Average(e => e.All) ?? 0;
            }
            adviserFamilyName = user.Adviser.FamilyName;
            adviserFirstName = user.Adviser.FirstName;
            adviserPhotos =
                (List<ImageSpecAndUrl>)
                    await _adviserProfileCommonLogic.GetPhotoUrls(user.Adviser);
            if (user.Adviser.IfaCompany != null)
            {
                adviserCompanyName = user.Adviser.IfaCompany.Name;
            }
        }

        return await _context.BatchIntroductions
            .AsNoTracking()
            .Where(
                a =>
                    a.EMail == user.MainEMail
                    && a.Status == BatchIntroductionStatus.Request
            )
            .Select(
                a =>
                    new RegisteredInvestorIntroductionData
                    {
                        AdviserFamilyName = adviserFamilyName,
                        AdviserFirstName = adviserFirstName,
                        AdviserCompanyName = adviserCompanyName,
                        IntroductionDate = a.CreatedTime.ToString("yyyy/MM/dd"),
                        InterviewDate = a.RegisteredInvestorIntroduceDate,
                        AdviserReviewAllAverage = adviserReviewAllAverage,
                        AdviserPhotos = adviserPhotos,
                    }
            )
            .ToListAsync();
    }

    public async Task SetIntroductionStatus(
        Ulid batchIntroductionId,
        RegisteredInvestorIntroductionStatusData data
    )
    {
        var batchIntroduction = await _context.BatchIntroductions
            .Where(a => a.Id == batchIntroductionId)
            .SingleOrDefaultAsync();
        if (batchIntroduction != null)
        {
            batchIntroduction.RegisteredInvestorIntroduceDate =
                data.IntroduceDate;
            await _context.SaveChangesAsync();
        }
    }
}
