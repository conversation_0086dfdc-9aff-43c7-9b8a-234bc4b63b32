using System.Dynamic;
using Adnavi.Domain.Models.Commons;
using Adnavi.Domain.Models.Consultations.BatchIntroductions;
using Adnavi.Domain.Models.Profiles.AdviserProfiles;
using Adnavi.Domain.Models.Profiles.InvestorProfiles;

using Microsoft.EntityFrameworkCore;
using NUlid;

namespace Adnavi.Domain.Logics.Profiles.AdviserProfiles;

public sealed class IntroductionParameterCSVRecord
{
    public static async Task<List<dynamic>> FromQuery(
        AdnaviDomainContext context,
        IQueryable<Adviser> query
    )
    {
        var query2 = query.Select(
            a =>
                new
                {
                    Id = a.Id,
                    FamilyName = a.FamilyName,
                    FirstName = a.FirstName,
                    IfaCompanyName = a.IfaCompany == null
                        ? null
                        : a.IfaCompany.Name,
                    CompanyByCompanyIntroduction = a.CompanyByCompanyIntroduction,
                    BatchIntroductionEnabled = a.BatchIntroductionSetting
                    == null
                        ? false
                        : a.BatchIntroductionSetting.Enabled,
                    BatchIntroductionSettingsId = a.BatchIntroductionSetting
                    == null
                        ? null
                        : a.BatchIntroductionSetting.Id as Ulid?,
                    IfaCompanyBatchIntroductionSettingsId = a.IfaCompany == null
                    || a.IfaCompany.BatchIntroductionIfaCompanySetting == null
                        ? null
                        : a.IfaCompany.BatchIntroductionIfaCompanySetting.Id
                            as Ulid?,
                    AdditionalProfileItemsId = a.AdditionalProfileItems == null
                        ? null
                        : a.AdditionalProfileItems.Id as Ulid?,
                }
        );
        var recordDicts = new List<dynamic>();

        foreach (var result in await query2.ToArrayAsync())
        {
            dynamic record = new ExpandoObject();
            var recordDict = (IDictionary<string, object?>)record;
            recordDict["Id"] = result.Id;
            recordDict["FamilyName"] = result.FamilyName;
            recordDict["FirstName"] = result.FirstName;
            recordDict["IfaCompanyName"] = result.IfaCompanyName;
            recordDict["CompanyByCompanyIntroduction"] =
                GetCompanyIntroductionDescription(
                    result.CompanyByCompanyIntroduction
                );
            recordDict["BatchIntroductionEnabled"] =
                result.BatchIntroductionEnabled;

            int adviserEnabledCount = 0;
            foreach (
                AssetRanges assetRange in Enum.GetValues(typeof(AssetRanges))
            )
            {
                recordDict["Adviser" + assetRange.ToString()] =
                    context.AssetRanges.Any(
                        b =>
                            b.BatchIntroductionSettings.Any(
                                c =>
                                    c.Id == result.BatchIntroductionSettingsId
                                    && b.Id == assetRange
                            )
                    )
                        ? "1"
                        : null;
                if (recordDict["Adviser" + assetRange.ToString()] != null)
                    adviserEnabledCount++;
            }
            if (adviserEnabledCount == 0)
            {
                foreach (
                    AssetRanges assetRange in Enum.GetValues(
                        typeof(AssetRanges)
                    )
                )
                {
                    recordDict["Adviser" + assetRange.ToString()] = "1";
                }
            }

            int companyEnabledCount = 0;

            foreach (
                AssetRanges assetRange in Enum.GetValues(typeof(AssetRanges))
            )
            {
                recordDict["Company" + assetRange.ToString()] =
                    context.AssetRanges.Any(
                        b =>
                            b.BatchIntroductionIfaCompanySetting.Any(
                                c =>
                                    c.Id
                                        == result.IfaCompanyBatchIntroductionSettingsId
                                    && b.Id == assetRange
                            )
                    )
                        ? "1"
                        : null;
                if (recordDict["Company" + assetRange.ToString()] != null)
                    companyEnabledCount++;
            }
            if (companyEnabledCount == 0)
            {
                foreach (
                    AssetRanges assetRange in Enum.GetValues(
                        typeof(AssetRanges)
                    )
                )
                {
                    recordDict["Company" + assetRange.ToString()] = "1";
                }
            }
            foreach (
                ContactMethods contactMethod in Enum.GetValues(
                    typeof(ContactMethods)
                )
            )
            {
                recordDict[contactMethod.ToString()] =
                    context.ContactMethods.Any(
                        b =>
                            b.AdditionalProfileItems.Any(
                                c =>
                                    c.Id == result.AdditionalProfileItemsId
                                    && b.Id == contactMethod
                            )
                    )
                        ? "1"
                        : null;
            }
            foreach (
                Prefectures prefecture in Enum.GetValues(typeof(Prefectures))
            )
            {
                recordDict["Visit" + prefecture.ToString()] =
                    context.Prefectures.Any(
                        b =>
                            b.VisitAdvisers.Any(
                                c => c.Id == result.Id && b.Id == prefecture
                            )
                    )
                        ? "1"
                        : null;
            }
            foreach (
                Prefectures prefecture in Enum.GetValues(typeof(Prefectures))
            )
            {
                recordDict["WebMeeting" + prefecture.ToString()] =
                    context.Prefectures.Any(
                        b =>
                            b.VisitAdvisers.Any(
                                c => c.Id == result.Id && b.Id == prefecture
                            )
                    )
                        ? "1"
                        : null;
            }

            recordDict["IntroductionCount"] = BatchIntroduction
                .QueryNotTest(context.BatchIntroductions)
                .Where(
                    b =>
                        b.Advisers.Any(c => c.Id == result.Id)
                        && b.Status == BatchIntroductionStatus.Introduction
                        && b.ParentIntroductionId == null
                )
                .Count();

            recordDict["RequestCount"] = BatchIntroduction
                .QueryNotTest(context.BatchIntroductions)
                .Where(
                    b =>
                        b.Advisers.Any(c => c.Id == result.Id)
                        && b.Status == BatchIntroductionStatus.Request
                )
                .Count();

            recordDicts.Add(recordDict);
        }
        return recordDicts;
    }

    private static string GetCompanyIntroductionDescription(
        bool? companyByCompanyIntroduction
    )
    {
        if (companyByCompanyIntroduction == null)
            return "";
        if (companyByCompanyIntroduction == true)
            return "企業で1人紹介";
        else
            return "企業で複数紹介";
    }
}
