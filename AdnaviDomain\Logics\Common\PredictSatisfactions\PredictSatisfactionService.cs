using Adnavi.Domain.Models.Commons;
using Adnavi.Utils;
using Microsoft.Extensions.Options;
using NUlid;
using Serilog;

namespace Adnavi.Domain.Logics.Common.PredictSatisfactions;

public class PredictSatisfactionService
{
    private readonly AdnaviS3Settings _settings;
    private readonly string _predictSatisfactionScoreUrl;
    private readonly string _aiModelBucket;

    public PredictSatisfactionService(IOptions<AdnaviS3Settings> settings)
    {
        _settings = settings.Value;
        _predictSatisfactionScoreUrl = _settings.PredictSatisfactionScoreUrl;
        _aiModelBucket = _settings.AdviserNaviAiModelBucketName;
    }

    /// <summary>
    /// Predict satisfaction score by AI model
    /// </summary>
    /// <param name="adviserId"></param>
    /// <param name="bucketName"></param>
    /// <param name="customerAge"></param>
    /// <param name="gender"></param>
    /// /// <param name="prefecture"></param>

    public async Task<double> PredictSatisfactionScore(
        Ulid adviserId,
        int customerAge,
        GenderTypes? gender,
        Prefectures prefecture
    )
    {
        string adviserIdString = adviserId.ToString();
        string ageString = customerAge.ToString();
        string genderString;
        if (gender == null)
            genderString = "0";
        else
            genderString = (int)gender + "";
        string prefectureString = (int)prefecture + "";

        using var httpClient = new HttpClient();

        // APIリクエストのURLを生成しアクセスする
        try
        {
            var apiUrl =
                $"{_predictSatisfactionScoreUrl}/predict?adviser={Uri.EscapeDataString(adviserIdString)}&bucket_name={Uri.EscapeDataString(_aiModelBucket)}&customer_age={Uri.EscapeDataString(ageString)}&gender={Uri.EscapeDataString(genderString)}&prefecture={Uri.EscapeDataString(prefectureString)}";
            var response = await httpClient.GetAsync(apiUrl);
            string responseBody = await response.Content.ReadAsStringAsync();
            double responseValue = double.Parse(responseBody);
            return responseValue;
        }
        // HTTPリクエストエラーの場合は0を返す (lambda初回起動時にtime out error起きる)
        catch (HttpRequestException e)
        {
            Log.Error(
                e,
                "http request error for predict satisfaction score api"
            );
            return 0;
        }
    }
}
