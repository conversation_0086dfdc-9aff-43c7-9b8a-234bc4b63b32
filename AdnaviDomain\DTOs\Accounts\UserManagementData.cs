using Adnavi.Domain.Common;
using Adnavi.Domain.Models.Accounts;
using Adnavi.Utils.Models;

namespace Adnavi.Domain.DTOs.Accounts;

/// <summary>
/// User DTO
/// </summary>
/// <seealso cref="User" />
public sealed class UserManagementData
    : IDataTransferObject,
        IObjectUpdater<User>
{
    public string? MainEMail { get; set; }

    public UserManagementData() { }

    public UserManagementData(User user)
    {
        MainEMail = user.MainEMail;
    }

    public User To(User target)
    {
        target.MainEMail = MainEMail;
        return target;
    }
}
