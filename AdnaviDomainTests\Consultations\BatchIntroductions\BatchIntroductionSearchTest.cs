using Adnavi.Domain;

using Xunit;
using Xunit.Abstractions;
using Microsoft.Extensions.DependencyInjection;
using Adnavi.Domain.Logics.Consultations.BatchIntroductions;
using Adnavi.AdnaviDomainTests.Utils;
using Adnavi.AdnaviDomainTests.Data;
using Adnavi.AdnaviDomainTests.Data.Consultations.BatchIntroductions;

namespace Adnavi.AdnaviDomainTests.Consultations.BatchIntroductions;

public class BatchIntroductionSearchTest : DomainTestBase
{
    private readonly BatchIntroductionLogic _logic;
    private readonly AdnaviDomainContext _context;
    private readonly AdviserTestDataA _adviserData = new();
    private readonly BatchIntroductionRequestTestDataA _batchIntroductionData =
        new();

    public BatchIntroductionSearchTest(ITestOutputHelper output)
        : base(output)
    {
        _context = Services.GetRequiredService<AdnaviDomainContext>();
        _logic = Services.GetRequiredService<BatchIntroductionLogic>();

        var data = _adviserData.GetTwoAdviserSameCompany(_context);
        _context.Add(data.Item1);
        _context.Add(data.Item2);
        _context.Add(data.Item3);
        _context.SaveChanges();
    }

    [Fact]
    public async void GenerateBatchIntroductionForIntroduction_IntroduceCompanyMultiple_OutputAdviserTwo()
    {
        // Arrange
        var requestData = _batchIntroductionData.GetData1();
        requestData.IntroduceCompanyMultiple = true;

        // Act
        var result = await _logic.GenerateBatchIntroductionForIntroduction(
            data: requestData,
            remoteHost: "",
            survey: null,
            sortOption: AdviserSortOptionForBatchIntroduction.MatchingRate
        );

        // Assert
        Assert.True(result.Advisers.Count() == 2);
    }

    [Fact]
    public async void GenerateBatchIntroductionForIntroduction_NotIntroduceCompanyMultiple_OutputAdviserZero()
    {
        // Arrange
        var requestData = _batchIntroductionData.GetData1();
        requestData.IntroduceCompanyMultiple = false;

        // Act
        var result = await _logic.GenerateBatchIntroductionForIntroduction(
            data: requestData,
            remoteHost: "",
            survey: null,
            sortOption: AdviserSortOptionForBatchIntroduction.MatchingRate
        );

        // Assert
        Assert.True(result.Advisers.Count() == 1);
    }
}
