using Adnavi.Domain.Common;

namespace Adnavi.Domain.Logics.Consultations;

public class InvestorsSettings
{
    public string AdviserTestMailAddress { get; set; }
    public string AdviserTestMailAddressForCalendar { get; set; }

    public string ChatWorkRoomId { get; set; }

    /// <summary>資産運用に関わるアンケート通知先ChatWorkのルームID</summary>
    public string AssetSurveyChatWorkRoomId { get; set; }

    public string InfluencerChatWorkRoomId { get; set; }

    public string CalendarAdviserNotificationChatWorkRoomId { get; set; }

    public MailNotification ConsultationRequestMail { get; set; }
    public MailNotification ConsultationRequestMail2 { get; set; }

    public string InsuranceChatWorkRoomId { get; set; }

    public MailNotification InsuranceConsultationRequestMail { get; set; }

    public MailNotification InsuranceConsultationRequestMail2 { get; set; }

    public string SurveyMail { get; set; }
    public bool ReminderServiceEnabled { get; set; }

    public string CloudWatchMetricsNamespace { get; set; }
    public string IfaContactFormDestination { get; set; }
}
