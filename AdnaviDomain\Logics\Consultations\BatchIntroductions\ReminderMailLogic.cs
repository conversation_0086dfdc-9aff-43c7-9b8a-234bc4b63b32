using Adnavi.Domain.Logics.Common.EMails;
using Adnavi.Domain.Models.Consultations.BatchIntroductions;
using Adnavi.Utils;
using Microsoft.Extensions.Options;
using MimeKit;

namespace Adnavi.Domain.Logics.Consultations.BatchIntroductions;

public class ReminderMailLogic
{
    private readonly AdnaviDomainContext _context;
    private readonly EMailService _emailService;
    private readonly InvestorsSettings _settings;

    public ReminderMailLogic(
        IOptions<AdnaviDomainSettings> settings,
        AdnaviDomainContext context,
        EMailService emailService
    )
    {
        _settings = settings.Value.Investors;
        _context = context;
        _emailService = emailService;
    }

    public List<BatchIntroduction> GetReminderIntroductions(
        DateTime? limit = null
    )
    {
        var currentTime = DateTime.UtcNow;
        var timeLimit = limit ?? currentTime.AddHours(-2);
        return _context.BatchIntroductions
            .Where(
                b =>
                    !b.SendRemindMail
                    && !b.StaffContacted
                    && string.IsNullOrEmpty(b.RegisterEmail) // <- !IsStaffCreated
                    && b.Status == BatchIntroductionStatus.Introduction
                    && b.CreatedTime >= timeLimit
                    && b.CreatedTime <= currentTime.AddMinutes(-5)
                    // The email has not send remind mail before or user has not request consultation
                    && !_context.BatchIntroductions.Any(
                        bb =>
                            bb.EMail == b.EMail
                            && (
                                bb.SendRemindMail
                                || bb.StaffContacted
                                || bb.Status == BatchIntroductionStatus.Request
                            )
                    )
            )
            .OrderByDescending(b => b.CreatedTime)
            .GroupBy(b => b.EMail)
            .Select(g => g.First())
            .ToList();
    }

    public async Task DoSendMail(
        BatchIntroduction data,
        BatchIntroductionReminderMailTemplate template,
        string origin,
        string textType = "plain"
    )
    {
        var mailFrom = new MailboxAddress(
            _settings.ConsultationRequestMail.FromName,
            _settings.ConsultationRequestMail.FromAddress
        );
        string content = template.GetContent(data);

        var success = await _emailService.TrySendMail(
            template.GetSubject(),
            mailFrom,
            data.EMail,
            content,
            origin,
            textType
        );

        var staffNotificationMails = _settings
            .ConsultationRequestMail
            .NotificationMailAddresses;

        // Send mail to the adviser-navi staff.
        foreach (var mail in staffNotificationMails)
        {
            await _emailService.TrySendMail(
                template.GetNotificationSubject(success),
                mailFrom,
                mail,
                content
                    + $@"<pre>
━━━━━━━━━━━ 管理用情報 ━━━━━━━━━━━
投資家名:{data.Name}
投資家メールアドレス:{data.EMail}
検索日:{DateUtils.UtcToJst(data.CreatedTime)}
検索ID：{data.Id}
お問い合わせ元のページ:{data.OriginUrl}
</pre>
",
                origin,
                textType
            );
        }
    }
}
