#pragma warning disable CA1822 // Mark members as static

using System.Text.RegularExpressions;
using Adnavi.Domain.Models;
using Adnavi.Domain.Models.Consultations.InsuranceBatchIntroductions;
using Adnavi.Domain.Models.Profiles.AdviserProfiles;
using Adnavi.Domain.Models.Profiles.InvestorProfiles;
using Microsoft.Extensions.Options;
using Scriban;

namespace Adnavi.Domain.Logics.Consultations.InsuranceBatchIntroductions;

public class InsuranceBatchIntroductionMailTemplate
{
    private readonly InsuranceBatchIntroduction _data;
    protected readonly AdnaviDomainContext _context;
    private readonly string _watashiIfaUrl;
    private readonly bool _isStaff;

    public InsuranceBatchIntroductionMailTemplate(
        InsuranceBatchIntroduction data,
        AdnaviDomainContext context,
        IOptions<AdnaviDomainSettings> settings,
        bool isStaff
    )
    {
        _data = data;
        _context = context;
        _watashiIfaUrl = settings.Value.WatashiIfaUrl;
        _isStaff = isStaff;
    }

    public string GetInvestorSubject() =>
        "お問合せありがとうございました【保険を検討している方と保険アドバイザーのマッチングサイト「生命保険ナビ」】";

    public string GetNotificationSubject(string originName) =>
        $"【フォーム:{originName}】お問い合わせがありました【保険を検討している方と保険アドバイザーのマッチングサイト「生命保険ナビ」】";

    public string GetInsuranceCompanySubject(InsuranceAdviser adviser) =>
        $"保険を検討している方から貴社{adviser.FamilyName}様へのご面談申し込み（控え）";

    public string GetAdviserSubject() => "保険を検討している方からのご面談希望（ご対応をお願いいたします。）";

    public string GetAdviserSettingText(InsuranceAdviser adviser)
    {
        string settingDetails;

        var setting = _context.InsuranceBatchIntroductionSetting
            .Where(s => s.InsuranceAdviserId == adviser.Id)
            .SingleOrDefault();
        var acceptAnnualIncomes = "";
        var acceptAssetRanges = "";
        var acceptAgeRanges = "";
        var contactMethods = "";

        if (setting != null && setting.Enabled)
        {
            contactMethods =
                setting?.InsuranceAdviser?.AdditionalProfileItems?.AvailableContactMethods?.Any()
                    == true
                && setting != null
                    ? string.Join(
                        ",",
                        setting.InsuranceAdviser.AdditionalProfileItems.AvailableContactMethods
                            .Where(
                                a =>
                                    a.Id == ContactMethods.Visit
                                    || a.Id == ContactMethods.WebMeeting
                            )
                            .Select(a => a.DisplayName)
                    )
                    : "なし";
            acceptAnnualIncomes =
                setting?.AcceptAnnualIncomes?.Any() == true
                && setting.AcceptAnnualIncomes.Count()
                    != _context.AnnualIncomes.ToList().Count
                    ? string.Join(
                        ",",
                        setting.AcceptAnnualIncomes.Select(a => a.DisplayName)
                    )
                    : "全ての年収を受け付けます";

            acceptAssetRanges =
                setting?.AcceptAssetRanges?.Any() == true
                // B1000_A3000, B10000_A50000 を除いたものすべてが選択されていない場合
                && setting.AcceptAssetRanges.Count()
                    < _context.AssetRanges.ToList().Count - 2
                    ? string.Join(
                        ",",
                        setting.AcceptAssetRanges.Select(a => a.DisplayName)
                    )
                    : "全ての資産を受け付けます";
            acceptAgeRanges =
                setting?.YoungerLimit != null && setting?.OlderLimit != null
                    ? $"{setting.YoungerLimit}歳～{setting.OlderLimit}歳"
                    : "全ての年齢を受け付けます";

            settingDetails =
                $@"
━━━━━━━━━━━━━━━━━━━━━━━━━━
  {adviser.FamilyName}様の一括紹介設定
━━━━━━━━━━━━━━━━━━━━━━━━━━
 ・一括紹介 : 使用する
 ・紹介可能年収範囲 : {acceptAnnualIncomes}
 ・紹介可能資産範囲 : {acceptAssetRanges}
 ・紹介可能年齢範囲 : {acceptAgeRanges}
 ・面談方法 : {contactMethods}
";
        }
        else
        {
            settingDetails = "一括紹介 : 使用しない";
        }

        return settingDetails;
    }

    private const string requestInformation =
        @"
━━━━━━━━━━━━━━━━━━━━━━━━━━
  問い合わせ情報
━━━━━━━━━━━━━━━━━━━━━━━━━━

・お名前 : {{ data.Name }}
・年齢 : {{ if data.Age == -1 }}20歳未満{{ else if data.Age == 999 }}75歳以上{{ else }}{{ data.Age }}歳{{ end }}
・金融資産 : {{ assetRange }}
・年間収入 : {{ annualIncome }}
・性別 : {{ gender }}
・希望する連絡方法　:　{{ preferredFirstContactMethod }}
・携帯電話番号 : {{ data.TelephoneNumber }}
・メールアドレス : {{ data.EMail }}
・都道府県 : {{ prefecture }}
・職業 : {{ investorOccupationType }}
{{ if investmentPurpose != null ~}}
・投資意向・目的 :
　{{ investmentPurpose }}
{{~ end ~}}

・相談したい内容 :
{{~ for workType in data.AdviserWorkTypes ~}}
　{{ workType.MailDisplayName }}
{{~ end ~}}

・希望する面談方法 :
{{~ for contactMethod in contactMethods ~}}
　{{ if contactMethod == '来店対応' }}対面での面談{{ else }}{{ contactMethod }}{{ end }}
{{~ end ~}}

{{~ if data.Remarks != '' ~}}
・保険アドバイザーへの要望 :
{{ data.Remarks }}
{{~ end ~}}
";

    private const string staffWritten =
        @"
※ この度の紹介は、アドバイザーナビ株式会社　カスタマーサポートチームがお客様に代わり入力致しました。";

    private const string requestInformationWithoutInvestor =
        @"
{{~ if data.InvestorRemarks != '' ~}}
・カスタマーサポート連絡欄(投資家様には送信されません) :
{{ data.InvestorRemarks }}
{{~ end ~}}
";

    private const string footerText =
        @"
━━━━━━━━━━━━━━━━━━━━━━━━━━

本自動配信メールは、ご登録いただいたメールアドレス宛てに、保険を検討している方と保険アドバイザーのマッチングサイト「生命保険ナビ」事務局から送られたものです。
お心当たりのない方は、お問合せ窓口までご連絡くださいませ。

（お問合せ窓口）
アドバイザーナビ株式会社
カスタマーサポート
<EMAIL>
";

    private const string contentsForInvestor =
        @"{{ data.Name }} 様

お世話になります。
アドバイザーナビ株式会社「生命保険ナビ」カスタマーサポートチームでございます。
保険を検討している方と保険アドバイザーのマッチングサイト「生命保険ナビ」にお申し込みいただきましてありがとうございます。

下記の内容でお申し込みを賜りました。
3営業日以内に、お申し込みいただきました保険アドバイザーから、ご指定の宛先に連絡がまいります。
今しばらくお待ちくださいませ。
"
        + requestInformation
        + @"
・問い合わせ先アドバイザー名 :
{{~ for adviser in data.Advisers ~}}
　{{ adviser.FamilyName }} {{ adviser.FirstName }}
{{~ end ~}}
{{~ if errorAdvisers != empty ~}}
・アドバイザーへの問い合わせでエラーがありました
{{~ for adviser in errorAdvisers ~}}
　{{ adviser.FamilyName }} {{ adviser.FirstName }}
{{~ end ~}}
{{~ end ~}}

お申し込み先サイト : {{ data.OriginUrl }}
"
        + footerText;

    public string GetContentsForInvestor(
        IEnumerable<InsuranceAdviser> errorAdvisers
    ) => RenderTemplate(contentsForInvestor, errorAdvisers);

    public string GetContentsForNotification(
        IEnumerable<InsuranceAdviser> errorAdvisers
    )
    {
        string text = RenderTemplate(
            contentsForInvestor
                + (_isStaff ? requestInformationWithoutInvestor : "")
                + @"
━━━━━━━━━━━ 管理用情報 ━━━━━━━━━━━
お問い合わせタイプ：{{ data.OriginName }}
お問い合わせ元のページ：{{ data.OriginUrl }}
お問い合わせ元端末：{{ data.RemoteHost }}
リクエストID : {{ data.Id }}
個人情報の取扱いと利用規約への同意 : {{ if data.AcceptPrivacy }}はい{{ else }}いいえ{{ end }}
メールマガジンを受け取る : {{ if data.AcceptMailMagazine }}はい{{ else }}いいえ{{ end }}
",
            errorAdvisers
        );

        _context.Entry(_data).Collection(e => e.Advisers).Load();
        foreach (var adviser in _data.Advisers)
        {
            text += GetAdviserSettingText(adviser);
        }

        return text;
    }

    public string GetContentsForNotificationOnSearch()
    {
        var content =
            @"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
            一括検索でアドバイザーが検索されました。"
            + requestInformation
            + @"
・検索アドバイザー名 :
{{~ for adviser in data.Advisers ~}}
　{{ adviser.FamilyName }} {{ adviser.FirstName }}
{{~ end ~}}

お問い合わせタイプ：{{ data.OriginName }}
お問い合わせ元のページ：{{ data.OriginUrl }}
お問い合わせ元端末：{{ data.RemoteHost }}
リクエストID : {{ data.Id }}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
";
        return RenderTemplate(
            Regex.Replace(content, "\n+", "\n"),
            Array.Empty<InsuranceAdviser>()
        );
    }

    public string GetContentsForInsuranceCompany(InsuranceAdviser adviser)
    {
        string adviserSetting = GetAdviserSettingText(adviser);
        return RenderTemplate(
            $@"{adviser.FamilyName} {adviser.FirstName} 様

アドバイザーナビ株式会社　カスタマーサポートチームでございます。
投資家様から、貴社のアドバイザー{adviser.FamilyName}様に、保険のご相談に関するご面談希望のお申し込みがございました。

つきましては、お申し込みのお客様の情報の控えをお送りさせていただきます。
なお、{adviser.FamilyName}様へは、別途ご案内メールをお送りさせていただいております。
（投資家様に対しての個人情報同意は取得済みでございます。）

引き続き、よろしくお願いいたします。
"
                + (_isStaff ? staffWritten : "")
                + requestInformation
                + (_isStaff ? requestInformationWithoutInvestor : "")
                + adviserSetting
                + footerText,
            Array.Empty<InsuranceAdviser>()
        );
    }

    public string GetContentsForAdviser(InsuranceAdviser adviser)
    {
        string adviserSetting = GetAdviserSettingText(adviser);
        return RenderTemplate(
            $@"{adviser.FamilyName} {adviser.FirstName} 様

アドバイザーナビ株式会社　カスタマーサポートチームでございます。
投資家様から、{adviser.FamilyName}様に、保険のご相談に関するご面談のお申込みがございました。
お手数ではございますが、ご対応をお願いいたします。

投資家様に対しての個人情報同意は取得済みでございます。
また、{adviser.FamilyName}様から直接連絡を差し上げることは、　{_data.Name} 様はご認識されています。

ご面談調整のご連絡のほど、よろしくお願いいたします。
"
                + (_isStaff ? staffWritten : "")
                + requestInformation
                + (_isStaff ? requestInformationWithoutInvestor : "")
                + adviserSetting
                + footerText,
            Array.Empty<InsuranceAdviser>()
        );
    }

    private string RenderTemplate(
        string templateString,
        IEnumerable<InsuranceAdviser> errorAdvisers
    )
    {
        var template = Template.Parse(templateString);

        var assetRange = _context.AssetRanges
            .Find(_data.AssetRange)
            ?.DisplayName;
        var consultationRequestType = _context.ConsultationRequestTypes
            .Find(_data.ConsultationRequestType)
            ?.DisplayName;
        var gender = _context.GenderTypes.Find(_data.Gender)?.DisplayName;
        var prefecture = _context.Prefectures
            .Find(_data.Prefecture)
            ?.DisplayName;
        var investmentPurpose = _context.InvestmentPurposes
            .Find(_data.InvestmentPurpose)
            ?.DisplayName;
        var contactMethods = _data.ContactMethods.Select(
            c => c.Id == ContactMethods.Visit ? "対面での面談" : c.DisplayName
        );
        var annualIncome = _context.AnnualIncomes
            .Find(_data.AnnualIncome)
            ?.DisplayName;

        var investorOccupationType = ModelUtils.GetDisplayName(
            _context.InvestorOccupationTypes,
            _data.InvestorOccupationType
        );
        var preferredFirstContactMethod = ModelUtils.GetDisplayName(
            _context.FirstContactMethodTypes,
            _data.FirstContactMethodType
        );

        var contents = template.Render(
            new
            {
                data = _data,
                assetRange,
                consultationRequestType,
                gender,
                prefecture,
                investmentPurpose,
                contactMethods,
                annualIncome,
                investorOccupationType,
                errorAdvisers,
                preferredFirstContactMethod,
                watashiIfaUrl = _watashiIfaUrl,
                isStaff = _isStaff,
            },
            member => member.Name
        );
        return contents;
    }
}
