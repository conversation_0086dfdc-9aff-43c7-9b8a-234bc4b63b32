using Adnavi.Utils.Exceptions;

namespace Adnavi.Domain.Logics.Invoices.MatchingInvoices;

public class ReferralStartDateAlreadyRegisteredError : BadRequestException
{
    private string StartDate { get; }

    public ReferralStartDateAlreadyRegisteredError(string startDate)
        : base($"StartDate {startDate} is already registered.")
    {
        StartDate = startDate;
    }

    public override object AdditionalInformation => new { Message, StartDate };
}
