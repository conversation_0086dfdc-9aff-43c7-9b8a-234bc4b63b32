using System.ComponentModel.DataAnnotations;
using Adnavi.Domain.Models.Accounts;
using Adnavi.Utils.Models;
using Microsoft.EntityFrameworkCore;
using NUlid;

namespace Adnavi.Domain.DTOs.Accounts.OrganizationManagement;

/// <summary>
/// Organization DTO
/// /// </summary>
/// <seealso cref="Organization" />
public sealed class OrganizationManagementData : IDataTransferObject
{
    public Ulid Id { get; set; }

    public string Name { get; set; }

    public Ulid? ManagedIfaCompanyId { get; set; }

    [Required]
    public IEnumerable<UserManagementSummaryData> Users { get; set; }

    public OrganizationManagementData() { }

    public static async IAsyncEnumerable<OrganizationManagementData> FromQuery(
        IQueryable<Organization> query,
        AdnaviDomainContext context
    )
    {
        var data = await query
            .AsNoTracking()
            .Select(
                o =>
                    new OrganizationManagementData { Id = o.Id, Name = o.Name, }
            )
            .ToArrayAsync();

        var organizationIds = data.Select(o => o.Id).ToArray();
        var memberships = await UserManagementSummaryData
            .FromQuery(
                context.OrganizationMemberships
                    .AsNoTracking()
                    .Where(m => organizationIds.Contains(m.OrganizationId))
            )
            .ToArrayAsync();

        foreach (var o in data)
        {
            o.Users = memberships.Where(m => m.OrganizationId == o.Id);
            yield return o;
        }
    }
}
