﻿using Adnavi.Domain.Models.Profiles.CardProfiles;
using NUlid;

namespace Adnavi.Domain.Logics.Profiles.CardProfiles;

public sealed class UpdateCardProfileRequest
{
    public string CardCompanyName { get; set; }
    public string CardName { get; set; }
    public string CardRankName { get; set; }
    public string ReferenceID { get; set; }
    public ICollection<CardIssuanceConditionData> IssuanceConditions { get; set; }
    public ICollection<CardRedemptionData> Redemptions { get; set; }
    public ICollection<CardPromotionData> Promotions { get; set; }
    public ICollection<InternationalCardBrands> InternationalCardBrands { get; set; }
    public ICollection<CardSensitivityData> Sensitivities { get; set; }
    public ICollection<CardInsurances> CardInsurances { get; set; }
    public ICollection<CardPaymentMethods> CardPaymentMethods { get; set; }
    public CardRankTypes? CardRankType { get; set; }
    public string? Disclaimer { get; set; }
    public string IntroductionText { get; set; }
    public int? MaximumAnnualFee { get; set; }
    public int? MinimumAnnualFee { get; set; }
    public string? AnnualFeeText { get; set; }
    public string? Url { get; set; }
    public string SalesPoint { get; set; }
    public float Point { get; set; }
    public bool PickUp { get; set; }
    public bool Hidden { get; set; }
    public bool? IsMilePoint { get; set; }
    public DateTime CreatedTime { get; set; }
    public DateTime ModifiedTime { get; set; }

    public CardProfile To(CardProfile to, AdnaviDomainContext context)
    {
        to.CardCompanyName = CardCompanyName;
        to.CardName = CardName;
        to.CardRankName = CardRankName;
        to.ReferenceID = ReferenceID;

        to.IssuanceConditions.Clear();
        to.IssuanceConditions = IssuanceConditions
            .Select(
                x =>
                    new CardIssuanceCondition()
                    {
                        Id = Ulid.NewUlid(),
                        Statement = x.Statement,
                        CardProfileId = to.Id
                    }
            )
            .ToList();

        to.Redemptions.Clear();
        to.Redemptions = Redemptions
            .Select(
                x =>
                    new CardRedemption()
                    {
                        Id = Ulid.NewUlid(),
                        MinimumPercent = x.MinimumPercent,
                        MaximumPercent = x.MaximumPercent,
                        Statement = x.Statement,
                        CardProfileId = to.Id
                    }
            )
            .ToList();

        to.Promotions.Clear();
        to.Promotions = Promotions
            .Select(
                x =>
                    new CardPromotion()
                    {
                        Id = Ulid.NewUlid(),
                        Statement = x.Statement,
                        StartDate = x.StartDate,
                        EndDate = x.EndDate,
                        CardProfileId = to.Id
                    }
            )
            .ToList();

        to.InternationalCardBrands.Clear();
        to.InternationalCardBrands = context.InternationalCardBrands
            .Where(x => InternationalCardBrands.Contains(x.Id))
            .ToList();

        to.CardInsurances.Clear();
        to.CardInsurances = context.CardInsurances
            .Where(x => CardInsurances.Contains(x.Id))
            .ToList();

        to.CardPaymentMethods.Clear();
        to.CardPaymentMethods = context.CardPaymentMethods
            .Where(x => CardPaymentMethods.Contains(x.Id))
            .ToList();

        to.Sensitivities.Clear();
        to.Sensitivities = Sensitivities
            .Select(
                x =>
                    new CardSensitivity()
                    {
                        Id = Ulid.NewUlid(),
                        Statement = x.Statement,
                        CardProfileId = to.Id
                    }
            )
            .ToList();

        to.CardRankType = context.CardRankTypes.SingleOrDefault(
            x => x.Id == CardRankType
        );

        to.Disclaimer = Disclaimer ?? string.Empty;
        to.IntroductionText = IntroductionText;
        to.MaximumAnnualFee = MaximumAnnualFee;
        to.MinimumAnnualFee = MinimumAnnualFee;
        to.AnnualFeeText = AnnualFeeText;
        to.Url = Url;
        to.CreatedTime = DateTime.UtcNow;
        to.ModifiedTime = DateTime.UtcNow;
        to.SalesPoint = SalesPoint;
        to.Point = Point;
        to.PickUp = PickUp;
        to.Hidden = Hidden;
        to.IsMilePoint = IsMilePoint ?? false;

        return to;
    }
}
