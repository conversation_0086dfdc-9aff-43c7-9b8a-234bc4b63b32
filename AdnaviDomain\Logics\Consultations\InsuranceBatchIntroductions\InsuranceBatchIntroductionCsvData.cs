﻿using System.Web;
using Adnavi.Domain.Models.Commons;
using Adnavi.Domain.Models.Consultations.ConsultationRequests;
using Adnavi.Domain.Models.Consultations.InsuranceBatchIntroductions;
using Adnavi.Domain.Models.Profiles.InvestorProfiles;
using Adnavi.Utils;
using CsvHelper.Configuration.Attributes;
using Microsoft.EntityFrameworkCore;

namespace Adnavi.Domain.Logics.Consultations.InsuranceBatchIntroductions;

public class InsuranceBatchIntroductionCsvData
{
    [Name("#")]
    public string Id { get; set; }

    [Name("リード獲得日")]
    public string CreatedTime { get; set; }

    [Name("Status")]
    public InsuranceBatchIntroductionStatus Status { get; set; }

    [Name("氏名")]
    public string Name { get; set; }

    [Name("年齢")]
    public int Age { get; set; }

    [Name("年収")]
    public AnnualIncomes? AnnualIncome { get; set; }

    [Name("金融資産")]
    public AssetRanges? AssetRange { get; set; }

    [Name("電話番号")]
    public string TelephoneNumber { get; set; }

    [Name("メールアドレス")]
    public string EMail { get; set; }

    [Name("区分")]
    public string OriginName { get; set; }

    [Name("流入詳細（記入：URL貼り付け）")]
    public string OriginUrl { get; set; }
    public string? Advsn { get; set; }
    public string? UtmSource { get; set; }
    public string? UtmMedium { get; set; }
    public string? UtmCampaign { get; set; }
    public string? UtmContent { get; set; }

    [Name("性別")]
    public GenderTypes? Gender { get; set; }

    [Name("都道府県")]
    public Prefectures Prefecture { get; set; }

    [Name("相談内容")]
    public string AdviserWorkTypes { get; set; }

    [Name("連絡手段")]
    public string ContactMethods { get; set; }

    [Name("備考")]
    public string Remarks { get; set; }

    [Name("職業")]
    public InvestorOccupationTypes InvestorOccupationType { get; set; }

    [Name("アドバイザー")]
    public string InsuranceAdvisers { get; set; }
    public string RemoteHost { get; set; }

    [Name("IFA申し込み人数")]
    public int AdvisersCount { get; set; }

    [Name("アドバイザー①")]
    public string Adviser0Name { get; set; }

    [Name("所属会社①")]
    public string Adviser0IfaCompanyName { get; set; }

    [Name("アドバイザー②")]
    public string Adviser1Name { get; set; }

    [Name("所属会社②")]
    public string Adviser1IfaCompanyName { get; set; }

    [Name("アドバイザー③")]
    public string Adviser2Name { get; set; }

    [Name("所属会社③")]
    public string Adviser2IfaCompanyName { get; set; }

    [Name("アドバイザー④")]
    public string Adviser3Name { get; set; }

    [Name("所属会社④")]
    public string Adviser3IfaCompanyName { get; set; }

    public string? UtmTerm { get; set; }
    public string? RegisterEmail { get; set; }

    public static async IAsyncEnumerable<InsuranceBatchIntroductionCsvData> FromQuery(
        IQueryable<InsuranceBatchIntroduction> query
    )
    {
        var timeZone = DateUtils.GetJapanTimeZoneInfo();

        var query2 = query.Select(
            a =>
                new
                {
                    Record = new InsuranceBatchIntroductionCsvData
                    {
                        Id = a.Id.ToString(),
                        CreatedTime = TimeZoneInfo
                            .ConvertTimeFromUtc(a.CreatedTime, timeZone)
                            .ToString("yyyy/MM/dd HH:mm:ss"),
                        Status = a.Status,
                        Name = a.Name,
                        Age = a.Age,
                        AnnualIncome = a.AnnualIncome,
                        AssetRange = a.AssetRange,
                        TelephoneNumber = a.TelephoneNumber,
                        EMail = a.EMail,
                        OriginName = a.OriginName,
                        OriginUrl = a.OriginUrl,
                        Gender = a.Gender,
                        Prefecture = a.Prefecture,
                        Remarks = a.Remarks,
                        InvestorOccupationType = a.InvestorOccupationType,
                        RemoteHost = a.RemoteHost,
                        RegisterEmail = a.RegisterEmail,
                        ContactMethods = string.Join(
                            ";",
                            a.ContactMethods.Select(c => c.DisplayName)
                        ),
                        AdviserWorkTypes = string.Join(
                            ";",
                            a.AdviserWorkTypes.Select(w => w.DisplayName)
                        ),
                        InsuranceAdvisers = string.Join(
                            ";",
                            a.Advisers.Select(adviser => adviser.FullName)
                        ),
                        AdvisersCount = a.Advisers.Count,
                    },
                    a.OriginUrl,
                    a.Advisers,
                }
        );

        foreach (var result in await query2.ToArrayAsync())
        {
            Uri.TryCreate(result.OriginUrl, UriKind.Absolute, out var uri);
            var urlQuery = HttpUtility.ParseQueryString(uri?.Query ?? "");
            var adviser0 =
                result.Advisers.ElementAtOrDefault(0)?.FullName ?? "";
            var adviser1 =
                result.Advisers.ElementAtOrDefault(1)?.FullName ?? "";
            var adviser2 =
                result.Advisers.ElementAtOrDefault(2)?.FullName ?? "";
            var adviser3 =
                result.Advisers.ElementAtOrDefault(3)?.FullName ?? "";
            var adviser0IfaCompanyName =
                result.Advisers.ElementAtOrDefault(0)?.InsuranceCompany?.Name
                ?? "";
            var adviser1IfaCompanyName =
                result.Advisers.ElementAtOrDefault(1)?.InsuranceCompany?.Name
                ?? "";
            var adviser2IfaCompanyName =
                result.Advisers.ElementAtOrDefault(2)?.InsuranceCompany?.Name
                ?? "";
            var adviser3IfaCompanyName =
                result.Advisers.ElementAtOrDefault(3)?.InsuranceCompany?.Name
                ?? "";

            yield return new InsuranceBatchIntroductionCsvData
            {
                Id = result.Record.Id,
                CreatedTime = result.Record.CreatedTime,
                Status = result.Record.Status,
                Name = result.Record.Name,
                Age = result.Record.Age,
                AnnualIncome = result.Record.AnnualIncome,
                AssetRange = result.Record.AssetRange,
                TelephoneNumber = result.Record.TelephoneNumber,
                EMail = result.Record.EMail,
                OriginName = result.Record.OriginName,
                OriginUrl = result.Record.OriginUrl,
                Gender = result.Record.Gender,
                Prefecture = result.Record.Prefecture,
                Remarks = result.Record.Remarks,
                InvestorOccupationType = result.Record.InvestorOccupationType,
                RemoteHost = result.Record.RemoteHost,
                RegisterEmail = result.Record.RegisterEmail,
                ContactMethods = result.Record.ContactMethods,
                AdviserWorkTypes = result.Record.AdviserWorkTypes,
                InsuranceAdvisers = result.Record.InsuranceAdvisers,
                AdvisersCount = result.Record.AdvisersCount,
                Advsn = urlQuery.Get("advsn"),
                UtmSource = urlQuery.Get("utm_source"),
                UtmMedium = urlQuery.Get("utm_medium"),
                UtmCampaign = urlQuery.Get("utm_campaign"),
                UtmContent = urlQuery.Get("utm_content"),
                UtmTerm = urlQuery.Get("utm_term"),
                Adviser0Name = adviser0,
                Adviser1Name = adviser1,
                Adviser2Name = adviser2,
                Adviser3Name = adviser3,
                Adviser0IfaCompanyName = adviser0IfaCompanyName,
                Adviser1IfaCompanyName = adviser1IfaCompanyName,
                Adviser2IfaCompanyName = adviser2IfaCompanyName,
                Adviser3IfaCompanyName = adviser3IfaCompanyName
            };
        }
    }
}
