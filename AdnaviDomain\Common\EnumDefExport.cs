using Adnavi.Utils.EnumTables;

namespace Adnavi.Domain.Common;

class EnumDefExport<EnumType, RecordType> : IDefinitionExport
    where EnumType : struct, Enum
    where RecordType : class, IEnumRecord<EnumType>
{
    public string Name { get; }

    public string ValueType => typeof(EnumType).Name;

    private readonly EnumDefinitions<EnumType, RecordType> definitions;

    public IEnumerable<string> DefinitionNames =>
        new string[] { $"dict/ja-jp/{Name}", $"list/{Name}" };

    public EnumDefExport(
        string name,
        IQueryable<RecordType> entities,
        Func<string, IEnumRecord<EnumType>, bool>? listFilter = null
    )
    {
        Name = name;
        definitions = new EnumDefinitions<EnumType, RecordType>(
            entities,
            listFilter
        );
    }

    public bool DictMatched(string name) => name == $"dict/ja-jp/{Name}";

    public bool ListMatched(string name) => name == $"list/{Name}";

    public object GetDict() => definitions.Dict;

    public object GetList() => definitions.List;

    public void Check() => definitions.Check();
}
