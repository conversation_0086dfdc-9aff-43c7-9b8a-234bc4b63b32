using System.Globalization;
using Adnavi.Domain.Models.Commons;
using Adnavi.Domain.Models.Profiles.CardLoanProfiles;
using Adnavi.Domain.Models.Profiles.CardProfiles;
using Adnavi.Utils.Exceptions;
using CsvHelper;
using CsvHelper.Configuration;
using Microsoft.EntityFrameworkCore;
using Serilog;

namespace Adnavi.Domain.Logics.Profiles.CardProfiles.Csv;

public class CardCsvReader
{
    private readonly AdnaviDomainContext _context;

    public CardCsvReader(AdnaviDomainContext context)
    {
        _context = context;
    }

    public async Task UpdateData(Stream cardEvaluationStream)
    {
        try
        {
            Log.Information("Start updating Card Evaluation data");
            await LoadCardEvaluations(cardEvaluationStream);
        }
        catch (ValidationException ex)
        {
            throw new InvalidCsvError(typeof(CardEvaluationCsv), ex);
        }
    }

    public async Task UpdateCardLoanData(Stream cardLoanEvaluationStream)
    {
        try
        {
            Log.Information("Start updating Card Loan Evaluation data");
            await LoadCardLoanEvaluations(cardLoanEvaluationStream);
        }
        catch (ValidationException ex)
        {
            throw new InvalidCsvError(typeof(CardLoanEvaluationCsv), ex);
        }
    }

    private async Task LoadCardEvaluations(Stream stream)
    {
        var config = new CsvConfiguration(CultureInfo.InvariantCulture) { };
        using var reader = new StreamReader(stream);
        using var csv = new CsvReader(reader, config);

        while (csv.Read())
        {
            CardEvaluationCsv data;
            try
            {
                data = csv.GetRecord<CardEvaluationCsv>();
            }
            catch (ValidationException ex)
            {
                throw new InvalidCsvError(typeof(CardEvaluationCsv), ex);
            }

            if (
                data.RespondentID == 0
                || data.RespondentDate == DateTime.MinValue
            )
            {
                continue;
            }

            var cardEvaluation = await _context.CardEvaluations
                .Include(r => r.ComprehensiveAssessmentPoint)
                .Where(
                    c =>
                        c.RespondentID == data.RespondentID
                        && c.RespondentDate == data.RespondentDate
                )
                .SingleOrDefaultAsync();

            var creation = cardEvaluation == null;
            cardEvaluation = data.To(new CardEvaluation(), _context);

            if (creation)
            {
                await _context.CardEvaluations.AddAsync(cardEvaluation);
            }

            await _context.SaveChangesAsync();
        }
    }

    private async Task LoadCardLoanEvaluations(Stream stream)
    {
        var config = new CsvConfiguration(CultureInfo.InvariantCulture) { };
        using var reader = new StreamReader(stream);
        using var csv = new CsvReader(reader, config);

        while (csv.Read())
        {
            CardLoanEvaluationCsv data;
            try
            {
                data = csv.GetRecord<CardLoanEvaluationCsv>();
            }
            catch (ValidationException ex)
            {
                throw new InvalidCsvError(typeof(CardLoanEvaluationCsv), ex);
            }

            if (
                data.RespondentID == 0
                || data.RespondentDate == DateTime.MinValue
            )
            {
                continue;
            }

            var cardLoanEvaluation = await _context.CardLoanEvaluations
                .AsNoTracking()
                .Include(r => r.ComprehensiveAssessmentPoint)
                .Include(r => r.Recommended)
                .Where(
                    c =>
                        c.RespondentID == data.RespondentID
                        && c.RespondentDate == data.RespondentDate
                )
                .SingleOrDefaultAsync();

            var creation = cardLoanEvaluation == null;
            cardLoanEvaluation = data.To(new CardLoanEvaluation(), _context);

            if (creation)
            {
                await _context.CardLoanEvaluations.AddAsync(cardLoanEvaluation);
            }

            await _context.SaveChangesAsync();
        }
    }
}
