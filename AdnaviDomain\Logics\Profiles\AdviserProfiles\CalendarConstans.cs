namespace Adnavi.Domain.Logics.Profiles.AdviserProfiles;

public static class CalendarConstants
{
    public const string GoogleAuthUrl =
        "https://accounts.google.com/o/oauth2/auth";
    public const string GoogleTokenUrl = "https://oauth2.googleapis.com/token";
    public const string MicrosoftAuthUrl =
        "https://login.microsoftonline.com/common/oauth2/v2.0/authorize";
    public const string MicrosoftTokenUrl =
        "https://login.microsoftonline.com/common/oauth2/v2.0/token";
    public const string MicrosoftDefaultScope =
        "https://graph.microsoft.com/Calendars.ReadWrite offline_access";
    public const string GoogleApplicationName = "AdnaviApi";
    public const string GoogleResponseType = "code";
    public const string GoogleAccessType = "offline";
    public const string GooglePrompt = "consent";
    public const string MicrosoftResponseType = "code";
    public const string MicrosoftResponseMode = "query";
    public const string MicrosoftPrompt = "consent";
    public const string GoogleTimeZone = "Asia/Tokyo";
}
