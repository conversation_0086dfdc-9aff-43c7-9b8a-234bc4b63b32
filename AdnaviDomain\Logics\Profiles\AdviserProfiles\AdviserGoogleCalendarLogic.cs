using System.Text.Json;
using Adnavi.Domain.DTOs.Profiles.AdviserProfiles;
using Adnavi.Domain.Logics.Common;
using Adnavi.Domain.Models;
using Adnavi.Domain.Models.Profiles.AdviserProfiles;
using Adnavi.Utils.Exceptions;
using Google.Apis.Auth.OAuth2;
using Google.Apis.Calendar.v3;
using Google.Apis.Services;
using Microsoft.EntityFrameworkCore;
using NUlid;
using Serilog;

namespace Adnavi.Domain.Logics.Profiles.AdviserProfiles;

/// <summary>
/// Googleカレンダー認可・トークン管理ロジック
/// </summary>
public class AdviserGoogleCalendarLogic
{
    private readonly TokenProtectLogic _protector;
    private readonly ModelUtils _model;
    private readonly AdnaviDomainContext _context;
    private readonly HttpClient _httpClient;
    private readonly LogDictionary _logDictionary;

    public AdviserGoogleCalendarLogic(
        TokenProtectLogic tokenProtectLogic,
        ModelUtils model,
        AdnaviDomainContext context,
        HttpClient httpClient,
        LogDictionary logDictionary
    )
    {
        _model = model;
        _protector = tokenProtectLogic;
        _context = context;
        _httpClient = httpClient;
        _logDictionary = logDictionary;
    }

    // Google認可URL生成
    public string GetGoogleAuthUrl(
        string clientId,
        string redirectUri,
        string[] scopes
    )
    {
        var scope = string.Join(" ", scopes);
        var authUrl =
            $"{CalendarConstants.GoogleAuthUrl}"
            + $"?client_id={clientId}"
            + $"&redirect_uri={Uri.EscapeDataString(redirectUri)}"
            + $"&response_type={CalendarConstants.GoogleResponseType}"
            + $"&scope={Uri.EscapeDataString(scope)}"
            + $"&access_type={CalendarConstants.GoogleAccessType}"
            + $"&prompt={CalendarConstants.GooglePrompt}";
        return authUrl;
    }

    // Google認可コールバック処理
    public async Task HandleGoogleCallbackAsync(
        string code,
        Ulid adviserId,
        string clientId,
        string clientSecret,
        string redirectUri
    )
    {
        var tokenRequestBody = new Dictionary<string, string>
        {
            { "code", code },
            { "client_id", clientId },
            { "client_secret", clientSecret },
            { "redirect_uri", redirectUri },
            { "grant_type", "authorization_code" }
        };

        var tokenData = await RequestOAuth2TokenAsync(
            CalendarConstants.GoogleTokenUrl,
            tokenRequestBody,
            ex => new GoogleAuthException(ex)
        );

        if (tokenData == null || string.IsNullOrEmpty(tokenData.AccessToken))
        {
            throw new GoogleAuthException(
                "Failed to parse access token from Google response."
            );
        }
        var accessToken = tokenData.AccessToken;
        var refreshToken = tokenData.RefreshToken ?? "";
        var expiresIn = tokenData.ExpiresIn;

        await SaveAdviserGoogleToken(
            adviserId,
            accessToken,
            refreshToken,
            expiresIn
        );
    }

    // 有効なGoogleアクセストークン取得（必要に応じてリフレッシュ）
    public async Task<string?> GetValidGoogleAccessTokenAsync(
        Ulid adviserId,
        string clientId,
        string clientSecret
    )
    {
        var token = await _context.AdviserGoogleTokens
            .AsNoTracking()
            .SingleOrDefaultAsync(t => t.AdviserId == adviserId);

        if (token == null)
        {
            return null;
        }

        if (token.AccessTokenExpiry > DateTime.UtcNow.AddMinutes(2))
        {
            return _protector.Unprotect(token.AccessToken);
        }

        var refreshToken = _protector.Unprotect(token.RefreshToken);

        if (string.IsNullOrEmpty(refreshToken))
        {
            throw new GoogleAuthException(
                "Google refresh token is missing. Cannot refresh access token."
            );
        }
        var refreshTokenRequestBody = new Dictionary<string, string>
        {
            { "client_id", clientId },
            { "client_secret", clientSecret },
            { "refresh_token", refreshToken },
            { "grant_type", "refresh_token" }
        };
        var refreshTokenResponse = await _httpClient.PostAsync(
            CalendarConstants.GoogleTokenUrl,
            new FormUrlEncodedContent(refreshTokenRequestBody)
        );
        var refreshTokenResponseBody =
            await refreshTokenResponse.Content.ReadAsStringAsync();
        if (!refreshTokenResponse.IsSuccessStatusCode)
        {
            _context.AdviserGoogleTokens.Remove(token);
            await _context.SaveChangesAsync();
            return null;
        }
        var refreshTokenData = JsonSerializer.Deserialize<OAuth2TokenResponse>(
            refreshTokenResponseBody
        );

        if (
            refreshTokenData == null
            || string.IsNullOrEmpty(refreshTokenData.AccessToken)
        )
        {
            throw new GoogleAuthException(
                $"Failed to parse access token from Google response. Response: {refreshTokenResponseBody}"
            );
        }
        var newAccessToken = refreshTokenData.AccessToken;
        var newExpiresIn = refreshTokenData.ExpiresIn;

        await SaveAdviserGoogleToken(
            adviserId,
            newAccessToken,
            refreshToken,
            newExpiresIn
        );
        return newAccessToken;
    }

    // Googleカレンダー連携解除
    public async Task DisconnectGoogleCalendarAsync(Ulid adviserId)
    {
        var token = await _context.AdviserGoogleTokens.SingleOrDefaultAsync(
            t => t.AdviserId == adviserId
        );

        if (token != null)
        {
            _context.AdviserGoogleTokens.Remove(token);
            await _context.SaveChangesAsync();
        }
    }

    // Googleカレンダー連携済みか判定
    public async Task<bool> IsGoogleCalendarConnectedAsync(Ulid adviserId)
    {
        return await _context.AdviserGoogleTokens
            .AsNoTracking()
            .AnyAsync(t => t.AdviserId == adviserId);
    }

    // 内部: OAuth2トークン取得
    private async Task<OAuth2TokenResponse?> RequestOAuth2TokenAsync(
        string tokenUrl,
        Dictionary<string, string> requestBody,
        Func<string, Exception> exceptionFactory
    )
    {
        var response = await _httpClient.PostAsync(
            tokenUrl,
            new FormUrlEncodedContent(requestBody)
        );
        var responseBody = await response.Content.ReadAsStringAsync();
        if (!response.IsSuccessStatusCode)
        {
            throw exceptionFactory(
                $"Failed to get access token. Response: {responseBody}"
            );
        }
        var tokenData = JsonSerializer.Deserialize<OAuth2TokenResponse>(
            responseBody
        );
        return tokenData;
    }

    // AdviserGoogleToken保存
    private async Task SaveAdviserGoogleToken(
        Ulid adviserId,
        string accessToken,
        string refreshToken,
        int expiresIn
    )
    {
        var expiryDate = DateTime.UtcNow.AddSeconds(expiresIn);

        var existing = await _context.AdviserGoogleTokens.SingleOrDefaultAsync(
            t => t.AdviserId == adviserId
        );

        if (existing != null)
        {
            existing.AccessToken = _protector.Protect(accessToken);
            existing.RefreshToken = _protector.Protect(refreshToken);
            existing.AccessTokenExpiry = expiryDate;
            _model.Update(existing);
        }
        else
        {
            _model.Create(
                new AdviserGoogleToken
                {
                    AdviserId = adviserId,
                    AccessToken = _protector.Protect(accessToken),
                    RefreshToken = _protector.Protect(refreshToken),
                    AccessTokenExpiry = expiryDate
                }
            );
        }

        await _context.SaveChangesAsync();
    }

    // Googleカレンダーの予定（busy slot）を取得
    public async Task<
        List<(DateTimeOffset Start, DateTimeOffset End)>
    > GetGoogleBusySlots(
        string accessToken,
        DateTimeOffset startDateTime,
        DateTimeOffset endDateTime,
        string calendarId = "primary"
    )
    {
        var initializer = new BaseClientService.Initializer()
        {
            HttpClientInitializer = GoogleCredential.FromAccessToken(
                accessToken
            ),
            ApplicationName = "AdnaviApi"
        };
        var service = new CalendarService(initializer);

        var request = service.Events.List(calendarId);
        request.TimeMinDateTimeOffset = startDateTime;
        request.TimeMaxDateTimeOffset = endDateTime;
        request.SingleEvents = true;
        request.OrderBy = EventsResource.ListRequest.OrderByEnum.StartTime;

        try
        {
            var events = await request.ExecuteAsync();

            var busySlots =
                new List<(DateTimeOffset Start, DateTimeOffset End)>();
            foreach (var e in events.Items)
            {
                // working location（勤務場所）やタスクは除外
                if (
                    (
                        e.EventType != null
                        && (
                            e.EventType == "workingLocation"
                            || e.EventType == "task"
                        )
                    )
                    || (
                        e.Summary != null
                        && (
                            e.Summary == "Working Location"
                            || e.Summary == "Task"
                        )
                    )
                )
                {
                    continue;
                }

                // 通常の時間指定イベント
                if (
                    e.Start.DateTimeDateTimeOffset.HasValue
                    && e.End.DateTimeDateTimeOffset.HasValue
                )
                {
                    busySlots.Add(
                        (
                            e.Start.DateTimeDateTimeOffset.Value,
                            e.End.DateTimeDateTimeOffset.Value
                        )
                    );
                }
                // 終日予定（all-day event）
                else if (
                    !string.IsNullOrEmpty(e.Start.Date)
                    && !string.IsNullOrEmpty(e.End.Date)
                )
                {
                    // Google仕様: end.Dateは「翌日」になる
                    try
                    {
                        var jstOffset = TimeSpan.FromHours(9);
                        var allDayStart = new DateTimeOffset(
                            DateTime.ParseExact(
                                e.Start.Date,
                                "yyyy-MM-dd",
                                null
                            ),
                            jstOffset
                        );
                        var allDayEnd = new DateTimeOffset(
                            DateTime.ParseExact(e.End.Date, "yyyy-MM-dd", null),
                            jstOffset
                        );
                        busySlots.Add((allDayStart, allDayEnd));
                    }
                    catch (FormatException)
                    {
                        _logDictionary.AllDayEventDateParseFailed(
                            e.Start.Date,
                            e.End.Date
                        );
                        // 日付のパースに失敗した場合はスキップ
                        continue;
                    }
                }
            }
            return busySlots.OrderBy(b => b.Start).ToList();
        }
        catch (Exception ex)
        {
            throw new GoogleAuthException(
                "Failed to get busy slots from Google Calendar.",
                ex
            );
        }
    }

    // Googleカレンダーにイベントを作成
    public async Task<string> CreateEvent(
        string accessToken,
        DateTimeOffset startDateTime,
        string eventTitle,
        string eventDescription,
        string calendarId = "primary"
    )
    {
        var initializer = new BaseClientService.Initializer()
        {
            HttpClientInitializer = GoogleCredential.FromAccessToken(
                accessToken
            ),
            ApplicationName = CalendarConstants.GoogleApplicationName
        };
        var service = new CalendarService(initializer);

        var newEvent = new Google.Apis.Calendar.v3.Data.Event
        {
            Summary = eventTitle,
            Description = eventDescription,
            Start = new Google.Apis.Calendar.v3.Data.EventDateTime
            {
                DateTimeDateTimeOffset = startDateTime,
                TimeZone = CalendarConstants.GoogleTimeZone
            },
            End = new Google.Apis.Calendar.v3.Data.EventDateTime
            {
                DateTimeDateTimeOffset = startDateTime.AddHours(1),
                TimeZone = CalendarConstants.GoogleTimeZone
            }
        };

        try
        {
            var createdEvent = await service.Events
                .Insert(newEvent, calendarId)
                .ExecuteAsync();

            if (createdEvent == null)
            {
                throw new GoogleAuthException(
                    "Failed to create event in Google Calendar."
                );
            }
            return createdEvent.Id;
        }
        catch
        {
            throw new GoogleAuthException("Googleカレンダーへのイベント作成に失敗しました");
        }
    }
}
