﻿using Adnavi.Utils.EnumTables;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;

namespace Adnavi.Domain.Models.Profiles.AdviserArticles;

/// <summary>
/// ArticleCategory
/// /// </summary>
/// <seealso cref="ArticleCategoryResponse" />
[Index(nameof(Name), IsUnique = true)]
public class ArticleCategory : IEnumRecord<ArticleCategories>
{
    /// <summary>ID</summary>
    [Required]
    public ArticleCategories Id { get; set; }

    /// <summary>名前</summary>
    [Required]
    public string Name { get; set; }

    /// <summary>表示名</summary>
    [Required]
    public string DisplayName { get; set; }

    /// <summary>記事</summary>
    [Required]
    public virtual ICollection<Article> Articles { get; set; }
}
