using Adnavi.Domain.Logics.Consultations.BatchIntroductions;

namespace Adnavi.Domain.Logics.Consultations.MinimumRequirements;

public class AvailableSecurities : IRequirement
{
    private readonly string _name;

    public AvailableSecurities(string name)
    {
        _name = name;
    }

    public bool CheckAdviser(AdviserRequirementsCheck data)
    {
        if (data.CooperativeSecurities == null)
            return false;
        foreach (var securities in data.CooperativeSecurities)
        {
            if (securities.Name == _name)
                return true;
        }
        return false;
    }
}
