using Adnavi.Domain.Models.Consultations.BatchIntroductions;

namespace Adnavi.Domain.Models.Profiles.AdviserProfiles.InvestorFilters;

public class AgeYoungerLimitFilter : IInvestorFilter
{
    private readonly uint _youngerLimit;

    public AgeYoungerLimitFilter(uint youngerLimit)
    {
        _youngerLimit = youngerLimit;
    }

    public bool IsMatched(IInvestor investor) => investor.Age >= _youngerLimit;
}
