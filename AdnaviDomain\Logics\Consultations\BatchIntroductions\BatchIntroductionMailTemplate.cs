#pragma warning disable CA1822 // Mark members as static

using System.Text.RegularExpressions;
using Adnavi.Domain.Models;
using Adnavi.Domain.Models.Consultations.BatchIntroductions;
using Adnavi.Domain.Models.Profiles.AdviserProfiles;
using Scriban;

namespace Adnavi.Domain.Logics.Consultations.BatchIntroductions;

public class BatchIntroductionMailTemplate
{
    private readonly BatchIntroduction _data;
    protected readonly AdnaviDomainContext _context;
    private readonly string _watashiIfaUrl;

    public BatchIntroductionMailTemplate(
        BatchIntroduction data,
        AdnaviDomainContext context,
        AdnaviDomainSettings settings
    )
    {
        _data = data;
        _context = context;
        _watashiIfaUrl = settings.WatashiIfaUrl;
    }

    public string GetInvestorSubject() => "お申込ありがとうございました【資産運用の相談サイト「資産運用ナビ」】";

    public string GetNotificationSubject(string originName) =>
        $"【フォーム:{originName}】投資家からお問い合わせがありました【資産運用の相談サイト「資産運用ナビ」】";

    public string GetIfaCompanySubject(Adviser adviser) =>
        $"投資家様から、貴社{adviser.FamilyName}様へのご面談申し込み（控え）";

    public string GetAdviserSubject() => "投資家様からのご面談希望（ご対応をお願いいたします。）";

    public const string requestInformation =
        @"
━━━━━━━━━━━━━━━━━━━━━━━━━━
  問い合わせ情報
━━━━━━━━━━━━━━━━━━━━━━━━━━

・お名前 : {{ data.Name }}
・年齢 : {{ if data.Age == -1 }}20歳未満{{ else if data.Age == 999 }}75歳以上{{ else }}{{ data.Age }}歳{{ end }}
・金融資産 : {{ assetRange }}
・性別 : {{ gender }}
・希望する連絡方法　:　{{ preferredFirstContactMethod }}
・携帯電話番号 : {{ data.TelephoneNumber }}
・メールアドレス : {{ data.EMail }}
・都道府県 : {{ prefecture }}
・職業 : {{ investorOccupationType }}
{{ if investmentPurpose != null ~}}
・投資意向・目的 :
　{{ investmentPurpose }}
{{~ end ~}}

・相談したい内容 :
{{~ for workType in data.AdviserWorkTypes ~}}
　{{ workType.MailDisplayName }}
{{~ end ~}}

・希望する面談方法 :
{{~ for contactMethod in contactMethods ~}}
　{{ if contactMethod == '来店対応' }}対面での面談{{ else }}{{ contactMethod }}{{ end }}
{{~ end ~}}

{{~ if data.Remarks != '' ~}}
・資産運用アドバイザーへの要望 :
{{ data.Remarks }}
{{~ end ~}}
{{ if data.IsCalendarIntegrated }}
・面談予定日時 : 
  {{- if consultationStartTime != null && consultationEndTime != null -}}
    {{ consultationStartTime | date.to_string '%Y/%m/%d %H:%M' }} ～ {{ consultationEndTime | date.to_string '%Y/%m/%d %H:%M' }}
  {{- else if consultationStartTime != null -}}
    {{ consultationStartTime | date.to_string '%Y/%m/%d %H:%M' }}
  {{- else if consultationEndTime != null -}}
    {{ consultationEndTime | date.to_string '%Y/%m/%d %H:%M' }}
  {{- end }}
{{ end }}
";

    private const string footerText =
        @"
━━━━━━━━━━━━━━━━━━━━━━━━━━

本自動配信メールは、ご登録いただいたメールアドレス宛てに、資産運用の相談サイト「資産運用ナビ」事務局から送られたものです。
お心当たりのない方は、お問合せ窓口までご連絡くださいませ。

（お問合せ窓口）
アドバイザーナビ株式会社
カスタマーサポート
<EMAIL>
";

    private const string contentsForInvestor =
        @"{{ data.Name }} 様

お世話になります。
アドバイザーナビ株式会社「資産運用ナビ」カスタマーサポートチームでございます。
資産運用の相談サイト「資産運用ナビ」にお申し込みいただきましてありがとうございます。

下記の内容でお申し込みを賜りました。
3営業日以内に、お申し込みいただきました資産運用アドバイザーから、ご指定の宛先に連絡がまいります。
今しばらくお待ちくださいませ。
"
        + requestInformation
        + @"
・問い合わせ先アドバイザー名 :
{{~ for adviser in data.Advisers ~}}
　{{ adviser.FamilyName }} {{ adviser.FirstName }} {{watashiIfaUrl}}ifa/{{adviser.SequenceId}}/
{{~ end ~}}
{{~ if errorAdvisers != empty ~}}
・アドバイザーへの問い合わせでエラーがありました
{{~ for adviser in errorAdvisers ~}}
　{{ adviser.FamilyName }} {{ adviser.FirstName }} {{watashiIfaUrl}}ifa/{{adviser.SequenceId}}/
{{~ end ~}}
{{~ end ~}}

お申し込み先サイト : {{ data.OriginUrl }}
"
        + footerText;

    public string GetContentsForInvestor(IEnumerable<Adviser> errorAdvisers)
    {
        return RenderTemplate(contentsForInvestor, errorAdvisers);
    }

    public string GetContentsForNotification(
        IEnumerable<Adviser> errorAdvisers,
        string watashiIfaUrl
    )
    {
        return RenderTemplate(
            contentsForInvestor
                + @"
━━━━━━━━━━━ 管理用情報 ━━━━━━━━━━━
お問い合わせタイプ：{{ data.OriginName }}
お問い合わせ元のページ：{{ data.OriginUrl }}
お問い合わせ元端末：{{ data.RemoteHost }}
リクエストID : {{ data.Id }}
個人情報の取扱いと利用規約への同意 : {{ if data.AcceptPrivacy }}はい{{ else }}いいえ{{ end }}
メールマガジンを受け取る : {{ if data.AcceptMailMagazine }}はい{{ else }}いいえ{{ end }}
",
            errorAdvisers
        );
    }

    public string GetContentsForNotificationOnSearch()
    {
        var content =
            @"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
            一括検索でアドバイザーが検索されました。"
            + requestInformation
            + @"
・検索アドバイザー名 :
{{~ for adviser in data.Advisers ~}}
　{{ adviser.FamilyName }} {{ adviser.FirstName }}
{{~ end ~}}

お問い合わせタイプ：{{ data.OriginName }}
お問い合わせ元のページ：{{ data.OriginUrl }}
お問い合わせ元端末：{{ data.RemoteHost }}
リクエストID : {{ data.Id }}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
";
        return RenderTemplate(
            Regex.Replace(content, "\n+", "\n"),
            Array.Empty<Adviser>()
        );
    }

    public string GetContentsForIfaCompany(Adviser adviser)
    {
        return RenderTemplate(
            $@"{adviser.FamilyName} {adviser.FirstName} 様

アドバイザーナビ株式会社　カスタマーサポートチームでございます。
投資家様から、貴社のアドバイザー{adviser.FamilyName}様に、資産運用のご相談に関するご面談希望のお申し込みがございました。

つきましては、お申し込みのお客様の情報の控えをお送りさせていただきます。
なお、{adviser.FamilyName}様へは、別途ご案内メールをお送りさせていただいております。
（投資家様に対しての個人情報同意は取得済みでございます。）

引き続き、よろしくお願いいたします。
"
                + requestInformation
                + footerText,
            Array.Empty<Adviser>()
        );
    }

    public string GetContentsForAdviser(Adviser adviser)
    {
        return RenderTemplate(
            $@"{adviser.FamilyName} {adviser.FirstName} 様

アドバイザーナビ株式会社　カスタマーサポートチームでございます。
投資家様から、{adviser.FamilyName}様に、資産運用のご相談に関するご面談のお申込みがございました。
お手数ではございますが、ご対応をお願いいたします。

投資家様に対しての個人情報同意は取得済みでございます。
また、{adviser.FamilyName}様から直接連絡を差し上げることは、　{_data.Name} 様はご認識されています。
ご面談調整のご連絡のほど、よろしくお願いいたします。
"
                + requestInformation
                + footerText,
            Array.Empty<Adviser>()
        );
    }

    private string RenderTemplate(
        string templateString,
        IEnumerable<Adviser> errorAdvisers
    )
    {
        var template = Template.Parse(templateString);

        var assetRange = _context.AssetRanges
            .Find(_data.AssetRange)
            ?.DisplayName;
        var consultationRequestType = _context.ConsultationRequestTypes
            .Find(_data.ConsultationRequestType)
            ?.DisplayName;
        var gender = _context.GenderTypes.Find(_data.Gender)?.DisplayName;
        var prefecture = _context.Prefectures
            .Find(_data.Prefecture)
            ?.DisplayName;
        var investmentPurpose = _context.InvestmentPurposes
            .Find(_data.InvestmentPurpose)
            ?.DisplayName;
        var contactMethods = _data.ContactMethods.Select(
            c => c.Id == ContactMethods.Visit ? "対面での面談" : c.DisplayName
        );
        var annualIncome = _context.AnnualIncomes
            .Find(_data.AnnualIncome)
            ?.DisplayName;

        var investorOccupationType = ModelUtils.GetDisplayName(
            _context.InvestorOccupationTypes,
            _data.InvestorOccupationType
        );
        var preferredFirstContactMethod = ModelUtils.GetDisplayName(
            _context.FirstContactMethodTypes,
            _data.FirstContactMethodType
        );

        // Convert DateTimeOffset? to DateTime? for Scriban compatibility
        DateTime? consultationStartTime = _data.ConsultationStartTime?.DateTime;
        DateTime? consultationEndTime = _data.ConsultationEndTime?.DateTime;

        var contents = template.Render(
            new
            {
                data = _data,
                assetRange,
                consultationRequestType,
                gender,
                prefecture,
                investmentPurpose,
                contactMethods,
                annualIncome,
                investorOccupationType,
                errorAdvisers,
                preferredFirstContactMethod,
                watashiIfaUrl = _watashiIfaUrl,
                consultationStartTime,
                consultationEndTime
            },
            member => member.Name
        );
        return contents;
    }
}
