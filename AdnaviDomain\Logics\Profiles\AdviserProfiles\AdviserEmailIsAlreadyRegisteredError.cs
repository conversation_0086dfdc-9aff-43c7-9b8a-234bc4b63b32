using Adnavi.Utils.Exceptions;

namespace Adnavi.Domain.Logics.Profiles.AdviserProfiles;

public class AdviserEmailIsAlreadyRegisteredError : BadRequestException
{
    private string EMail { get; }

    public AdviserEmailIsAlreadyRegisteredError(string eMail)
        : base($"Email {eMail} of adviser is already registered.")
    {
        EMail = eMail;
    }

    public override object AdditionalInformation => new { Message, EMail };
}
