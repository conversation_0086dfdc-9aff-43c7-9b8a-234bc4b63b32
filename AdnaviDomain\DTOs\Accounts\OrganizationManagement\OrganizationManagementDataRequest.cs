using Adnavi.Domain.Models.Accounts;
using Adnavi.Utils.Models;
using NUlid;

namespace Adnavi.Domain.DTOs.Accounts.OrganizationManagement;

public sealed class OrganizationManagementDataRequest : IDataTransferObject
{
    public string Name { get; set; }
    public Ulid? ManagedIfaCompanyId { get; set; }

    public Ulid? ManagedInsuranceCompanyId { get; set; }

    public Organization To(Organization t)
    {
        t.Name = Name;
        t.InvoiceRemarks = string.Empty;
        return t;
    }
}
