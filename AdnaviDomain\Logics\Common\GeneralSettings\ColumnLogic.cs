using System.Text;
using System.Text.RegularExpressions;
using Adnavi.Domain;
using Adnavi.Domain.Logics.Common.GeneralSettings;
using Adnavi.Domain.Models.Common;
using Adnavi.Utils;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Org.BouncyCastle.Math.EC.Rfc7748;
using Serilog;

public class ColumnLogic
{
    private readonly AdnaviDomainContext _context;
    private readonly S3Utils _s3;

    public ColumnLogic(AdnaviDomainContext context, S3Utils s3)
    {
        _s3 = s3;
        _context = context;
    }

    public async Task<string> GetStaffColumnSettings(GeneralSettingTypes id)
    {
        var staffColumnSettings = await _context.GeneralSettings
            .Where(x => x.Id == id)
            .Select(x => x.JsonContents)
            .SingleOrDefaultAsync();
        string staffColumnSettingString = "";
        if (staffColumnSettings != null)
        {
            try
            {
                List<List<object>>? staffColumnSettingsList =
                    JsonConvert.DeserializeObject<List<List<object>>>(
                        staffColumnSettings
                    );
                if (staffColumnSettingsList != null)
                {
                    foreach (var item in staffColumnSettingsList)
                    {
                        staffColumnSettingString +=
                            item[0] + "\t" + item[1].ToString() + "\n";
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Json deserialization failed. {columnId}", id);
                throw new NotJsonTypeException();
            }
        }
        return staffColumnSettingString;
    }

    public async Task UpdateStaffColumnSettings(
        string staffColumnSettings,
        GeneralSettingTypes id
    )
    {
        List<List<object>> splittedStaffColumnSettings;
        string staffColumnSettingsJson;
        try
        {
            splittedStaffColumnSettings = StringUtils
                .SplitCsvColum(staffColumnSettings)
                .Select(x => x.Split("\t"))
                .Select(x => new List<object> { int.Parse(x[0]), x[1] })
                .ToList();

            staffColumnSettingsJson = System.Text.Json.JsonSerializer.Serialize(
                splittedStaffColumnSettings
            );
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Json deserialization failed. {columnId}", id);
            throw new NotJsonTypeException();
        }
        //S3に保存
        var asyncOldColumnKeys = _s3.List(
            "columns/cta-targets/watashi-ifa-column"
        );
        IEnumerable<string> oldColumnIds = Enumerable.Empty<string>();
        string pattern = @"\d+$";

        await foreach (var oldColumnKey in asyncOldColumnKeys) //非同期型から同期型に変換
        {
            oldColumnIds = oldColumnIds.Append(
                Regex.Match(oldColumnKey, pattern).Value
            );
        }

        var notDeleteColumnIds = new List<string>(); //消去しないもの

        foreach (var splittedStaffColumnSetting in splittedStaffColumnSettings)
        {
            var url = splittedStaffColumnSetting[1].ToString();
            var newColumnId = splittedStaffColumnSetting[0].ToString();

            if (oldColumnIds.Contains(newColumnId) && newColumnId != null)
            {
                notDeleteColumnIds.Add(newColumnId);
            }

            if (!string.IsNullOrEmpty(url))
            {
                byte[] encoding = Encoding.UTF8.GetBytes(url);
                using var stream = new MemoryStream(encoding);
                var key =
                    $"columns/cta-targets/watashi-ifa-column/{newColumnId}";
                await _s3.Put(key, "text/plain", stream);
            }
        }

        foreach (var oldColumnId in oldColumnIds)
        {
            if (!notDeleteColumnIds.Contains(oldColumnId))
            {
                await _s3.Delete(
                    $"columns/cta-targets/watashi-ifa-column/{oldColumnId}"
                );
            }
        }

        //DBに保存
        var settings = await _context.GeneralSettings
            .Where(x => x.Id == id)
            .SingleOrDefaultAsync();
        if (settings != null)
        {
            settings.JsonContents = staffColumnSettingsJson;
        }
        else
        {
            var newSettings = new GeneralSetting
            {
                Id = id,
                JsonContents = staffColumnSettingsJson
            };
            _context.GeneralSettings.Add(newSettings);
        }
        await _context.SaveChangesAsync();
    }

    public async Task<string?> GetPublicColumnSettings(int columnId)
    {
        var columnSettings = await _context.GeneralSettings
            .Where(x => x.Id == GeneralSettingTypes.WIfaCtaColumnNumbers)
            .Select(x => x.JsonContents)
            .SingleOrDefaultAsync();
        if (columnSettings != null)
        {
            try
            {
                List<List<object>>? staffColumnSettingsList =
                    JsonConvert.DeserializeObject<List<List<object>>>(
                        columnSettings
                    );
                if (staffColumnSettingsList != null)
                {
                    return staffColumnSettingsList
                        .Where(x => x[0].ToString() == columnId.ToString())
                        .Select(x => x[1].ToString())
                        .SingleOrDefault();
                }
            }
            catch (Exception ex)
            {
                Log.Error(
                    ex,
                    "Json deserialization failed. {columnId}",
                    columnId
                );
                throw new NotJsonTypeException();
            }
        }
        return null;
    }
}
